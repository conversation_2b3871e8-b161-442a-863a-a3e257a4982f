.knowledge-base-container {
  padding: 24px;
  background-color: #f8f9fa;
  min-height: calc(100vh - 64px);
}

.breadcrumb-container {
  margin-bottom: 24px;
}

.page-header {
  margin-bottom: 24px;
  display: none; /* 隐藏原标题 */
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #333333;
  margin: 0;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.table-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  overflow-x: auto;
}

.table-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.table-top-left {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.table-top-right {
  display: flex;
  justify-content: flex-end;
}

.add-button {
  min-width: 120px;
}

.query-button {
  background-color: #a18cd1;
  border-color: #a18cd1;
  min-width: 88px;
}

.query-button:hover {
  background-color: #9370db;
  border-color: #9370db;
}

.reset-button {
  min-width: 88px;
}

:deep(.ant-select-selector) {
  border-radius: 4px !important;
  border: 1px solid #e6e6e6 !important;
  transition: all 0.3s;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: #a18cd1 !important;
  box-shadow: 0 0 0 2px rgba(161, 140, 209, 0.2) !important;
}

:deep(.ant-select:hover .ant-select-selector) {
  border-color: #a18cd1 !important;
}

/* 增加placeholder样式确保其显示 */
:deep(.ant-select-selection-placeholder) {
  color: #bfbfbf !important;
  opacity: 1 !important;
  visibility: visible !important;
}

:deep(.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder) {
  padding-right: 18px !important;
  display: block !important;
}



.file-preview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.file-info {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.file-info h3 {
  font-size: 18px;
  margin-bottom: 16px;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.file-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.file-placeholder p {
  font-size: 16px;
  color: #333333;
  margin: 0;
} 