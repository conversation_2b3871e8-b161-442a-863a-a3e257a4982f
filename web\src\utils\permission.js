import { useUserStore } from '@/store/modules/user'

/**
 * 检查当前用户是否拥有指定权限
 * @param {string|Array} permission 权限标识，可以是字符串或数组
 * @returns {boolean} 是否拥有权限
 */
export function hasPermission(permission) {
  try {
    const userStore = useUserStore()
    const userPermissions = userStore.permissions || []
    
    if (!permission) {
      return true
    }
    
    // 如果是数组，只要有其中一个权限即可
    if (Array.isArray(permission)) {
      return permission.some(p => userPermissions.includes(p))
    }
    
    // 如果是字符串
    return userPermissions.includes(permission)
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

/**
 * 检查当前用户是否拥有所有指定权限
 * @param {Array} permissions 权限标识数组
 * @returns {boolean} 是否拥有所有权限
 */
export function hasAllPermissions(permissions) {
  try {
    const userStore = useUserStore()
    const userPermissions = userStore.permissions || []
    
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return true
    }
    
    return permissions.every(p => userPermissions.includes(p))
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

/**
 * 检查当前用户是否拥有任意一个指定权限
 * @param {Array} permissions 权限标识数组
 * @returns {boolean} 是否拥有任意权限
 */
export function hasAnyPermission(permissions) {
  try {
    const userStore = useUserStore()
    const userPermissions = userStore.permissions || []
    
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return true
    }
    
    return permissions.some(p => userPermissions.includes(p))
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

/**
 * 获取当前用户的所有权限
 * @returns {Array} 权限数组
 */
export function getUserPermissions() {
  try {
    const userStore = useUserStore()
    return userStore.permissions || []
  } catch (error) {
    console.error('获取用户权限失败:', error)
    return []
  }
} 