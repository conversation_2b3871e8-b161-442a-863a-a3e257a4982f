<template>
  <a-drawer
    v-model:visible="visible"
    :title="`文档预览 - ${document?.name || ''}`"
    width="800"
    destroyOnClose
    @close="handleClose"
  >
    <template v-if="document">
      <!-- 文档基本信息区域 -->
      <section class="document-info-section">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="文档名称" :span="2">
            {{ document.name }}
          </a-descriptions-item>
          <a-descriptions-item label="文档大小">
            {{ formatFileSize(document.fileSize) }}
          </a-descriptions-item>
          <a-descriptions-item label="上传时间">
            {{ formatDate(document.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="上传人">
            {{ document.createBy }}
          </a-descriptions-item>
          <a-descriptions-item label="文档类型">
            {{ getFileTypeLabel(document.fileType) }}
          </a-descriptions-item>
          <a-descriptions-item label="所属分类">
            <a-tag color="blue">{{ getCategoryLabel(document.category) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            <a-tag color="green">{{ getPositionLabel(document.position) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="文档描述" :span="2">
            {{ document.remarks || '暂无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </section>
    </template>
    
    <template v-else>
      <a-empty description="未找到文档信息" />
    </template>
    
    <!-- 抽屉底部按钮 -->
    <div class="drawer-footer">
      <a-button @click="handleClose">关闭</a-button>
      <a-space>
        <a-button type="primary" @click="handleDownload">
          <download-outlined />下载
        </a-button>
        <a-button type="primary" @click="handleEdit">
          <edit-outlined />编辑
        </a-button>
      </a-space>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, watch } from 'vue';
import { DownloadOutlined, EditOutlined } from '@ant-design/icons-vue';
import { useDocumentUtils } from '../composables/useDocumentUtils';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  document: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'download', 'edit', 'close']);

const visible = ref(props.visible);

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
  visible.value = newValue;
});

// 监听内部visible变化并更新父组件
watch(visible, (newValue) => {
  emit('update:visible', newValue);
});

// 使用文档工具函数
const { 
  formatFileSize, 
  formatDate, 
  getCategoryLabel, 
  getPositionLabel, 
  getFileTypeLabel 
} = useDocumentUtils();

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  emit('close');
};

// 下载文档
const handleDownload = () => {
  if (props.document) {
    emit('download', props.document);
  }
};

// 编辑文档
const handleEdit = () => {
  if (props.document) {
    emit('edit', props.document);
  }
};
</script>

<style lang="scss" scoped>
.document-info-section {
  margin-bottom: 24px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 16px;
  background-color: #fff;
  text-align: right;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);
}
</style> 