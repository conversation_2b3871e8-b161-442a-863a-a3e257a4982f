/**
 * 表格相关工具函数
 */

/**
 * 处理表格数据变化事件（分页、排序、筛选）
 * @param {Object} pagination 分页对象
 * @param {Object} state 组件状态
 * @param {Function} fetchFunction 获取数据的函数
 */
export const handleTableChange = (pagination, state, fetchFunction) => {
  // 如果pagination已经在BaseTable组件中处理，这里不再需要额外处理
  if (typeof fetchFunction === 'function') {
    fetchFunction();
  }
};

/**
 * 重置搜索表单
 * @param {Object} searchForm 搜索表单
 * @param {Object} pagination 分页对象
 * @param {Function} fetchFunction 获取数据的函数
 */
export const resetSearch = (searchForm, pagination, fetchFunction) => {
  // 重置所有表单字段
  Object.keys(searchForm).forEach(key => {
    if (searchForm[key] !== undefined) {
      searchForm[key] = Array.isArray(searchForm[key]) ? [] : '';
    }
  });
  
  // 重置分页已经在BaseTable组件中处理
  
  // 获取数据
  if (typeof fetchFunction === 'function') {
    fetchFunction();
  }
};

/**
 * 搜索处理函数
 * @param {Object} values 搜索值
 * @param {Object} pagination 分页对象
 * @param {Function} fetchFunction 获取数据的函数
 */
export const handleSearch = (values, pagination, fetchFunction) => {
  // 处理搜索值 - 分页重置已经在BaseTable组件中处理
  
  // 获取数据
  if (typeof fetchFunction === 'function') {
    fetchFunction();
  }
}; 