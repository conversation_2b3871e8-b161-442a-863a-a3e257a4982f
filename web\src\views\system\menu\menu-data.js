export const menuData = [
  {
    id: 1,
    name: '控制台',
    path: '/dashboard',
    icon: 'dashboard',
    parentId: 0,
    order: 1,
    type: 'MENU',
    permission: null,
    component: 'dashboard/index',
    visible: true,
    children: []
  },
  {
    id: 2,
    name: '企业管理',
    path: '/enterprise',
    icon: 'team',
    parentId: 0,
    order: 2,
    type: 'CATALOG',
    permission: null,
    component: null,
    visible: true,
    children: [
      {
        id: 21,
        name: '企业列表',
        path: '/enterprise/list',
        icon: null,
        parentId: 2,
        order: 1,
        type: 'MENU',
        permission: 'enterprise.list',
        component: 'enterprise/list/index',
        visible: true,
        children: []
      },
      {
        id: 22,
        name: '智能体列表',
        path: '/enterprise/agent',
        icon: null,
        parentId: 2,
        order: 2,
        type: 'MENU',
        permission: 'enterprise.agent',
        component: 'enterprise/agent/index',
        visible: true,
        children: []
      },
      {
        id: 23,
        name: '知识库列表',
        path: '/enterprise/knowledge',
        icon: null,
        parentId: 2,
        order: 3,
        type: 'MENU',
        permission: 'enterprise.knowledge',
        component: 'enterprise/knowledge/index',
        visible: true,
        children: []
      }
    ]
  },
  {
    id: 3,
    name: '组织管理',
    path: '/organization',
    icon: 'apartment',
    parentId: 0,
    order: 3,
    type: 'CATALOG',
    permission: null,
    component: null,
    visible: true,
    children: [
      {
        id: 31,
        name: '组织架构',
        path: '/organization/structure',
        icon: null,
        parentId: 3,
        order: 1,
        type: 'MENU',
        permission: 'organization.structure',
        component: 'organization/structure/index',
        visible: true,
        children: []
      },
      {
        id: 32,
        name: '员工管理',
        path: '/organization/employee',
        icon: null,
        parentId: 3,
        order: 2,
        type: 'MENU',
        permission: 'organization.employee',
        component: 'organization/employee/index',
        visible: true,
        children: []
      },
      {
        id: 33,
        name: '员工统计',
        path: '/organization/statistics',
        icon: null,
        parentId: 3,
        order: 3,
        type: 'MENU',
        permission: 'organization.statistics',
        component: 'organization/statistics/index',
        visible: true,
        children: []
      },
      {
        id: 34,
        name: '角色管理',
        path: '/organization/role',
        icon: null,
        parentId: 3,
        order: 4,
        type: 'MENU',
        permission: 'organization.role',
        component: 'organization/role/index',
        visible: true,
        children: []
      },
      {
        id: 35,
        name: '字典管理',
        path: '/organization/dictionary',
        icon: null,
        parentId: 3,
        order: 5,
        type: 'MENU',
        permission: 'organization.dictionary',
        component: 'organization/dictionary/index',
        visible: true,
        children: []
      }
    ]
  },
  {
    id: 8,
    name: '知识库管理',
    path: '/knowledge-base',
    icon: 'book',
    parentId: 0,
    order: 4,
    type: 'MENU',
    permission: 'knowledge.base',
    component: 'knowledge-base/index',
    visible: true,
    children: []
  },
  {
    id: 9,
    name: '练考管理',
    path: '/exam-manage',
    icon: 'form',
    parentId: 0,
    order: 5,
    type: 'MENU',
    permission: 'exam.manage',
    component: 'exam-manage/index',
    visible: true,
    children: []
  },
  {
    id: 4,
    name: '系统管理',
    path: '/system',
    icon: 'setting',
    parentId: 0,
    order: 6,
    type: 'CATALOG',
    permission: null,
    component: null,
    visible: true,
    children: [
      {
        id: 41,
        name: '用户管理',
        path: '/system/user',
        icon: null,
        parentId: 4,
        order: 1,
        type: 'MENU',
        permission: 'system.user',
        component: 'system/user/index',
        visible: true,
        children: []
      },
      {
        id: 42,
        name: '角色管理',
        path: '/system/role',
        icon: null,
        parentId: 4,
        order: 2,
        type: 'MENU',
        permission: 'system.role',
        component: 'system/role/index',
        visible: true,
        children: []
      },
      {
        id: 43,
        name: '菜单管理',
        path: '/system/menu',
        icon: null,
        parentId: 4,
        order: 3,
        type: 'MENU',
        permission: 'system.menu',
        component: 'system/menu/index',
        visible: true,
        children: []
      },
      {
        id: 44,
        name: '系统设置',
        path: '/system/setting',
        icon: null,
        parentId: 4,
        order: 4,
        type: 'MENU',
        permission: 'system.setting',
        component: 'system/setting/index',
        visible: true,
        children: []
      }
    ]
  },
  {
    id: 5,
    name: '岗位管理',
    path: '/position',
    icon: 'idcard',
    parentId: 0,
    order: 7,
    type: 'CATALOG',
    permission: null,
    component: null,
    visible: true,
    children: [
      {
        id: 51,
        name: '岗位等级',
        path: '/position/level',
        icon: null,
        parentId: 5,
        order: 1,
        type: 'MENU',
        permission: 'position.level',
        component: 'position/level/index',
        visible: true,
        children: []
      },
      {
        id: 52,
        name: '岗位管理',
        path: '/position/management',
        icon: null,
        parentId: 5,
        order: 2,
        type: 'MENU',
        permission: 'position.management',
        component: 'position/management/index',
        visible: true,
        children: []
      },
      {
        id: 53,
        name: '晋升配置',
        path: '/position/promotion',
        icon: null,
        parentId: 5,
        order: 3,
        type: 'MENU',
        permission: 'position.promotion',
        component: 'promotion/index',
        visible: true,
        children: []
      }
    ]
  },
  {
    id: 6,
    name: '系统配置',
    path: '/config',
    icon: 'tool',
    parentId: 0,
    order: 8,
    type: 'CATALOG',
    permission: null,
    component: null,
    visible: true,
    children: [
      {
        id: 61,
        name: '信息配置',
        path: '/config/info',
        icon: null,
        parentId: 6,
        order: 1,
        type: 'MENU',
        permission: 'config.info',
        component: 'config/info/index',
        visible: true,
        children: []
      },
      {
        id: 62,
        name: '公告配置',
        path: '/config/announcement',
        icon: null,
        parentId: 6,
        order: 2,
        type: 'MENU',
        permission: 'config.announcement',
        component: 'config/announcement/index',
        visible: true,
        children: []
      }
    ]
  }
]; 