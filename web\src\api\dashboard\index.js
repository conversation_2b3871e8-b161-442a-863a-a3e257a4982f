import request from '@/utils/request';

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  });
}

// 获取练习排行数据
export function getPracticeRanking(params) {
  return request({
    url: '/dashboard/practice-ranking',
    method: 'get',
    params
  });
}

// 获取证书排行榜数据
export function getCertificateRanking(params) {
  return request({
    url: '/dashboard/certificate-ranking',
    method: 'get',
    params
  });
}

// 获取科目练习和考试统计数据
export function getSubjectExamStats() {
  return request({
    url: '/dashboard/subject-exam-stats',
    method: 'get'
  });
}

// 获取考试统计数据
export function getExamStatistics(params) {
  return request({
    url: '/dashboard/exam-statistics',
    method: 'get',
    params
  });
}

// 获取岗位通过率统计数据
export function getPositionPassRates(params) {
  return request({
    url: '/dashboard/position-pass-rates',
    method: 'get',
    params
  });
}
