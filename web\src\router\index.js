import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
    meta: { title: '登录', hideInMenu: true }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('../layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { title: '控制台', icon: 'dashboard' }
      },
      {
        path: 'enterprise',
        name: 'Enterprise',
        component: () => import('../views/enterprise/index.vue'),
        meta: { title: '企业管理', icon: 'team' },
        redirect: '/enterprise/list',
        children: [
          {
            path: 'list',
            name: 'EnterpriseList',
            component: () => import('../views/enterprise/list/index.vue'),
            meta: { title: '企业列表' }
          },
          {
            path: 'agent',
            name: 'AgentList',
            component: () => import('../views/enterprise/agent/index.vue'),
            meta: { title: '智能体列表' }
          },
          {
            path: 'knowledge',
            name: 'KnowledgeList',
            component: () => import('../views/enterprise/knowledge/index.vue'),
            meta: { title: '知识库列表' }
          }
        ]
      },
      {
        path: 'organization',
        name: 'Organization',
        component: () => import('../views/organization/index.vue'),
        meta: { title: '组织管理', icon: 'apartment' },
        redirect: '/organization/structure',
        children: [
          {
            path: 'structure',
            name: 'OrgStructure',
            component: () => import('../views/organization/structure/index.vue'),
            meta: { title: '组织架构', permission: ['organization.structure'] }
          },
          {
            path: 'employee',
            name: 'OrgEmployee',
            component: () => import('../views/organization/employee/index.vue'),
            meta: { title: '员工管理', permission: ['organization.employee'] }
          },
          {
            path: 'statistics',
            name: 'OrgStatistics',
            component: () => import('../views/organization/statistics/index.vue'),
            meta: { title: '员工统计', permission: ['organization.statistics'] }
          },
          {
            path: 'role',
            name: 'OrgRole',
            component: () => import('../views/organization/role/index.vue'),
            meta: { title: '角色管理', permission: ['organization.role'] }
          },
          {
            path: 'employee-application',
            name: 'EmployeeApplication',
            component: () => import('../views/organization/employee/application.vue'),
            meta: { title: '员工审核', permission: ['organization.employee.application'] }
          }
        ]
      },
      {
        path: 'knowledge-base',
        name: 'KnowledgeBase',
        component: () => import('../views/knowledge-base/index.vue'),
        meta: { title: '知识库管理', permission: ['knowledge.base'] }
      },
      {
        path: 'enterprise-knowledge-base',
        name: 'EnterpriseKnowledgeBase',
        component: () => import('../views/enterprise-knowledge-base/index.vue'),
        meta: { title: '企业知识库', permission: ['enterprise.knowledge.base'] }
      },
      {
        path: 'kb-questions',
        name: 'KBQuestions',
        component: () => import('../views/kb-questions/index.vue'),
        meta: { title: '题目管理', icon: 'question-circle', permission: ['knowledge.question'] }
      },
      {
        path: 'kb-questions/:id',
        name: 'KBQuestionsByKnowledgeBase',
        component: () => import('../views/kb-questions/index.vue'),
        meta: { title: '题目管理', hideInMenu: true, permission: ['knowledge.question'] }
      },
      {
        path: 'exam-manage',
        name: 'ExamManage',
        component: () => import('../views/exam-manage/index.vue'),
        meta: { title: '练考管理', icon: 'read', permission: ['exam.manage'] },
        redirect: '/exam-manage/config',
        children: [
          {
            path: 'config',
            name: 'ExamConfig',
            component: () => import('../views/exam-manage/config/index.vue'),
            meta: { title: '练考配置', permission: ['exam.manage.config'] }
          },
          {
            path: 'review',
            name: 'ExamReview',
            component: () => import('../views/exam-manage/review/index.vue'),
            meta: { title: '考试审核', permission: ['exam.manage.review'] }
          },
          {
            path: 'practice',
            name: 'PracticeRecord',
            component: () => import('../views/exam-manage/practice/index.vue'),
            meta: { title: '练习记录', permission: ['exam.manage.practice'] }
          },
          {
            path: 'exam',
            name: 'ExamRecord',
            component: () => import('../views/exam-manage/exam/index.vue'),
            meta: { title: '考试记录', permission: ['exam.manage.exam'] }
          },
          {
            path: 'certificate',
            name: 'CertificateRecord',
            component: () => import('../views/exam-manage/certificate/index.vue'),
            meta: { title: '证书记录', permission: ['exam.manage.certificate'] }
          }
        ]
      },
      {
        path: 'catering-examiner',
        name: 'CateringExaminer',
        component: () => import('../views/catering-examiner/index.vue'),
        meta: { title: '餐考师', icon: 'customer-service', permission: ['catering.examiner'] },
        redirect: '/catering-examiner/front-hall',
        children: [
          {
            path: 'front-hall',
            name: 'FrontHallExaminer',
            component: () => import('../views/catering-examiner/front-hall/index.vue'),
            meta: { title: '前厅餐考师', permission: ['catering.examiner.front'] }
          },
         
        ]
      },
      {
        path: 'feedback',
        name: 'Feedback',
        component: () => import('../views/feedback/index.vue'),
        meta: { title: '意见反馈', icon: 'message', permission: ['feedback'] }
      },
      {
        path: 'system',
        name: 'System',
        component: () => import('../views/system/index.vue'),
        meta: { title: '系统管理', icon: 'setting' },
        redirect: '/system/user',
        children: [
          {
            path: 'user',
            name: 'SystemUser',
            component: () => import('../views/system/user/index.vue'),
            meta: { title: '用户管理', permission: ['system.user'] }
          },
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('../views/system/profile/index.vue'),
            meta: { title: '个人资料', hideInMenu: true }
          },
          {
            path: 'role',
            name: 'SystemRole',
            component: () => import('../views/system/role/index.vue'),
            meta: { title: '角色管理', permission: ['system.role'] }
          },
          {
            path: 'menu',
            name: 'SystemMenu',
            component: () => import('../views/system/menu/index.vue'),
            meta: { title: '菜单管理', permission: ['system.menu'] }
          },
          {
            path: 'setting',
            name: 'SystemSetting',
            component: () => import('../views/system/setting/index.vue'),
            meta: { title: '系统设置', permission: ['system.setting'] }
          },
          {
            path: 'dictionary',
            name: 'SystemDictionary',
            component: () => import('../views/organization/dictionary/index.vue'),
            meta: { title: '字典管理', permission: ['organization.dictionary'] }
          }
        ]
      },
      {
        path: 'position',
        name: 'Position',
        component: () => import('../views/position/index.vue'),
        meta: { title: '岗位管理', icon: 'profile' },
        redirect: '/position/level',
        children: [
          {
            path: 'level',
            name: 'PositionLevel',
            component: () => import('../views/position/level/index.vue'),
            meta: { title: '岗位等级', permission: ['position.level'] }
          },
          {
            path: 'hierarchy',
            name: 'PositionHierarchy',
            component: () => import('../views/position/hierarchy/index.vue'),
            meta: { title: '岗位类型名称', permission: ['position.hierarchy'] }
          },
          {
            path: 'management',
            name: 'PositionManagement',
            component: () => import('../views/position/management/index.vue'),
            meta: { title: '岗位管理', permission: ['position.management'] }
          },
          {
            path: 'promotion',
            name: 'PositionPromotion',
            component: () => import('../views/promotion/index.vue'),
            meta: { title: '晋升配置', permission: ['position.promotion'] }
          }
        ]
      },
      {
        path: 'config',
        name: 'Config',
        component: () => import('../views/config/index.vue'),
        meta: { title: '系统配置', icon: 'tool' },
        redirect: '/config/info',
        children: [
          {
            path: 'info',
            name: 'ConfigInfo',
            component: () => import('../views/config/info/index.vue'),
            meta: { title: '信息配置', permission: ['config.info'] }
          },
          {
            path: 'announcement',
            name: 'ConfigAnnouncement',
            component: () => import('../views/config/announcement/index.vue'),
            meta: { title: '公告配置', permission: ['config.announcement'] }
          },
          {
            path: 'restaurant',
            name: 'ConfigRestaurant',
            component: () => import('../views/config/restaurant/index.vue'),
            meta: { title: '餐烤师配置', permission: ['config.restaurant'] }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../views/error/404.vue'),
    meta: { title: '404', hideInMenu: true }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 餐烤餐考后台管理系统` : '餐烤餐考后台管理系统';
  
  // 检查是否已登录
  const token = localStorage.getItem('token');
  
  if (to.path === '/login') {
    if (token) {
      next('/');
    } else {
      next();
    }
  } else {
    if (!token) {
      next('/login');
    } else {
      try {
        // 权限检查
        const userStore = await import('@/store/modules/user').then(module => module.useUserStore());
        
        // 如果用户信息或菜单未加载，先获取用户信息和菜单权限
        if (!userStore.userInfo || !Array.isArray(userStore.userMenus) || userStore.userMenus.length === 0) {
          try {
            // 先获取用户信息
            const userResult = await userStore.getCurrentUserInfo();
            if (!userResult.success) {
              console.error('获取用户信息失败:', userResult.error);
              next('/login');
              return;
            }
            
            // 再获取用户菜单权限
            await userStore.getUserMenus();
          } catch (error) {
            console.error('获取用户信息或菜单权限出错:', error);
            next('/login');
            return;
          }
        }
        
        // 检查用户是否有访问该路由的权限
        const hasPermission = checkPermission(to, userStore.permissions);
        
        if (hasPermission) {
          next();
        } else {
          next('/404');
        }
      } catch (error) {
        console.error('路由守卫错误:', error);
        next('/login');
      }
    }
  }
});

// 检查权限
function checkPermission(route, permissions) {
  // 确保permissions是数组
  if (!Array.isArray(permissions)) {
    permissions = [];
  }
  
  // 没有配置权限要求的路由，直接放行
  if (!route.meta || !route.meta.permission) {
    return true;
  }
  
  // 如果是需要验证权限的页面，则验证权限
  const requiredPermissions = route.meta.permission;
  if (Array.isArray(requiredPermissions)) {
    return requiredPermissions.some(permission => permissions.includes(permission));
  } else {
    return permissions.includes(requiredPermissions);
  }
}

export default router; 