<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchParams"
          :items="formItems"
          @search="handleSearch"
          @reset="handleReset"
        />
      </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          添加餐烤师
        </a-button>
      </template>
    </page-header>

    <!-- 使用 base-table 替换原 a-table -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      :show-default-action="false"
      rowKey="id"
    >
      <!-- 添加自定义操作列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'avatar'">
          <a-avatar :src="getFullImageUrl(record.avatar)" size="large">
            <template #icon><UserOutlined /></template>
          </a-avatar>
        </template>
        <template v-if="column.key === 'positionBelongId'">
          <span>{{ record.positionType?.name || '未分配' }}</span>
        </template>
        <template v-if="column.key === 'createdAt'">
          <span>{{ formatDate(record.createdAt) }}</span>
        </template>
        <!-- 修改操作列模板 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" class="action-button edit-button" @click="handleEdit(record)">
              <edit-outlined />编辑
            </a-button>
            <a-popconfirm
              title="确定要删除这个餐烤师配置吗？"
              @confirm="handleDelete(record.id)"
            >
              <a-button type="link" class="action-button delete-button">
                <delete-outlined />删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </base-table>

    <!-- 添加/编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalSubmit"
      @cancel="handleModalCancel"
      :confirmLoading="confirmLoading"
      width="600px"
      class="restaurant-modal"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="餐烤师名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入餐烤师名称" />
        </a-form-item>

        <a-form-item label="岗位分类" name="positionBelongId">
          <a-select
            v-model:value="formData.positionBelongId"
            placeholder="请选择岗位分类"
            :options="positionTypeOptions"
            :fieldNames="{ label: 'name', value: 'id' }"
          />
        </a-form-item>

        <a-form-item label="打招呼消息" name="initMessage">
          <a-textarea 
            v-model:value="formData.initMessage" 
            placeholder="请输入打招呼消息" 
            :auto-size="{ minRows: 2, maxRows: 6 }" 
          />
        </a-form-item>

        <a-form-item label="头像上传" name="avatar">
          <a-upload
            v-model:file-list="avatarFileList"
            list-type="picture-card"
            :show-upload-list="false"
            :customRequest="handleAvatarUpload"
            :before-upload="beforeImageUpload"
          >
            <div v-if="formData.avatar" class="image-container">
              <img :src="getFullImageUrl(formData.avatar)" alt="餐烤师头像" class="uploaded-image" />
            </div>
            <div v-else>
              <PlusOutlined />
              <div style="margin-top: 8px">上传头像</div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  getRestaurantConfigList,
  getRestaurantConfigDetail,
  createRestaurantConfig,
  updateRestaurantConfig,
  deleteRestaurantConfig,
  uploadAvatar,
  getPositionTypeList
} from '@/api/config/restaurant';
import {
  SearchOutlined,
  PlusOutlined,
  UserOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';

// 基础URL配置
const VITE_APP_API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || '';
const VITE_API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

// 表格数据和加载状态
const tableData = ref([]);
const loading = ref(false);
const searchParams = reactive({
  name: '',
  page: 1,
  pageSize: 10
});

// 岗位类型选项
const positionTypeOptions = ref([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
});

// 表格列定义
const columns = [
  {
    title: '头像',
    key: 'avatar',
    width: 60,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '岗位分类',
    key: 'positionBelongId',
    dataIndex: 'positionBelongId',
    width: 120
  },
  {
    title: '打招呼消息',
    key: 'initMessage',
    dataIndex: 'initMessage',
    width: 160,
    ellipsis: true
  },
  {
    title: '创建时间',
    key: 'createdAt',
    dataIndex: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
];

// 模态框相关状态
const modalVisible = ref(false);
const confirmLoading = ref(false);
const formRef = ref(null);
const isEdit = ref(false);
const formData = reactive({
  id: null,
  name: '',
  positionBelongId: undefined,
  avatar: '',
  initMessage: ''
});
const avatarFileList = ref([]);

// 计算属性：模态框标题
const modalTitle = computed(() => isEdit.value ? '编辑餐烤师' : '添加餐烤师');

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入餐烤师名称', trigger: 'blur' },
    { max: 50, message: '名称长度不能超过50个字符', trigger: 'blur' }
  ],
  positionBelongId: [
    { required: true, message: '请选择岗位分类', trigger: 'change' }
  ]
};

// 获取完整的图片URL
const getFullImageUrl = (url) => {
  if (!url) return '';
  // 如果已经是完整URL则直接返回
  if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
    return url;
  }
  let baseUrl = VITE_APP_API_BASE_URL.replace(VITE_API_BASE_URL, '');
  console.log('baseUrl',baseUrl);
  // 拼接基础URL和相对路径
  return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取餐烤师配置列表
const fetchRestaurantConfigList = async () => {
  try {
    loading.value = true;
    const res = await getRestaurantConfigList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      name: searchParams.name
    });

    if (res ) {
      tableData.value = res.items || [];
      pagination.total = res.total || 0;
    } else {
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取餐烤师配置列表失败:', error);
    message.error('获取餐烤师配置列表失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 获取岗位类型列表
const fetchPositionTypeList = async () => {
  try {
    const res = await getPositionTypeList();
    console.log('岗位类型列表返回数据:', res);

    if (res ) {
      positionTypeOptions.value = res || [];
    } else {
      positionTypeOptions.value = [];
    }
  } catch (error) {
    console.error('获取岗位类型列表失败:', error);
    message.error('获取岗位类型列表失败: ' + (error.message || '未知错误'));
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchRestaurantConfigList();
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchRestaurantConfigList();
};

// 添加餐烤师
const handleAdd = () => {
  isEdit.value = false;
  formData.id = null;
  formData.name = '';
  formData.positionBelongId = undefined;
  formData.avatar = '';
  formData.initMessage = '';
  avatarFileList.value = [];
  modalVisible.value = true;
};

// 编辑餐烤师
const handleEdit = async (record) => {
  try {
    loading.value = true;
    isEdit.value = true;

    // 获取详细信息
    const res = await getRestaurantConfigDetail(record.id);

    if (res ) {
      const detail = res;
      formData.id = detail.id;
      formData.name = detail.name;
      formData.positionBelongId = detail.positionBelongId;
      formData.avatar = detail.avatar || '';
      formData.initMessage = detail.initMessage || '';

      // 设置头像文件列表
      if (detail.avatar) {
        avatarFileList.value = [
          {
            uid: '-1',
            name: 'avatar.png',
            status: 'done',
            url: getFullImageUrl(detail.avatar)
          }
        ];
      } else {
        avatarFileList.value = [];
      }
    }

    modalVisible.value = true;
  } catch (error) {
    console.error('获取餐烤师详情失败:', error);
    message.error('获取餐烤师详情失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 删除餐烤师
const handleDelete = async (id) => {
  try {
    loading.value = true;
    const res = await deleteRestaurantConfig(id);
    console.log('删除餐烤师返回数据:', res);
    if (res) {
      message.success('餐烤师删除成功');
      fetchRestaurantConfigList();
    }
  } catch (error) {
    console.error('删除餐烤师失败:', error);
    message.error('删除餐烤师失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片必须小于 2MB!');
    return false;
  }
  return isJpgOrPng && isLt2M;
};

// 头像上传处理
const handleAvatarUpload = async ({ file, onSuccess, onError }) => {
  try {
    loading.value = true;
    const res = await uploadAvatar(file);
    console.log('头像上传返回数据:', res);

    // 从响应中正确提取文件URL
    if (res  && res.fileUrl) {
      // 保存后端返回的相对路径
      formData.avatar = res.fileUrl;

      // 获取完整URL用于显示
      const fullImageUrl = getFullImageUrl(res.fileUrl);
      console.log('完整图片URL:', fullImageUrl);

      // 更新文件列表
      avatarFileList.value = [
        {
          uid: '-1',
          name: file.name || 'avatar.png',
          status: 'done',
          url: fullImageUrl
        }
      ];

      onSuccess(res, file);
      message.success('头像上传成功');
    } else {
      console.error('上传响应结构不正确:', res);
      onError(new Error('上传失败'));
      message.error('头像上传失败');
    }
  } catch (error) {
    console.error('上传过程发生错误:', error);
    onError(error);
    message.error(`头像上传失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 模态框提交
const handleModalSubmit = async () => {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;

    // 准备提交的数据
    const submitData = {
      name: formData.name,
      positionBelongId: formData.positionBelongId,
      avatar: formData.avatar,
      initMessage: formData.initMessage
    };

    if (isEdit.value) {
      // 更新操作
      const res = await updateRestaurantConfig(formData.id, submitData);
      if (res) {
        message.success('餐烤师更新成功');
      }
    } else {
      // 创建操作
      const res = await createRestaurantConfig(submitData);
      if (res) {
        message.success('餐烤师添加成功');
      }
    }

    // 关闭模态框，刷新列表
    modalVisible.value = false;
    fetchRestaurantConfigList();
  } catch (error) {
    console.error('保存餐烤师失败:', error);
    message.error('保存餐烤师失败: ' + (error.message || '未知错误'));
  } finally {
    confirmLoading.value = false;
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
  formRef.value.resetFields();
};

// 初始化
onMounted(() => {
  fetchRestaurantConfigList();
  fetchPositionTypeList();
});
</script>

<style scoped>
.page-container {
  width: 100%;
}

/* 弹窗样式优化 */
.restaurant-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
    padding: 16px 24px;
    border-bottom: none;
  }

  :deep(.ant-modal-title) {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
  }

  :deep(.ant-modal-close) {
    color: #fff;
  }
}

/* 自定义按钮样式 */
.custom-button {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border: none;
  color: #fff;

  &:hover {
    background: linear-gradient(135deg, #8a65c9 0%, #f8a5d1 100%);
    color: #fff;
  }
}

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}

/* 表格操作按钮样式 */
.action-button {
  padding: 4px 8px;
  height: 28px;
  border-radius: 4px;

  :deep(.anticon) {
    margin-right: 4px;
  }
}

.edit-button {
  color: #a18cd1;

  &:hover {
    color: #8a65c9;
    background: rgba(161, 140, 209, 0.1);
  }
}

.delete-button {
  color: #ff4d4f;

  &:hover {
    color: #ff7875;
    background: rgba(255, 77, 79, 0.1);
  }
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;

  .ant-table-thead > tr > th {
    background: #f8f9fc;
    font-weight: 500;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }

  .ant-table-row:hover {
    .action-button {
      opacity: 1;
    }
  }


}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

:deep(.ant-upload.ant-upload-select-picture-card) {
  width: 100px;
  height: 100px;
}
</style>
