/**
 * 统一页面根容器类名脚本
 * 本脚本会将所有Vue文件的根容器类名修改为page-container
 */

const fs = require('fs');
const path = require('path');

// 记录处理结果
const processed = [];
const errors = [];

// 递归获取所有Vue文件
function getAllVueFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // 过滤掉components和composables目录
      if (!filePath.includes('components') && !filePath.includes('composables')) {
        getAllVueFiles(filePath, arrayOfFiles);
      }
    } else if (path.extname(file) === '.vue') {
      arrayOfFiles.push(filePath);
    }
  });

  return arrayOfFiles;
}

// 处理单个Vue文件
function processFile(filePath) {
  console.log(`处理文件: ${filePath}`);
  
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否是标准页面组件
    const regex = /<template>\s*<div class="([^"]*-container[^"]*)"/;
    const match = content.match(regex);
    
    if (match) {
      const oldClassName = match[1];
      
      // 如果已经是page-container或包含page-container，则跳过
      if (oldClassName === 'page-container' || oldClassName.includes('page-container')) {
        console.log(`  已经使用page-container，跳过`);
        return;
      }
      
      // 提取前缀名称
      const moduleName = oldClassName.replace('-container', '');
      
      // 替换根容器类名
      const newClassName = `page-container ${moduleName}-page`;
      const newContent = content.replace(`<div class="${oldClassName}"`, `<div class="${newClassName}"`);
      
      // 重新匹配验证是否已替换
      const newMatch = newContent.match(/<template>\s*<div class="([^"]*)"/);
      if (newMatch && newMatch[1] === newClassName) {
        // 写入文件
        fs.writeFileSync(filePath, newContent);
        processed.push(`${filePath}: ${oldClassName} -> ${newClassName}`);
        console.log(`  成功: ${oldClassName} -> ${newClassName}`);
      } else {
        errors.push(`${filePath}: 替换失败，无法匹配新类名`);
        console.log(`  失败: 替换失败，无法匹配新类名`);
      }
    } else {
      console.log(`  不是标准页面组件或无需修改`);
    }
  } catch (error) {
    errors.push(`${filePath}: ${error.message}`);
    console.log(`  失败: ${error.message}`);
  }
}

// 主函数
function main() {
  const viewsDir = path.join(__dirname, '..', 'src', 'views');
  const vueFiles = getAllVueFiles(viewsDir);
  
  console.log(`找到 ${vueFiles.length} 个Vue文件，开始处理...\n`);
  
  vueFiles.forEach(processFile);
  
  console.log(`\n处理完成! 共修改 ${processed.length} 个文件`);
  
  if (processed.length > 0) {
    console.log(`\n已修改的文件:`);
    processed.forEach(p => console.log(`- ${p}`));
  }
  
  if (errors.length > 0) {
    console.log(`\n处理失败的文件:`);
    errors.forEach(e => console.log(`- ${e}`));
  }
  
  console.log(`\n注意: 此脚本只修改了根容器类名，您还需要手动处理以下工作:`);
  console.log(`1. 检查并移除各页面中已经挪到全局global.scss的重复样式`);
  console.log(`2. 保留页面特有的样式`);
  console.log(`3. 修复可能的类型断言相关问题`);
  console.log(`4. 检查页面渲染是否正常`);
}

// 执行主函数
main(); 