<template>
  <div class="page-container">

    <!-- 表单区域 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Banner主标题" name="mainTitle">
              <a-input v-model:value="formData.mainTitle" placeholder="请输入主标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Banner副标题" name="subTitle">
              <a-input v-model:value="formData.subTitle" placeholder="请输入副标题" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <!-- Logo上传已隐藏 -->
          <!-- <a-col :span="8">
            <a-form-item label="Logo上传" name="logo">
              <a-upload
                v-model:file-list="logoFileList"
                list-type="picture-card"
                :show-upload-list="false"
                :customRequest="(options) => handleImageUpload(options, 'logo')"
                :before-upload="beforeImageUpload"
              >
                <div v-if="formData.logo" class="image-container">
                  <img :src="formData.logo" alt="logo" class="uploaded-image" />
                </div>
                <div v-else>
                  <plus-outlined />
                  <div style="margin-top: 8px">上传Logo</div>
                  <div style="font-size: 12px; color: #999">(建议尺寸200×200px)</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col> -->

          <a-col :span="8">
            <a-form-item label="企业Logo上传" name="enterpriseLogo">
              <a-upload
                v-model:file-list="enterpriseLogoFileList"
                list-type="picture-card"
                :show-upload-list="false"
                :customRequest="(options) => handleImageUpload(options, 'enterpriseLogo')"
                :before-upload="beforeImageUpload"
              >
                <div v-if="formData.enterpriseLogo" class="image-container">
                  <img :src="formData.enterpriseLogo" alt="enterprise logo" class="uploaded-image" />
                </div>
                <div v-else>
                  <plus-outlined />
                  <div style="margin-top: 8px">上传企业Logo</div>
                  <div style="font-size: 12px; color: #999">(建议尺寸200×200px)</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>



        <div class="form-actions">
          <a-button type="primary" @click="handleSubmit" :loading="loading">
            保存配置
          </a-button>

        </div>
      </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Upload } from 'ant-design-vue';
import { getInfoConfig, updateInfoConfig, uploadImage } from '@/api/config/info';

// 基础URL配置
const VITE_APP_API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || '';
const VITE_API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

// 数据与状态
const loading = ref(false);
const formRef = ref(null);
const formData = reactive({
  id: null,
  mainTitle: '',
  subTitle: '',
  logo: '',
  enterpriseLogo: '',
  enterpriseId: '',
  createTime: '',
  updateTime: '',
  isDefault: 0
});

// 上传相关
const logoFileList = ref([]);
const enterpriseLogoFileList = ref([]);


// 表单校验规则
const formRules = {
  mainTitle: [
    { required: true, message: '请输入Banner主标题', trigger: 'blur' },
    { max: 30, message: '主标题长度不能超过30个字符', trigger: 'blur' }
  ],
  subTitle: [
    { required: true, message: '请输入Banner副标题', trigger: 'blur' },
    { max: 50, message: '副标题长度不能超过50个字符', trigger: 'blur' }
  ],
  // logo: [
  //   { required: true, message: '请上传Logo', trigger: 'change' }
  // ],
  enterpriseLogo: [
    { required: true, message: '请上传企业Logo', trigger: 'change' }
  ],
};

// 格式化显示名称
const formatDisplayName = (name) => {
  if (!name) return '';
  if (name.length > 3) {
    return name.slice(-2);
  }
  return name;
};

// 获取信息配置
const fetchInfoConfig = async () => {
  try {
    loading.value = true;
    const res = await getInfoConfig();

    const config = res;
    if(!config) return

    // 将API返回的数据映射到表单
    Object.assign(formData, {
      id: config.id,
      mainTitle: config.mainTitle,
      subTitle: config.subTitle,
      logo: config.logo ? getFullImageUrl(config.logo) : '',
      enterpriseLogo: config.enterpriseLogo ? getFullImageUrl(config.enterpriseLogo) : '',
      enterpriseId: config.enterpriseId,
      createTime: config.createTime,
      updateTime: config.updateTime,
      isDefault: config.isDefault
    });

    // 设置文件列表
    if (config.logo) {
      logoFileList.value = [
        {
          uid: '-1',
          name: 'logo.png',
          status: 'done',
          url: getFullImageUrl(config.logo),
        },
      ];
    }

    if (config.enterpriseLogo) {
      enterpriseLogoFileList.value = [
        {
          uid: '-1',
          name: 'enterprise-logo.png',
          status: 'done',
          url: getFullImageUrl(config.enterpriseLogo),
        },
      ];
    }
  } catch (error) {
    console.error('获取配置信息出错:', error);
    message.error('获取配置信息出错: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 初始化表单数据
const initFormData = () => {
  fetchInfoConfig();
};

// 重置表单数据
const handleReset = () => {
  initFormData();
  message.info('表单已重置');
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    // 准备提交的数据
    const submitData = {
      id: formData.id,
      mainTitle: formData.mainTitle,
      subTitle: formData.subTitle,
      logo: removeBaseUrl(formData.logo),
      enterpriseLogo: removeBaseUrl(formData.enterpriseLogo),
      enterpriseId: formData.enterpriseId,
      createTime: formData.createTime,
      updateTime: formData.updateTime,
      isDefault: formData.isDefault
    };

    console.log('提交数据:', submitData);

    // 发送更新请求
    const res = await updateInfoConfig(submitData);
    console.log(res)
    message.success('配置保存成功');
    // 重新获取最新数据
    fetchInfoConfig();

  } catch (error) {
    console.error('保存配置出错:', error);
    message.error('保存配置失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 通用图片校验
const beforeImageUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
    return Upload.LIST_IGNORE;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片必须小于 2MB!');
    return Upload.LIST_IGNORE;
  }
  return true;
};

// Logo上传前检查
const beforeLogoUpload = (file) => {
  return beforeImageUpload(file);
};

// 获取完整的图片URL
const getFullImageUrl = (url) => {
  if (!url) return '';
  // 如果已经是完整URL则直接返回
  if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
    return url;
  }
  let baseUrl = VITE_APP_API_BASE_URL.replace(VITE_API_BASE_URL, '');
  console.log('baseUrl',baseUrl);
  // 拼接基础URL和相对路径
  return `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
};


// 通用图片上传处理函数
const handleImageUpload = async ({ file, onSuccess, onError }, type) => {
  try {
    loading.value = true;
    const res = await uploadImage(file);
    console.log('上传返回结果:', res);

    // 定义类型配置
    const typeConfig = {
      logo: {
        fieldName: 'logo',
        fileList: logoFileList,
        fileName: 'logo.png',
        successMsg: 'Logo上传成功',
        errorMsg: 'Logo上传失败'
      },
      enterpriseLogo: {
        fieldName: 'enterpriseLogo',
        fileList: enterpriseLogoFileList,
        fileName: '企业logo.png',
        successMsg: '企业Logo上传成功',
        errorMsg: '企业Logo上传失败'
      },
    };

    const config = typeConfig[type];

    if (res && res.fileUrl) {
      console.log('文件路径:', res.fileUrl);

      // 保存后端返回的相对路径，用于后续提交
      const relativeUrl = res.fileUrl;

      // 获取完整的图片URL用于显示
      const fullImageUrl = getFullImageUrl(relativeUrl);
      console.log('完整图片URL:', fullImageUrl);

      // 更新表单数据为完整URL路径，保证立即可以看到图片
      formData[config.fieldName] = fullImageUrl;

      // 使用完整URL更新文件列表(用于前端显示)
      config.fileList.value = [{
        uid: '-1',
        name: file.name || config.fileName,
        status: 'done',
        url: fullImageUrl,
      }];

      onSuccess(res, file);
      message.success(config.successMsg);
    } else {
      onError(new Error('上传失败'));
      message.error(config.errorMsg);
    }
  } catch (error) {
    onError(error);
    message.error(`图片上传失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 从URL中移除基础路径，用于提交相对路径
const removeBaseUrl = (url) => {
  if (!url) return '';
  // 如果是data:开头的base64数据，直接返回
  if (url.startsWith('data:')) return url;

  let baseUrl = VITE_APP_API_BASE_URL.replace(VITE_API_BASE_URL, '');

  // 如果URL包含基础路径，则移除
  if (url.startsWith(baseUrl)) {
    return url.substring(baseUrl.length);
  }

  // 如果URL包含完整的BASE_API_URL，则移除
  if (url.startsWith(VITE_APP_API_BASE_URL)) {
    return url.substring(VITE_APP_API_BASE_URL.length);
  }

  return url;
};

// 页面加载时初始化数据
onMounted(() => {
  initFormData();
});
</script>

<style scoped>

.page-container {
  overflow-y:auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.page-title h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

.page-title p {
  color: #666;
  margin: 8px 0 0;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  padding: 24px;
}

.name-note {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.5;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* 图片容器样式 */
.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 上传后的图片样式 */
.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持原始比例，确保整个图片可见 */
  max-width: 100%;
  max-height: 100%;
}

/* 自定义样式覆盖 */
:deep(.ant-upload-select-picture-card) {
  width: 100%;
  height: 120px;
}

:deep(.ant-upload-picture-card-wrapper) {
  width: 100%;
}

/* 应用主题渐变背景到按钮 */
.form-actions .ant-btn-primary {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-color: transparent;
  transition: all 0.3s;
}

.form-actions .ant-btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(161, 140, 209, 0.3);
}
</style>
