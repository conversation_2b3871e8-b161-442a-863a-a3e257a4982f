<template>
  <div class="page-container">
      <div class="org-structure-layout">
        <!-- 左侧部门树 -->
        <div class="department-tree-container">
          <div class="tree-header">
            <h3>组织架构</h3>
            <a-button type="primary" class="primary-button" @click="handleAddDepartment">
              新增部门
            </a-button>
          </div>
          <div class="tree-content">
            <a-tree
              :tree-data="departmentTreeData"
              :defaultExpandAll="true"
              :blockNode="true"
              :selectedKeys="selectedDepartmentKeys"
              @select="handleDepartmentSelect"
              :fieldNames="{ key: 'id', title: 'name' }"
            >
              <template #title="{ name, id }">
                <div class="tree-node-title">
                  <span>{{ name }}</span>
                  <div class="tree-node-actions">
                    <edit-outlined
                      class="edit-icon"
                      @click.stop="handleEditDepartment({ id, name })"
                    />
                    <delete-outlined
                      class="delete-icon"
                      @click.stop="handleDeleteDepartment({ id, name })"
                    />
                  </div>
                </div>
              </template>
            </a-tree>
          </div>
        </div>

        <!-- <h3>{{ currentDepartment ? currentDepartment.name : '全部' }}员工</h3> -->
        <div class="employee-table-container">
          <page-header>
            <template #search>
              <search-form-card
                :model-value="searchForm"
                :items="searchFormItems"
                @search="handleSearch"
                @reset="resetSearch"
              />
            </template>
            <template #actions>
              <a-space :size="16">
                <a-button type="primary" class="primary-button" @click="handleAddEmployee">
                    新增员工
                  </a-button>
                <a-button class="primary-button" @click="handleImport">
                    导入
                  </a-button>
              </a-space>
            </template>
          </page-header>


          <!-- 员工表格 -->
          <base-table
            :columns="employeeColumns"
            :data-source="employeeDataSource"
            :loading="employeeLoading"
            :pagination="employeePagination"
            @change="handleEmployeeTableChange"
            rowKey="id"
            @edit="handleEditEmployee"
            @delete="handleDeleteEmployee"
            :action-config="actionConfig"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'gender'">
                {{ record.gender === 'male' ? '男' : '女' }}
              </template>
              <template v-if="column.key === 'status'">
                <a-badge :status="record.status === '1' ? 'success' : 'error'" :text="record.status === '1' ? '在职' : '离职'" />
              </template>
              <template v-if="column.key === 'isActivated'">
                <a-badge :status="record.isActivated ? 'success' : 'error'" :text="record.isActivated ? '已认证' : '未认证'" />
              </template>
              <template v-if="column.key === 'position'">
                {{ getPositionName(record.positionId) }}
              </template>
            </template>
          </base-table>
        </div>
      </div>

    <!-- 新增/编辑部门弹窗 -->
    <a-modal
      v-model:visible="departmentModalVisible"
      :title="departmentModalTitle"
      @ok="handleDepartmentModalOk"
      @cancel="handleDepartmentModalCancel"
    >
      <a-form :model="departmentFormState" :rules="departmentFormRules" ref="departmentFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="部门名称" name="name">
          <a-input v-model:value="departmentFormState.name" placeholder="请输入部门名称" />
        </a-form-item>
        <a-form-item label="上级部门" name="parentId">
          <a-tree-select
            v-model:value="departmentFormState.parentId"
            :tree-data="departmentOptions"
            placeholder="请选择上级部门"
            tree-default-expand-all
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          />
        </a-form-item>
        <a-form-item label="排序号" name="orderNum">
          <a-input-number v-model:value="departmentFormState.orderNum" placeholder="请输入排序号" style="width: 100%" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="departmentFormState.status">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="departmentFormState.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 新增/编辑员工弹窗 -->
    <a-modal
      v-model:visible="employeeModalVisible"
      :title="employeeModalTitle"
      @ok="handleEmployeeModalOk"
      @cancel="handleEmployeeModalCancel"
      width="700px"
    >
      <a-form :model="employeeFormState" :rules="employeeFormRules" ref="employeeFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" name="name">
              <a-input v-model:value="employeeFormState.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="employeeFormState.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="归属部门" name="departmentId">
              <a-tree-select
                v-model:value="employeeFormState.departmentId"
                :tree-data="departmentOptions"
                placeholder="请选择部门"
                tree-default-expand-all
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="gender">
              <a-radio-group v-model:value="employeeFormState.gender">
                <a-radio value="male">男</a-radio>
                <a-radio value="female">女</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="身份证号" name="idCard">
              <a-input v-model:value="employeeFormState.idCard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="入职时间" name="entryTime">
              <a-date-picker
                v-model:value="employeeFormState.entryTime"
                style="width: 100%"
                :format="dateFormat"
                :valueFormat="dateValueFormat"
                :locale="zhCN"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="employeeFormState.status">
                <a-radio value="1">在职</a-radio>
                <a-radio value="0">离职</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 多岗位配置 - 移到最下面并增加宽度 -->
        <a-form-item label="岗位配置" name="positions" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
          <div class="position-config-container" :key="positionConfigRefreshKey">
            <div v-for="(position, index) in employeeFormState.positions" :key="`position-${index}-${positionConfigRefreshKey}`" class="position-config-card">
              <div class="position-card-header">
                <div class="position-card-title">
                  <span class="position-index">岗位 {{ index + 1 }}</span>
                  <a-tag v-if="position.isDefault" color="blue" class="default-tag">
                    <template #icon>
                      <star-filled />
                    </template>
                    默认岗位
                  </a-tag>
                </div>
                <a-button
                  @click="removePosition(index)"
                  type="text"
                  danger
                  size="small"
                  :disabled="employeeFormState.positions.length <= 1"
                  class="remove-btn"
                >
                  <template #icon>
                    <delete-outlined />
                  </template>
                </a-button>
              </div>

              <div class="position-card-content">
                <a-row :gutter="16">
                  <a-col :span="10">
                    <label class="form-item-label">选择岗位</label>
                    <a-cascader
                      :key="`cascader-${index}-${position.positionTypeId}-${position.positionId}`"
                      v-model:value="position.positionCascader"
                      :options="positionCascaderOptions"
                      @change="(value) => handlePositionCascaderChange(value, index)"
                      placeholder="请选择岗位"
                      style="width: 100%"
                    />
                  </a-col>
                  <a-col :span="8">
                    <label class="form-item-label">岗位等级</label>
                    <a-select
                      :key="`level-${index}-${position.levelId}`"
                      v-model:value="position.levelId"
                      placeholder="请选择等级"
                      style="width: 100%"
                      :disabled="!position.positionId"
                    >
                      <a-select-option 
                        v-for="level in (positionLevelOptionsMap[index] || [])" 
                        :key="level.value" 
                        :value="level.value"
                      >
                        {{ level.label }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="6">
                    <label class="form-item-label">默认设置</label>
                    <a-checkbox
                      v-model:checked="position.isDefault"
                      @change="(e) => handleDefaultChange(e, index)"
                      class="default-checkbox"
                    >
                      设为默认岗位
                    </a-checkbox>
                  </a-col>
                </a-row>
              </div>
            </div>

            <a-button @click="addPosition" type="dashed" block class="add-position-btn">
              <template #icon>
                <plus-outlined />
              </template>
              添加岗位配置
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入员工弹窗 -->
    <a-modal
      v-model:visible="importModalVisible"
      title="导入员工"
      @ok="handleImportOk"
      @cancel="handleImportCancel"
      width="600px"
      :confirm-loading="importLoading"
      :ok-button-props="{ disabled: importSuccess }"
    >
      <div class="import-content">
        <div class="import-tips">
          <h4>导入说明：</h4>
          <ul>
            <li>1、红色列为必填项</li>
            <li>2、部门名称请按系统中组织架构填写，多层级用/间隔，例如：阿依来/红旗南路店</li>
            <li>3、岗位级别格式：岗位类型-岗位名称-岗位等级，多岗位用/间隔，例如：餐厅服务-服务员-P2/后厨-传菜员-P3</li>
            <li>4、身份证号中的X请使用大写</li>
            <li>5、性别：男/女，不填写系统默认男</li>
            <li>6、入职时间：年-月-日</li>
            <li>7、状态：在职/离职，不填写系统默认在职</li>
          </ul>
        </div>
        
        <div class="import-actions">
          <a-space direction="vertical" style="width: 100%;">
            <div>
              <a-button type="link" @click="handleDownloadTemplate" :loading="downloadLoading">
                下载导入模板
              </a-button>
            </div>
            
            <div>
              <a-upload
                :file-list="fileList"
                :before-upload="beforeUpload"
                @remove="handleRemove"
                accept=".xlsx,.xls"
              >
                <a-button>
                  <upload-outlined />
                  选择文件
                </a-button>
              </a-upload>
            </div>
            
            <div v-if="importResult" class="import-result">
              <a-alert
                :message="importResult.message"
                :type="importResult.type"
                show-icon
                closable
              />
              <!-- 显示详细错误信息 -->
              <div v-if="importResult.type === 'error' && importResult.details" class="error-details">
                <a-typography-paragraph copyable>
                  <pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px; background: #f6f8fa; padding: 12px; border-radius: 4px; margin-top: 8px; max-height: 200px; overflow-y: auto;">{{ importResult.details }}</pre>
                </a-typography-paragraph>
              </div>
            </div>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 全屏loading遮罩 -->
    <div v-if="globalLoading" class="global-loading-overlay">
      <a-spin size="large" tip="正在导入员工，请稍候...">
        <div class="loading-content"></div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { PlusOutlined, EditOutlined, DeleteOutlined, StarFilled, UploadOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { SearchFormCard } from '@/components/SearchForm';

// 导入API接口
import { getDepartmentTree, addDepartment, updateDepartment, deleteDepartment } from '@/api/organization/department';
import { getEmployeeByDepartment, addEmployee, updateEmployee, deleteEmployee, importEmployees, exportEmployees, getEmployeePositions, downloadEmployeeTemplate } from '@/api/organization/employee';
import { getPositionOptions, getPositionNameOptions, getPositionTypeOptions } from '@/api/organization/position';
import { getLevelOptions, getLevelsByPosition } from '@/api/organization/position';

const actionConfig = ref({
    edit: true,
    delete: true
   })

const expandedKeys = ref([]);
// 当前选中的部门
const selectedDepartmentKeys = ref([]);
const currentDepartment = ref(null);

// 部门树数据
const departmentTreeData = ref([]);
// 岗位等级选项
const levelOptions = ref([]);

// 每个岗位配置对应的等级选项（按索引存储）
const positionLevelOptionsMap = ref({});

// 导入相关状态
const importModalVisible = ref(false);
const importLoading = ref(false);
const downloadLoading = ref(false);
const fileList = ref([]);
const importResult = ref(null);
// 全屏loading状态
const globalLoading = ref(false);
// 导入成功标志
const importSuccess = ref(false);

// 部门选择
const handleDepartmentSelect = (selectedKeys, e) => {
  if (selectedKeys.length > 0) {
    selectedDepartmentKeys.value = selectedKeys;
    currentDepartment.value = e.node;
  } else {
    selectedDepartmentKeys.value = [];
    currentDepartment.value = null;
  }

  // 重新加载员工数据
  loadEmployeeData();
};

// 部门树形选择器数据
const departmentOptions = computed(() => {
  // 递归构建部门树选择器数据
  const buildTreeOptions = (items) => {
    if (!items || !items.length) return [];

    return items.map(item => ({
      id: item.id,
      value: item.id,
      title: item.name,
      children: item.children ? buildTreeOptions(item.children) : undefined
    }));
  };

  return buildTreeOptions(departmentTreeData.value);
});

// 部门相关
// 新增/编辑部门表单相关
const departmentFormRef = ref(null);
const departmentModalVisible = ref(false);
const departmentModalTitle = ref('新增部门');
const departmentModalType = ref('add'); // add 或 edit
const currentDepartmentRecord = ref(null);

const departmentFormState = reactive({
  name: '',
  parentId: null,
  orderNum: 0,
  status: true,
  remark: '',
});

const departmentFormRules = {
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
};

// 打开新增部门弹窗
const handleAddDepartment = () => {
  departmentModalType.value = 'add';
  departmentModalTitle.value = '新增部门';
  currentDepartmentRecord.value = null;

  // 如果有选中的部门，则默认为其子部门
  if (currentDepartment.value) {
    departmentFormState.parentId = currentDepartment.value.id;
  } else {
    departmentFormState.parentId = null;
  }

  departmentFormState.name = '';
  departmentFormState.orderNum = 0;
  departmentFormState.status = true;
  departmentFormState.remark = '';

  departmentModalVisible.value = true;
};

// 编辑部门
const handleEditDepartment = (record) => {
  departmentModalType.value = 'edit';
  departmentModalTitle.value = '编辑部门';
  currentDepartmentRecord.value = record;

  departmentFormState.name = record.name;
  departmentFormState.parentId = record.parentId || null;
  departmentFormState.orderNum = record.orderNum || 0;
  departmentFormState.status = record.status !== false;
  departmentFormState.remark = record.remark || '';

  departmentModalVisible.value = true;
};

// 删除部门
const handleDeleteDepartment = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除部门"${record.name}"吗？删除后不可恢复！`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        await deleteDepartment(record.id);
        message.success('删除部门成功');
        // 重新加载部门树数据
        loadDepartmentData();
        // 如果删除的是当前选中的部门，清除选中状态
        if (currentDepartment.value && currentDepartment.value.id === record.id) {
          selectedDepartmentKeys.value = [];
          currentDepartment.value = null;
          // 重新加载员工数据（显示所有部门的员工）
          loadEmployeeData();
        }
      } catch (err) {
        // 处理特定的错误消息
        if (err.response && err.response.data && err.response.data.message) {
          message.error(err.response.data.message);
        } else {
          message.error(`删除部门失败: ${err.message}`);
        }
      }
    },
  });
};

// 确认部门弹窗
const handleDepartmentModalOk = () => {
  departmentFormRef.value.validate().then(() => {
    const formData = { ...departmentFormState };

    if (departmentModalType.value === 'add') {
      // 新增部门
      addDepartment(formData).then(() => {
        message.success('新增部门成功');
        departmentModalVisible.value = false;
        // 重新加载部门树数据
        loadDepartmentData();
      }).catch(err => {
        message.error(`新增部门失败: ${err.message}`);
      });
    } else {
      // 编辑部门
      formData.id = currentDepartmentRecord.value.id;
      updateDepartment(formData).then(() => {
        message.success('编辑部门成功');
        departmentModalVisible.value = false;
        // 重新加载部门树数据
        loadDepartmentData();
      }).catch(err => {
        message.error(`编辑部门失败: ${err.message}`);
      });
    }
  }).catch(() => {
    // 表单验证失败
  });
};

// 取消部门弹窗
const handleDepartmentModalCancel = () => {
  departmentModalVisible.value = false;
};

// 加载部门数据
const loadDepartmentData = () => {
  let deepList=[]
  getDepartmentTree().then(res => {
    deepList[0] = {
      id: 0,
      name: '全部',
      parentId: null,
      orderNum: 0,
      status: true,
    };
    departmentTreeData.value = deepList.concat(res)
    selectedDepartmentKeys.value = [0]
  }).catch(err => {
    message.error(`获取部门列表失败: ${err.message}`);
  });
};

// 员工相关
// 搜索表单
const searchForm = reactive({
  name: '',
  status: undefined,
});


// 搜索表单配置
const searchFormItems = computed(() => {
  return [
    {
      label: '姓名',
      field: 'name',
      type: 'input',
      placeholder: '请输入姓名'
    },
    {
      label: '状态',
      field: 'status',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '在职', value: 1 },
        { label: '离职', value: 0 }
      ]
    }
  ];
});

// 重置搜索
const resetSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  } else {
    searchForm.name = '';
    searchForm.status = undefined;
  }
  handleSearch();
};

// 查询
const handleSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  }
  employeePagination.current = 1;
  loadEmployeeData();
};

// 员工表格列定义
const employeeColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '岗位名称',
    dataIndex: 'position',
    key: 'position',
  },
  {
    title: '岗位等级',
    dataIndex: 'level',
    key: 'level',
  },
  {
    title: '入职时间',
    dataIndex: 'entryTime',
    key: 'entryTime',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '小程序认证状态',
    dataIndex: 'isActivated',
    key: 'isActivated',
  },
  {
    title: '操作',
    key: 'action',
  },
];

// 员工表格数据
const employeeDataSource = ref([]);
const employeeLoading = ref(false);
const employeePagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
});

// 员工表格变化处理
const handleEmployeeTableChange = (changeInfo) => {

  // BaseTable组件传递的是一个对象，包含pagination、filters、sorter等属性
  const { pagination, filters, sorter } = changeInfo;


  employeePagination.current = pagination.current;
  employeePagination.pageSize = pagination.pageSize;

  // 处理排序
  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };

  if (sorter && sorter.field) {
    params.sortField = sorter.field;
    params.sortOrder = sorter.order;
  }
  loadEmployeeData(params);
};

// 新增/编辑员工表单相关
const employeeFormRef = ref(null);
const employeeModalVisible = ref(false);
const employeeModalTitle = ref('新增员工');
const employeeModalType = ref('add'); // add 或 edit
const currentEmployeeRecord = ref(null);
const positionConfigRefreshKey = ref(0); // 用于强制刷新岗位配置组件

const employeeFormState = reactive({
  name: '',
  phone: '',
  departmentId: null,
  positions: [],
  idCard: '',
  entryTime: null,
  status: '1',
  isActivated: false,
});

const employeeFormRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号', trigger: 'blur' }
  ],
  departmentId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  positions: [{ required: true, message: '请选择岗位', trigger: 'change' }],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  entryTime: [{ required: true, message: '请选择入职时间', trigger: 'change' }],
};

// 岗位选项
const positionOptions = ref([]);

// 岗位名称选项（从字典获取）
const positionNameOptions = ref([]);

// 岗位类型选项
const positionTypeOptions = ref([]);

// 岗位级联选择器选项
const positionCascaderOptions = computed(() => {
  console.log('构建级联选择器选项 - 岗位类型:', positionTypeOptions.value);
  console.log('构建级联选择器选项 - 岗位名称:', positionNameOptions.value);

  const options = positionTypeOptions.value.map(type => ({
    value: Number(type.id), // 确保是数字类型
    label: type.name,
    children: positionNameOptions.value
      .filter(name => Number(name.typeId) === Number(type.id))
      .map(name => ({
        value: Number(name.id), // 确保是数字类型
        label: name.name || name.dictLabel
      }))
  }));

  console.log('构建完成的级联选择器选项:', options);
  return options;
});

// 处理级联选择器变更
const handlePositionCascaderChange = async (value, index) => {
  if (value && value.length === 2) {
    const selectedPositionId = value[1];
    
    // 检查是否已经选择了相同的岗位
    const isDuplicate = employeeFormState.positions.some((position, i) => {
      return i !== index && position.positionId === selectedPositionId;
    });
    
    if (isDuplicate) {
      message.error('该岗位已经被选择，请选择其他岗位');
      // 清空当前选择
      employeeFormState.positions[index].positionCascader = [];
      employeeFormState.positions[index].positionTypeId = null;
      employeeFormState.positions[index].positionId = null;
      employeeFormState.positions[index].levelId = null;
      positionLevelOptionsMap.value[index] = [];
      return;
    }
    
    employeeFormState.positions[index].positionTypeId = value[0]; // 设置岗位类型ID
    employeeFormState.positions[index].positionId = value[1]; // 设置岗位ID
    
    // 清空当前选择的等级
    employeeFormState.positions[index].levelId = null;
    
    // 根据选择的岗位获取对应的等级选项
    try {
      const levelOptions = await getLevelsByPosition(value[1]);
      console.log(`岗位${value[1]}对应的等级选项:`, levelOptions);
      
      // 将等级选项存储到对应的位置
      positionLevelOptionsMap.value[index] = levelOptions || [];
    } catch (error) {
      console.error('获取岗位等级选项失败:', error);
      message.error('获取岗位等级选项失败');
      positionLevelOptionsMap.value[index] = [];
    }
  } else {
    employeeFormState.positions[index].positionTypeId = null;
    employeeFormState.positions[index].positionId = null;
    employeeFormState.positions[index].levelId = null;
    // 清空等级选项
    positionLevelOptionsMap.value[index] = [];
  }
};

// 添加岗位配置
const addPosition = () => {
  const newPosition = {
    positionId: null,
    positionTypeId: null,
    levelId: null,
    positionCascader: [],
    isDefault: employeeFormState.positions.length === 0 // 第一个岗位默认为默认岗位
  };
  employeeFormState.positions.push(newPosition);
  
  // 为新增的岗位配置初始化等级选项
  const newIndex = employeeFormState.positions.length - 1;
  positionLevelOptionsMap.value[newIndex] = [];
};

// 删除岗位配置
const removePosition = (index) => {
  if (employeeFormState.positions.length <= 1) {
    message.warning('至少需要保留一个岗位配置');
    return;
  }

  const removedPosition = employeeFormState.positions[index];
  employeeFormState.positions.splice(index, 1);

  // 重新整理等级选项映射，移除被删除的索引，并重新编号
  const newPositionLevelOptionsMap = {};
  Object.keys(positionLevelOptionsMap.value).forEach(key => {
    const oldIndex = Number(key);
    if (oldIndex < index) {
      // 索引小于删除位置的保持不变
      newPositionLevelOptionsMap[oldIndex] = positionLevelOptionsMap.value[oldIndex];
    } else if (oldIndex > index) {
      // 索引大于删除位置的需要减1
      newPositionLevelOptionsMap[oldIndex - 1] = positionLevelOptionsMap.value[oldIndex];
    }
    // 等于index的被删除，不复制
  });
  positionLevelOptionsMap.value = newPositionLevelOptionsMap;

  // 如果删除的是默认岗位，需要设置第一个岗位为默认
  if (removedPosition.isDefault && employeeFormState.positions.length > 0) {
    employeeFormState.positions[0].isDefault = true;
  }
};

// 处理默认岗位变更
const handleDefaultChange = (e, index) => {
  if (e.target.checked) {
    // 取消其他岗位的默认状态
    employeeFormState.positions.forEach((pos, i) => {
      if (i !== index) {
        pos.isDefault = false;
      }
    });
    employeeFormState.positions[index].isDefault = true;
  } else {
    // 不允许取消默认岗位，必须有一个默认岗位
    message.warning('必须设置一个默认岗位');
    employeeFormState.positions[index].isDefault = true;
  }
};

// 新增日期格式常量
const dateFormat = 'YYYY-MM-DD';
const dateValueFormat = 'YYYY-MM-DD';

// 打开新增员工弹窗
const handleAddEmployee = () => {
  employeeModalType.value = 'add';
  employeeModalTitle.value = '新增员工';
  currentEmployeeRecord.value = null;

  employeeFormState.name = '';
  employeeFormState.phone = '';

  // 如果有选中的部门，则默认为该部门
  if (currentDepartment.value) {
    employeeFormState.departmentId = currentDepartment.value.id;
  } else {
    employeeFormState.departmentId = null;
  }

  // 初始化一个默认的岗位配置
  employeeFormState.positions = [{
    positionId: null,
    positionTypeId: null,
    levelId: null,
    positionCascader: [],
    isDefault: true
  }];
  
  // 初始化等级选项映射
  positionLevelOptionsMap.value = { 0: [] };
  
  employeeFormState.gender = 'male';
  employeeFormState.idCard = '';
  employeeFormState.entryTime = null;
  employeeFormState.status = '1';
  employeeFormState.isActivated = false;

  employeeModalVisible.value = true;
};

// 编辑员工
const handleEditEmployee = async (record) => {
  try {
    console.log('编辑员工 - 原始记录数据:', record);

    employeeModalType.value = 'edit';
    employeeModalTitle.value = '编辑员工';
    currentEmployeeRecord.value = record;

    employeeFormState.name = record.name;
    employeeFormState.phone = record.phone;
    employeeFormState.departmentId = record.departmentId;
    employeeFormState.gender = record.gender || 'male';
    employeeFormState.idCard = record.idCard;

    // 处理日期格式，使用dayjs处理
    if (record.entryTime) {
      try {
        // 使用dayjs解析日期，支持多种格式
        const date = dayjs(record.entryTime);
        if (date.isValid()) {
          employeeFormState.entryTime = date;
        } else {
          console.error('无效的日期:', record.entryTime);
          employeeFormState.entryTime = null;
        }
      } catch (e) {
        console.error('日期解析错误:', e);
        employeeFormState.entryTime = null;
      }
    } else {
      employeeFormState.entryTime = null;
    }

    employeeFormState.status = record.status;
    employeeFormState.isActivated = record.isActivated;

    // 先显示弹窗
    employeeModalVisible.value = true;

    // 等待DOM更新后再设置岗位配置
    await nextTick();

    // 获取员工的岗位配置
    try {
      const positionsResponse = await getEmployeePositions(record.id);
      console.log('获取到的岗位配置数据:', positionsResponse);
      console.log('岗位配置数据类型:', typeof positionsResponse);
      console.log('岗位配置数据结构:', JSON.stringify(positionsResponse, null, 2));

      if (positionsResponse && positionsResponse && positionsResponse.length > 0) {
        // 如果有岗位配置数据，使用多岗位配置
        console.log('使用多岗位配置数据');
        const processedPositions = positionsResponse.map((position, index) => {
          console.log(`处理第${index + 1}个岗位配置:`, position);

          // 确保所有ID都是数字类型
          const positionId = Number(position.positionId);
          const positionTypeId = Number(position.positionTypeId);
          const levelId = Number(position.levelId);

          console.log(`转换后的ID - positionId: ${positionId}, positionTypeId: ${positionTypeId}, levelId: ${levelId}`);

          const cascaderValue = positionTypeId && positionId ? [positionTypeId, positionId] : [];
          console.log(`级联选择器值:`, cascaderValue);

          return {
            positionId: positionId,
            positionTypeId: positionTypeId,
            levelId: levelId,
            isDefault: position.isDefault,
            positionCascader: cascaderValue
          };
        });

        console.log('处理后的岗位配置:', processedPositions);
        employeeFormState.positions = processedPositions;

        // 为每个岗位配置获取对应的等级选项
        await Promise.all(processedPositions.map(async (position, index) => {
          if (position.positionId) {
            try {
              const levelOptions = await getLevelsByPosition(position.positionId);
              console.log(`岗位${position.positionId}对应的等级选项:`, levelOptions);
              positionLevelOptionsMap.value[index] = levelOptions || [];
            } catch (error) {
              console.error(`获取岗位${position.positionId}等级选项失败:`, error);
              positionLevelOptionsMap.value[index] = [];
            }
          } else {
            positionLevelOptionsMap.value[index] = [];
          }
        }));

        // 强制触发响应式更新
        await nextTick();
        console.log('nextTick后的岗位配置:', employeeFormState.positions);

        // 再次强制更新
        employeeFormState.positions = [...processedPositions];
        await nextTick();
        console.log('强制更新后的岗位配置:', employeeFormState.positions);

        // 强制刷新组件
        positionConfigRefreshKey.value++;
        await nextTick();
        console.log('刷新key更新后的岗位配置:', employeeFormState.positions);
      } else {
        console.log('没有岗位配置数据，使用降级处理');
        // 如果没有岗位配置数据，使用员工表中的单岗位数据（向后兼容）
        if (record.positionId && record.levelId) {
          const positionId = Number(record.positionId);
          const positionTypeId = Number(record.positionTypeId);
          const levelId = Number(record.levelId);

          employeeFormState.positions = [{
            positionId: positionId,
            positionTypeId: positionTypeId,
            levelId: levelId,
            isDefault: true,
            positionCascader: positionTypeId && positionId ? [positionTypeId, positionId] : []
          }];
        } else {
          // 如果都没有，创建一个空的岗位配置
          employeeFormState.positions = [{
            positionId: null,
            positionTypeId: null,
            levelId: null,
            isDefault: true,
            positionCascader: []
          }];
        }
      }
    } catch (error) {
      console.error('获取员工岗位配置失败:', error);
      console.error('错误详情:', error.response || error.message);
      // 降级处理：使用员工表中的单岗位数据
      if (record.positionId && record.levelId) {
        const positionId = Number(record.positionId);
        const positionTypeId = Number(record.positionTypeId);
        const levelId = Number(record.levelId);

        employeeFormState.positions = [{
          positionId: positionId,
          positionTypeId: positionTypeId,
          levelId: levelId,
          isDefault: true,
          positionCascader: positionTypeId && positionId ? [positionTypeId, positionId] : []
        }];
      } else {
        employeeFormState.positions = [{
          positionId: null,
          positionTypeId: null,
          levelId: null,
          isDefault: true,
          positionCascader: []
        }];
      }
    }
  } catch (error) {
    console.error('编辑员工失败:', error);
    message.error('获取员工信息失败');
  }
};

// 删除员工
const handleDeleteEmployee = (record) => {
  // 使用Modal.confirm来显示确认对话框
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除员工 ${record.name} 吗？`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        await deleteEmployee(record.id);
        message.success('删除员工成功');
        // 重新加载员工数据
        loadEmployeeData();
      } catch (err) {
        message.error(`删除员工失败: ${err.message}`);
      }
    },
  });
};

// 确认员工弹窗
const handleEmployeeModalOk = () => {
  employeeFormRef.value.validate().then(() => {
    const formData = { ...employeeFormState };

    // 处理日期格式，使用dayjs格式化
    if (formData.entryTime) {
      try {
        if (dayjs.isDayjs(formData.entryTime)) {
          // 如果是dayjs对象，格式化为YYYY-MM-DD字符串
          formData.entryTime = formData.entryTime.format('YYYY-MM-DD');
        } else if (formData.entryTime instanceof Date) {
          // 如果是Date对象，转换为dayjs再格式化
          formData.entryTime = dayjs(formData.entryTime).format('YYYY-MM-DD');
        } else if (typeof formData.entryTime === 'string') {
          // 如果是字符串，确保格式正确
          formData.entryTime = dayjs(formData.entryTime).format('YYYY-MM-DD');
        }
      } catch (e) {
        console.error('日期处理错误:', e);
        formData.entryTime = null;
      }
    }

    // 验证岗位配置
    if (!formData.positions || formData.positions.length === 0) {
      message.error('请至少配置一个岗位');
      return;
    }

    // 验证是否有默认岗位
    const hasDefault = formData.positions.some(pos => pos.isDefault);
    if (!hasDefault) {
      message.error('请设置一个默认岗位');
      return;
    }

    // 验证岗位配置是否完整
    for (let i = 0; i < formData.positions.length; i++) {
      const pos = formData.positions[i];
      if (!pos.positionId || !pos.positionTypeId || !pos.levelId) {
        message.error(`第${i + 1}个岗位配置不完整，请选择岗位类型、岗位和等级`);
        return;
      }
    }

    // 验证是否有重复的岗位
    const positionIds = formData.positions.map(pos => pos.positionId);
    const uniquePositionIds = [...new Set(positionIds)];
    if (positionIds.length !== uniquePositionIds.length) {
      message.error('存在重复的岗位配置，请检查后重新选择');
      return;
    }

    // 处理岗位配置数据，移除前端特有的字段
    const processedPositions = formData.positions.map(pos => ({
      positionId: pos.positionId,
      positionTypeId: pos.positionTypeId,
      levelId: pos.levelId,
      isDefault: pos.isDefault
    }));

    // 设置处理后的岗位配置
    formData.positions = processedPositions;

    if (employeeModalType.value === 'add') {
      // 新增员工
      addEmployee(formData).then(() => {
        message.success('新增员工成功');
        employeeModalVisible.value = false;
        // 重新加载员工数据
        loadEmployeeData();
      }).catch(err => {
        message.error(`新增员工失败: ${err.message}`);
      });
    } else {
      // 编辑员工
      formData.id = currentEmployeeRecord.value.id;
      updateEmployee(formData).then(() => {
        message.success('编辑员工成功');
        employeeModalVisible.value = false;
        // 重新加载员工数据
        loadEmployeeData();
      }).catch(err => {
        message.error(`编辑员工失败: ${err.message}`);
      });
    }
  }).catch(() => {
    // 表单验证失败
  });
};

// 取消员工弹窗
const handleEmployeeModalCancel = () => {
  employeeModalVisible.value = false;
  // 清空等级选项映射
  positionLevelOptionsMap.value = {};
};

// 导出员工数据
const handleExport = () => {
  const params = { ...searchForm };

  // 如果有选中的部门，添加部门ID
  if (currentDepartment.value) {
    params.departmentId = currentDepartment.value.id;
  }

  exportEmployees(params).then(res => {
    // 处理二进制流
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '员工数据.xlsx';
    link.click();
    URL.revokeObjectURL(link.href);
  }).catch(err => {
    message.error(`导出员工失败: ${err.message}`);
  });
};

// 打开导入弹窗
const handleImport = () => {
  importModalVisible.value = true;
  fileList.value = [];
  importResult.value = null;
  // 重置所有状态
  importLoading.value = false;
  globalLoading.value = false;
  importSuccess.value = false;
};

// 下载导入模板
const handleDownloadTemplate = async () => {
  downloadLoading.value = true;
  try {
    const response = await downloadEmployeeTemplate();
    // 处理二进制流，使用正确的MIME类型
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '员工导入模板.xlsx';
    document.body.appendChild(link); // 确保链接被添加到DOM
    link.click();
    document.body.removeChild(link); // 清理DOM
    URL.revokeObjectURL(link.href);
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('模板下载失败');
  } finally {
    downloadLoading.value = false;
  }
};

// 文件上传前的处理
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }
  
  fileList.value = [file];
  importResult.value = null;
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = () => {
  fileList.value = [];
  importResult.value = null;
};

// 确认导入
const handleImportOk = async () => {
  // 如果已经导入成功，不允许再次点击
  if (importSuccess.value) {
    return;
  }
  
  if (fileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }
  
  // 启动全屏loading，完全阻止用户操作
  globalLoading.value = true;
  importLoading.value = true;
  
  try {
    const formData = new FormData();
    formData.append('file', fileList.value[0]);
    
    const response = await importEmployees(formData);
    
    importResult.value = {
      type: 'success',
      message: `成功导入 ${response.count || 0} 名员工`
    };
    
    // 设置导入成功标志，禁用确定按钮
    importSuccess.value = true;
    
    // 重新加载数据
    loadEmployeeData();
    
    // 3秒后关闭弹窗
    setTimeout(() => {
      importModalVisible.value = false;
      // 关闭弹窗时重置状态
      importSuccess.value = false;
    }, 3000);
    
  } catch (error) {
    console.error('导入失败:', error);
    
    // 处理详细的错误信息
    let errorMessage = '导入失败';
    let errorDetails = null;
    
    if (error.response && error.response.data) {
      const errorData = error.response.data;
      errorMessage = errorData.message || '导入失败';
      
      // 如果有详细的错误列表，显示前10个错误
      if (errorData.errors && Array.isArray(errorData.errors)) {
        const maxErrors = 10;
        const errors = errorData.errors.slice(0, maxErrors);
        const hasMore = errorData.errors.length > maxErrors;
        
        errorDetails = errors.join('\n') + (hasMore ? `\n... 还有 ${errorData.errors.length - maxErrors} 个错误` : '');
      }
    }
    
    importResult.value = {
      type: 'error',
      message: errorMessage,
      details: errorDetails
    };
  } finally {
    // 关闭全屏loading
    globalLoading.value = false;
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  fileList.value = [];
  importResult.value = null;
  // 重置所有状态
  importLoading.value = false;
  globalLoading.value = false;
  importSuccess.value = false;
};

// 岗位相关辅助函数
const getPositionName = (positionId) => {
  if (!positionId) return '';
  const position = positionNameOptions.value.find(item => item.id === positionId);
  return position ? (position.name || position.dictLabel) : '';
};

// 加载员工数据
const loadEmployeeData = (params = {}) => {
  employeeLoading.value = true;

  // 先设置默认的分页参数
  const defaultParams = {
    pageNum: employeePagination.current || 1,
    pageSize: employeePagination.pageSize || 10
  };

  // 合并查询参数，params中的参数优先级最高
  const queryParams = {
    ...defaultParams,
    ...searchForm,
    ...params,  // params放在最后，优先级最高
  };

  // 过滤掉值为undefined或空字符串的参数，但保留数字0
  Object.keys(queryParams).forEach(key => {
    if (queryParams[key] === undefined || queryParams[key] === '') {
      delete queryParams[key];
    }
  });

  // 确保分页参数存在
  if (!queryParams.pageNum) {
    queryParams.pageNum = 1;
  }
  if (!queryParams.pageSize) {
    queryParams.pageSize = 10;
  }

  // 添加调试日志



  // 如果有选中的部门，获取该部门下的员工
  if (currentDepartment.value && currentDepartment.value.id !== 0) {

    getEmployeeByDepartment(currentDepartment.value.id, queryParams).then(res => {

      // 处理响应数据
      if (res && res.data) {
        employeeDataSource.value = res.data.rows || [];
        employeePagination.total = res.data.total || 0;
      } else {
        // 兼容旧格式
        employeeDataSource.value = res.rows || [];
        employeePagination.total = res.total || 0;
      }
      employeeLoading.value = false;
    }).catch(err => {
      message.error(`获取员工列表失败: ${err.message}`);
      employeeLoading.value = false;
    });
  } else {
    // 获取所有员工

    getEmployeeByDepartment(null, queryParams).then(res => {
      // 处理响应数据
      if (res && res.data) {
        employeeDataSource.value = res.data.rows || [];
        employeePagination.total = res.data.total || 0;
      } else {
        // 兼容旧格式
        employeeDataSource.value = res.rows || [];
        employeePagination.total = res.total || 0;
      }
      employeeLoading.value = false;
    }).catch(err => {
      message.error(`获取员工列表失败: ${err.message}`);
      employeeLoading.value = false;
    });
  }
};

// 加载岗位和岗位等级选项
const loadOptions = () => {
  // 获取岗位选项
  getPositionOptions().then(res => {
    positionOptions.value = res.map(item => ({
      value: item.id,
      label: item.name,
    }));
  }).catch(err => {
    message.error(`获取岗位选项失败: ${err.message}`);
  });

  // 获取岗位名称选项
  getPositionNameOptions().then(res => {
    positionNameOptions.value = res.rows || res;
    console.log('岗位名称选项：', positionNameOptions.value);
  }).catch(err => {
    message.error(`获取岗位名称选项失败: ${err.message}`);
  });

  // 获取岗位类型选项
  getPositionTypeOptions().then(res => {
    positionTypeOptions.value = res;
    console.log('岗位类型选项：', positionTypeOptions.value);
  }).catch(err => {
    message.error(`获取岗位类型选项失败: ${err.message}`);
  });

  // 获取岗位等级选项
  getLevelOptions().then(res => {
    levelOptions.value = res.map(item => ({
      value: Number(item.id), // 确保是数字类型
      label: item.name,
    }));
    console.log('岗位等级选项：', levelOptions.value);
  }).catch(err => {
    message.error(`获取岗位等级选项失败: ${err.message}`);
  });
};

// 组件挂载时加载数据
onMounted(() => {
  console.log('[onMounted] employeePagination初始值:', JSON.parse(JSON.stringify(employeePagination)));
  console.log('[onMounted] employeePagination.current:', employeePagination.current);
  console.log('[onMounted] employeePagination.pageSize:', employeePagination.pageSize);

  loadDepartmentData();
  loadEmployeeData();
  loadOptions();
});
</script>

<style scoped>
@import './structure.css';
.position-config-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
  margin-top: 8px;
}

.position-config-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.position-config-item:last-child {
  margin-bottom: 0;
}

.position-config-item:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.position-config-card {
  margin-bottom: 16px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.position-config-card:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.position-config-card:last-child {
  margin-bottom: 0;
}

.position-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.position-card-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 15px;
}

.position-index {
  color: #666;
  font-size: 14px;
  margin-right: 12px;
}

.default-tag {
  margin-left: 12px;
  font-size: 12px;
}

.remove-btn {
  color: #ff4d4f;
  border: none;
  padding: 6px 10px;
  height: auto;
  line-height: 1;
}

.remove-btn:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.remove-btn:disabled {
  color: #d9d9d9;
  background-color: transparent;
}

.position-card-content {
  padding: 20px;
}

.form-item-label {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.default-checkbox {
  color: #666;
  font-size: 14px;
  margin-top: 0;
}

.add-position-btn {
  margin-top: 16px;
  height: 44px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-position-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
  background-color: #f6ffed;
}

.import-content {
  padding: 16px 0;
}


.import-actions {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 24px;
  text-align: center;
}

.import-result {
  margin-top: 16px;
}

.error-details {
  margin-top: 8px;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 4px;
}

.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  width: 100px;
  height: 100px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}
</style>
