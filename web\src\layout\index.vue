<template>
  <a-config-provider :locale="zhCN">
  <a-layout class="layout-container">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      theme="light"
      class="layout-sider"
    >
      <div class="logo">
        <span v-if="!collapsed">餐烤餐考</span>
        <span v-else>餐</span>
      </div>
      
      <!-- 菜单容器 -->
      <div class="menu-wrapper">
        <!-- 动态菜单 -->
        <menu-tree :menus="userMenus" ref="menuTreeRef" /> 
      </div>
      
      <!-- 侧边栏底部折叠按钮 -->
      <div class="sider-trigger">
        <menu-unfold-outlined
          v-if="collapsed"
          class="trigger"
          @click="() => (collapsed = !collapsed)"
        />
        <menu-fold-outlined
          v-else
          class="trigger"
          @click="() => (collapsed = !collapsed)"
        />
      </div>
    </a-layout-sider>
    
    <!-- 主体区域 -->
    <a-layout>
      <!-- 顶部栏 -->
      <a-layout-header class="layout-header">
        <div class="header-breadcrumb">
          <div class="breadcrumb-wrapper">
            <router-link to="/dashboard" class="breadcrumb-home">
              <home-outlined class="breadcrumb-icon" />
              <span>首页</span>
            </router-link>
            <template v-for="(item, index) in breadcrumbItems" :key="index">
              <span class="breadcrumb-separator">/</span>
              <router-link 
                v-if="index < breadcrumbItems.length - 1 && item.path && item.path !== '/'" 
                :to="item.fullPath"
                class="breadcrumb-item"
              >
                {{ item.title }}
              </router-link>
              <span v-else class="breadcrumb-item breadcrumb-item-active">{{ item.title }}</span>
            </template>
          </div>
        </div>
        <div class="header-right">
          <a-dropdown>
            <div class="user-info">
              <user-outlined class="user-icon" />
              <span class="username">{{ username }}</span>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item key="settings" @click="handleSettings">
                  <setting-outlined />
                  <span>个人设置</span>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined />
                  <span>退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
      
      <!-- 页脚 -->
      <!-- <a-layout-footer class="layout-footer">
        餐烤餐考后台管理系统 ©2025
      </a-layout-footer> -->
    </a-layout>
  </a-layout>
</a-config-provider>
</template>

<script setup>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { useMenuStore } from '@/store/modules/menu';
import MenuTree from '@/components/MenuTree.vue';
import { 
  MenuUnfoldOutlined, 
  MenuFoldOutlined, 
  UserOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  HomeOutlined
} from '@ant-design/icons-vue';
import { Modal, message } from 'ant-design-vue';
import { getUserInfo } from '@/utils/auth';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const menuStore = useMenuStore();
const menuTreeRef = ref(null);

// 用户菜单数据
const userMenus = computed(() => {
  // 如果用户菜单为空，使用备用菜单
  if (!userStore.userMenus || !Array.isArray(userStore.userMenus) || userStore.userMenus.length === 0) {
    return getFallbackMenus();
  }
  
  return userStore.userMenus;
});

// 备用菜单数据
const getFallbackMenus = () => {
  return [
    {
      id: 1,
      parentId: 0,
      name: '控制台',
      path: '/dashboard',
      component: 'dashboard/index',
      redirect: null,
      icon: 'dashboard',
      sort: 1,
      hidden: false,
      type: 1,
      perms: 'dashboard',
      status: true
    },
    {
      id: 2,
      parentId: 0,
      name: '企业管理',
      path: '/enterprise',
      component: null,
      redirect: null,
      icon: 'team',
      sort: 2,
      hidden: false,
      type: 0,
      perms: null,
      status: true,
      children: [
        {
          id: 21,
          parentId: 2,
          name: '企业列表',
          path: '/enterprise/list',
          component: 'enterprise/list/index',
          redirect: null,
          icon: null,
          sort: 1,
          hidden: false,
          type: 1,
          perms: 'enterprise.list',
          status: true
        }
      ]
    },
    {
      id: 4,
      parentId: 0,
      name: '系统管理',
      path: '/system',
      component: null,
      redirect: null,
      icon: 'setting',
      sort: 3,
      hidden: false,
      type: 0,
      perms: null,
      status: true,
      children: [
        {
          id: 41,
          parentId: 4,
          name: '用户管理',
          path: '/system/user',
          component: 'system/user/index',
          redirect: null,
          icon: null,
          sort: 1,
          hidden: false,
          type: 1,
          perms: 'system.user',
          status: true
        },
        {
          id: 42,
          parentId: 4,
          name: '角色管理',
          path: '/system/role',
          component: 'system/role/index',
          redirect: null,
          icon: null,
          sort: 2,
          hidden: false,
          type: 1,
          perms: 'system.role',
          status: true
        },
        {
          id: 43,
          parentId: 4,
          name: '菜单管理',
          path: '/system/menu',
          component: 'system/menu/index',
          redirect: null,
          icon: null,
          sort: 3,
          hidden: false,
          type: 1,
          perms: 'system.menu',
          status: true
        }
      ]
    }
  ];
};

// 菜单折叠状态
const collapsed = ref(false);

// 用户名
const username = ref(getUserInfo()?.nickname || 'Admin');

// 面包屑数据
const breadcrumbItems = computed(() => {
  // 获取当前路由的匹配记录
  const matched = route.matched.filter(record => {
    // 过滤掉首页和布局组件
    return record.name !== 'Layout' && record.path !== '/' && record.path !== '/dashboard';
  });
  
  // 提取每个匹配路由的标题信息
  return matched.map(record => {
    // 处理路由路径，避免重复的斜杠
    const path = record.path.startsWith('/') ? record.path : `/${record.path}`;
    
    return {
      title: record.meta?.title || record.name,
      path: path,
      fullPath: path
    };
  });
});

// 初始化加载菜单
onMounted(async () => {
  // 总是重新加载菜单，确保数据最新
  try {
    const menus = await userStore.getUserMenus();
  } catch (error) {
    console.error('Layout组件加载菜单失败:', error);
  }
});

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      userStore.logout();
      message.success('已退出登录');
      router.push('/login');
    }
  });
};

// 处理个人设置
const handleSettings = () => {
  router.push('/system/profile');
};
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
}

/* 侧边栏样式 */
.layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  height: 100vh;
  position: fixed;
  left: 0;
  display: flex;
  flex-direction: column;
}

.logo {
  height: 64px;
  line-height: 64px;
  padding-left: 24px;
  font-size: 18px;
  font-weight: bold;
  overflow: hidden;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.menu-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 64px - 48px); /* 视口高度减去logo和底部按钮的高度 */
}

/* 自定义滚动条样式 */
.menu-wrapper::-webkit-scrollbar {
  width: 5px;
}

.menu-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.menu-wrapper::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

.sider-trigger {
  height: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #a18cd1;
}

/* 顶部栏样式 */
.layout-header {
  background: #fff;
  padding: 0 24px;
  margin-left: 200px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 9;
  transition: margin-left 0.3s;
  overflow: hidden;
}

.collapsed:host .layout-header {
  margin-left: 80px;
}

/* 面包屑导航样式 */
.header-breadcrumb {
  display: flex;
  align-items: center;
  padding-left: 12px;
  min-width: 200px;
  flex-grow: 1;
  max-width: 60%;
  overflow: hidden;
}

.breadcrumb-wrapper {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
}

.breadcrumb-home {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
  padding: 0 4px;
}

.breadcrumb-icon {
  color: #a18cd1;
  font-size: 16px;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.breadcrumb-item {
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
  padding: 0 4px;
  display: inline-block;
}

.breadcrumb-item-active {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.breadcrumb-home span {
  margin-left: 4px;
}

.breadcrumb-home span,
.breadcrumb-item {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.breadcrumb-home:hover,
.breadcrumb-item:hover {
  color: #a18cd1;
}

@media (max-width: 768px) {
  .header-breadcrumb {
    max-width: 50%;
  }
  
  .breadcrumb-item,
  .breadcrumb-home span {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* 用户信息样式 */
.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  transition: background-color 0.3s;
  border-radius: 4px;
  height: 40px;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.user-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #a18cd1;
}

.username {
  font-size: 14px;
  color: #333;
}

/* 内容区域样式 */
.layout-content {
  margin-left: 200px;
  padding: 12px;
  background-color: #f0f2f5;
  height: calc(100vh - 64px);
}

.collapsed:host .layout-content {
  margin-left: 80px;
}

.content-wrapper {
  background-color: #fff;
  padding: 24px;
  height: calc(100vh - 64px - 48px);
  border-radius: 4px;
  overflow:hidden;
  box-sizing: border-box;
}
/* 页脚样式 */
.layout-footer {
  margin-left: 200px;
  text-align: center;
  transition: margin-left 0.3s;
}

.collapsed:host .layout-footer {
  margin-left: 80px;
}
</style> 