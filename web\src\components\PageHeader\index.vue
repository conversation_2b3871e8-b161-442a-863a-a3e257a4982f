<template>
  <div class="page-header">
    <div class="header-search">
      <slot name="search"></slot>
    </div>
    <div class="header-actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>ead<PERSON>',
  props: {}
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  

  /* padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06); */

}


.header-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style> 