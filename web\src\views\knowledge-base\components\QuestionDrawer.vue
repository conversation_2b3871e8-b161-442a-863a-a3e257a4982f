<template>
  <a-drawer
    :visible="visible"
    :title="'文档题目管理'"
    :width="720"
    @close="handleClose"
    :bodyStyle="{ paddingBottom: '80px' }"
    @afterVisibleChange="handleVisibleChange"
  >
    <a-spin :spinning="loading">
      <div class="question-drawer">
        <!-- 操作按钮 -->
        <div class="operation-bar">
          <a-button type="primary" @click="showAddModal">
            <plus-outlined /> 新增题目
          </a-button>
          <a-button @click="fetchQuestions" style="margin-left: 8px">
            <sync-outlined /> 刷新
          </a-button>
        </div>

        <!-- 题目列表卡片形式 -->
        <div v-if="questionList.length > 0" class="question-card-list">
          <a-card 
            v-for="(question, index) in questionList" 
            :key="question.id"
            class="question-card"
            :bordered="true"
          >
            <template #title>
              <div class="question-card-title">
                <span class="question-number">题目 {{ index + 1 }}</span>
                <a-tag :color="question.type === '智能出题' ? '#108ee9' : '#87d068'">
                  {{ question.type }}
                </a-tag>
              </div>
            </template>
            <template #extra>
              <div class="question-card-actions">
                <a-button type="link" @click="handleEdit(question)">
                  <edit-outlined /> 编辑
                </a-button>
                <a-button type="link" danger @click="handleDelete(question)">
                  <delete-outlined /> 删除
                </a-button>
              </div>
            </template>
            <div class="question-card-content">
              <div class="question-content">
                <div class="content-label">题目：</div>
                <div class="content-text">{{ question.question }}</div>
              </div>
              <a-divider style="margin: 12px 0" />
              <div class="answer-content">
                <div class="content-label">答案：</div>
                <div class="content-text">{{ question.answer }}</div>
              </div>
              <div v-if="question.segment_id" class="segment-id">
                <div class="content-label">分段ID：</div>
                <div class="content-text">{{ question.segment_id }}</div>
              </div>
            </div>
          </a-card>
          
          <!-- 没有题目时显示空状态 -->
          <a-empty v-if="questionList.length === 0" description="暂无题目数据" />
        </div>
      </div>
    </a-spin>

    <!-- 新增/编辑题目对话框 -->
    <a-modal
      :visible="modalVisible"
      :title="modalType === 'add' ? '新增题目' : '编辑题目'"
      @ok="handleModalSubmit"
      @cancel="modalVisible = false"
      :confirmLoading="submitLoading"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="题目" name="question">
          <a-textarea
            v-model:value="formState.question"
            placeholder="请输入题目内容"
            :auto-size="{ minRows: 2, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item label="答案" name="answer">
          <a-textarea
            v-model:value="formState.answer"
            placeholder="请输入答案内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 抽屉底部操作按钮 -->
    <div class="drawer-footer">
      <a-button @click="handleClose">关闭</a-button>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { 
  getQuestionList, 
  addQuestion, 
  updateQuestion, 
  deleteQuestion 
} from '@/api/enterprise/knowledge';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  knowledgeId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

// 表单相关
const formRef = ref(null);
const modalVisible = ref(false);
const modalType = ref('add'); // 'add' or 'edit'
const formState = ref({
  id: '',
  question: '',
  answer: ''
});

// 表单校验规则
const rules = {
  question: [
    { required: true, message: '请输入题目内容', trigger: 'blur' },
    { max: 500, message: '题目内容不能超过500个字符', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请输入答案内容', trigger: 'blur' },
    { max: 1000, message: '答案内容不能超过1000个字符', trigger: 'blur' }
  ]
};

// 加载状态
const loading = ref(false);
const tableLoading = ref(false);
const submitLoading = ref(false);

// 题目列表
const questionList = ref([]);

// 获取题目列表
const fetchQuestions = async () => {
  if (!props.knowledgeId) return;
  
  tableLoading.value = true;
  loading.value = true;
  
  try {
    const res = await getQuestionList(props.knowledgeId);
    // 根据修改后的接口响应结构调整数据获取
    if (res && res.code === 200) {
      questionList.value = res.data || [];
    } else if (Array.isArray(res)) {
      // 如果直接返回数组数据
      questionList.value = res;
    } else {
      // 其他情况，尝试从不同层级获取数据
      questionList.value = res?.data?.list || res?.list || res?.data || [];
    }
  } catch (error) {
    console.error('获取题目列表失败:', error);
    message.error('获取题目列表失败');
  } finally {
    tableLoading.value = false;
    loading.value = false;
  }
};

// 显示新增模态框
const showAddModal = () => {
  modalType.value = 'add';
  formState.value = {
    id: '',
    question: '',
    answer: ''
  };
  modalVisible.value = true;
};

// 显示编辑模态框
const handleEdit = (record) => {
  modalType.value = 'edit';
  formState.value = {
    id: record.id,
    question: record.question,
    answer: record.answer
  };
  modalVisible.value = true;
};

// 处理删除题目
const handleDelete = async (record) => {
  if (!props.knowledgeId || !record.id) {
    message.error('删除失败：缺少知识库ID或题目ID');
    return;
  }
  
  try {
    loading.value = true; // 显示加载状态
    console.log(`准备删除题目，知识库ID: ${props.knowledgeId}, 题目ID: ${record.id}`);
    
    const res = await deleteQuestion(props.knowledgeId, record.id);
    console.log('删除题目响应:', res);
    
    // 检查响应状态

      message.success('删除题目成功');
      // 刷新题目列表
      await fetchQuestions();
    
  } catch (error) {
    console.error('删除题目失败:', error);
    message.error(`删除题目失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false; // 无论成功失败都关闭加载状态
  }
};

// 提交表单
const handleModalSubmit = () => {
  formRef.value.validate().then(async () => {
    submitLoading.value = true;
    try {
      console.log("提交表单数据:", {
        knowledgeId: props.knowledgeId,
        modalType: modalType.value,
        formData: formState.value
      });
      
      if (modalType.value === 'add') {
        // 新增题目
        const res = await addQuestion(props.knowledgeId, {
          question: formState.value.question,
          answer: formState.value.answer
        });
        
        console.log("添加题目响应:", res);
        
        if (res && (res.code === 200 || res.id)) {
          message.success('添加题目成功');
          modalVisible.value = false;
          await fetchQuestions();
        } else {
          message.error(res?.message || '添加题目失败');
        }
      } else {
        // 编辑题目
        const res = await updateQuestion(props.knowledgeId, {
          questionId: formState.value.id,
          question: formState.value.question,
          answer: formState.value.answer
        });
        
        console.log("更新题目响应:", res);
        
        if (res && (res.code === 200 || res.id)) {
          message.success('更新题目成功');
          modalVisible.value = false;
          await fetchQuestions();
        } else {
          message.error(res?.message || '更新题目失败');
        }
      }
    } catch (error) {
      console.error(modalType.value === 'add' ? '添加题目失败:' : '更新题目失败:', error);
      message.error(modalType.value === 'add' ? '添加题目失败' : '更新题目失败');
    } finally {
      submitLoading.value = false;
    }
  }).catch(error => {
    console.error('表单验证失败:', error);
  });
};

// 关闭处理函数
const handleClose = () => {
  // 立即清空数据
  questionList.value = [];
  emit('update:visible', false);
};

// 组件挂载时确保数据加载
onMounted(() => {
  console.log("组件挂载", props.visible, props.knowledgeId);
  if (props.visible && props.knowledgeId) {
    fetchQuestions();
  }
});

// 修改watch函数，优化处理顺序
watch(
  () => props.visible,
  (newVal) => {
    console.log("抽屉显示状态变化", newVal, props.knowledgeId);
    // 无论是打开还是关闭，都立即清空数据
    questionList.value = [];
    
    // 如果是打开，则请求新数据
    if (newVal && props.knowledgeId) {
      fetchQuestions();
    }
  }
);

// 修改知识库ID监听，立即清空
watch(
  () => props.knowledgeId,
  (newVal, oldVal) => {
    console.log("知识库ID变化", newVal, oldVal, props.visible);
    // 只要ID变化，立即清空数据
    questionList.value = [];
    
    if (newVal && props.visible) {
      fetchQuestions();
    }
  }
);

// 修改处理可见性变化的函数
const handleVisibleChange = (visible) => {
  console.log("抽屉可见性变化完成", visible, props.knowledgeId);
  // 只在打开时请求数据，不再清空数据(已在watch中清空)
  if (visible && props.knowledgeId) {
    fetchQuestions();
  }
};
</script>

<style lang="scss" scoped>
.question-drawer {
  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
}

// 卡片样式
.question-card-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.question-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  .question-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .question-number {
      font-weight: 500;
      font-size: 16px;
    }
  }
  
  .question-card-actions {
    display: flex;
    gap: 8px;
  }
  
  .question-card-content {
    .question-content, .answer-content, .segment-id {
      margin-bottom: 12px;
    }
    
    .content-label {
      font-weight: 500;
      color: #666;
      margin-bottom: 4px;
    }
    
    .content-text {
      color: #333;
      line-height: 1.6;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
  
  button {
    margin-left: 8px;
  }
}
</style> 