/**
 * 全局组件注册
 */
import { SearchForm, SearchFormCard } from './SearchForm';
import PageHeader from './PageHeader';
import BaseTable from './BaseTable';
// import { PlusOutlined, SearchOutlined, ReloadOutlined, EyeOutlined, DeleteOutlined, EditOutlined, UnorderedListOutlined, QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';

// 常用组件列表
const components = [
  SearchForm,
  SearchFormCard,
  PageHeader,
  BaseTable
];

// 常用图标列表
// const icons = [
//   PlusOutlined, 
//   SearchOutlined, 
//   ReloadOutlined, 
//   EyeOutlined, 
//   DeleteOutlined, 
//   EditOutlined, 
//   UnorderedListOutlined, 
//   QuestionCircleOutlined,
//   UploadOutlined
// ];

// 注册全局组件
export default {
  install(app) {
    // 注册自定义组件
    components.forEach(component => {
      app.component(component.name, component);
    });
    
    // 注册常用图标组件
    // icons.forEach(icon => {
    //   app.component(icon.displayName || icon.name, icon);
    // });
  }
}; 