# 样式指南文档

## 样式统一规范

### 页面根容器

所有页面根容器统一使用 `page-container` 类名，以确保样式的一致性和可维护性。

```vue
<template>
  <div class="page-container">
    <!-- 页面内容 -->
  </div>
</template>
```

### 特殊页面样式

对于需要特殊样式的页面（如登录页），使用组合类名方式：

```vue
<template>
  <div class="page-container login-page">
    <!-- 特殊页面内容 -->
  </div>
</template>

<style scoped>
.page-container.login-page {
  /* 特殊样式，覆盖全局样式 */
  padding: 0;
  height: 100vh;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}
</style>
```

### 样式分层原则

1. **全局通用样式**：放在 `global.scss` 中
   - 包括所有页面共用的样式定义，如页面容器、表格样式、按钮样式等

2. **组件特有样式**：放在各自组件的 `<style>` 标签中
   - 仅针对当前组件的特殊样式，不应该影响其他组件

3. **变量和主题**：使用 `variables.css` 中定义的 CSS 变量
   - 颜色、间距、字体等应该使用变量而非硬编码值

### CSS 变量使用

优先使用 CSS 变量而非硬编码颜色和尺寸：

```scss
// 推荐
.element {
  color: var(--primary-color);
  padding: var(--spacing-md);
}

// 不推荐
.element {
  color: #a18cd1;
  padding: 16px;
}
```

## 常用类名

- `page-container`: 页面根容器
- `table-card`: 表格卡片容器
- `search-card`: 搜索表单容器
- `custom-button`: 自定义按钮
- `action-buttons`: 操作按钮组

## 常用 CSS 变量

- `--primary-color`: 主色调 (#a18cd1)
- `--secondary-color`: 次要色调 (#fbc2eb)
- `--bg-color`: 背景色 (#f8f9fa)
- `--spacing-md`: 标准间距 (16px)
- `--border-radius`: 标准圆角 (4px)

## 深度选择器

使用 `:deep()` 语法修改第三方组件样式：

```scss
.container {
  :deep(.ant-table-row) {
    background-color: var(--bg-color);
  }
}
``` 