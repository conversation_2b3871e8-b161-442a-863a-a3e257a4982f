import request from '@/utils/request';

/**
 * 获取员工列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getEmployeeList(params) {
  return request({
    url: '/organization/employee/list',
    method: 'get',
    params
  });
}

/**
 * 根据部门ID获取员工
 * @param {Number} departmentId - 部门ID
 * @param {Object} params - 其他查询参数
 * @returns {Promise} 请求结果
 */
export function getEmployeeByDepartment(departmentId, params) {
  return request({
    url: `/organization/employee/department/${departmentId}`,
    method: 'get',
    params
  });
}

/**
 * 获取员工详情
 * @param {Number} id - 员工ID
 * @returns {Promise} 请求结果
 */
export function getEmployeeDetail(id) {
  return request({
    url: `/organization/employee/${id}`,
    method: 'get'
  });
}

/**
 * 新增员工
 * @param {Object} data - 员工数据
 * @returns {Promise} 请求结果
 */
export function addEmployee(data) {
  return request({
    url: '/organization/employee',
    method: 'post',
    data
  });
}

/**
 * 更新员工
 * @param {Object} data - 员工数据
 * @returns {Promise} 请求结果
 */
export function updateEmployee(data) {
  return request({
    url: '/organization/employee',
    method: 'put',
    data
  });
}

/**
 * 删除员工
 * @param {Number} id - 员工ID
 * @returns {Promise} 请求结果
 */
export function deleteEmployee(id) {
  return request({
    url: `/organization/employee/${id}`,
    method: 'delete'
  });
}

/**
 * 更新员工状态
 * @param {Number} id - 员工ID
 * @param {String} status - 状态(1:在职 0:离职)
 * @returns {Promise} 请求结果
 */
export function updateEmployeeStatus(id, status) {
  return request({
    url: '/organization/employee/status',
    method: 'put',
    data: { id, status }
  });
}

/**
 * 更新员工小程序激活状态
 * @param {Number} id - 员工ID
 * @param {Boolean} isActivated - 激活状态
 * @returns {Promise} 请求结果
 */
export function updateEmployeeActivation(id, isActivated) {
  return request({
    url: '/organization/employee/activation',
    method: 'put',
    data: { id, isActivated }
  });
}

/**
 * 下载员工导入模板
 * @returns {Promise} 请求结果
 */
export function downloadEmployeeTemplate() {
  return request({
    url: '/organization/employee/template',
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 批量导入员工
 * @param {FormData} formData - 包含Excel文件的表单数据
 * @returns {Promise} 请求结果
 */
export function importEmployees(formData) {
  return request({
    url: '/organization/employee/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 导出员工数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function exportEmployees(params) {
  return request({
    url: '/organization/employee/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 获取员工的岗位配置列表
 * @param {Number} employeeId - 员工ID
 * @returns {Promise} 请求结果
 */
export function getEmployeePositions(employeeId) {
  return request({
    url: `/organization/employee/${employeeId}/positions`,
    method: 'get'
  });
}

/**
 * 更新员工的岗位配置
 * @param {Number} employeeId - 员工ID
 * @param {Array} positions - 岗位配置数组
 * @returns {Promise} 请求结果
 */
export function updateEmployeePositions(employeeId, positions) {
  return request({
    url: `/organization/employee/${employeeId}/positions`,
    method: 'put',
    data: { positions }
  });
}

/**
 * 添加员工岗位配置
 * @param {Object} data - 岗位配置数据
 * @returns {Promise} 请求结果
 */
export function addEmployeePosition(data) {
  return request({
    url: '/organization/employee/position',
    method: 'post',
    data
  });
}

/**
 * 删除员工岗位配置
 * @param {Number} id - 岗位配置ID
 * @returns {Promise} 请求结果
 */
export function deleteEmployeePosition(id) {
  return request({
    url: `/organization/employee/position/${id}`,
    method: 'delete'
  });
}

/**
 * 获取员工企业申请列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getEmployeeApplicationList(params) {
  return request({
    url: '/organization/employee/application/list',
    method: 'get',
    params
  });
}

/**
 * 获取员工企业申请详情
 * @param {Number} id - 申请ID
 * @returns {Promise} 请求结果
 */
export function getEmployeeApplicationDetail(id) {
  return request({
    url: `/organization/employee/application/${id}`,
    method: 'get'
  });
}

/**
 * 审核员工企业申请
 * @param {Number} id - 申请ID
 * @param {Object} data - 审核数据 { auditStatus, auditRemark }
 * @returns {Promise} 请求结果
 */
export function auditEmployeeApplication(id, data) {
  return request({
    url: `/organization/employee/application/${id}/audit`,
    method: 'put',
    data
  });
}

/**
 * 批量审核员工企业申请
 * @param {Object} data - 批量审核数据 { ids, auditStatus, auditRemark }
 * @returns {Promise} 请求结果
 */
export function batchAuditEmployeeApplication(data) {
  return request({
    url: '/organization/employee/application/batch-audit',
    method: 'put',
    data
  });
}
