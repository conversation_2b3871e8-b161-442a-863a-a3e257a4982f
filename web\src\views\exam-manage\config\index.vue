<!-- 练考配置页面 -->
<template>
  <div class="page-container">
    <!-- 内容区域 -->
      <div class="tree-table-layout">
        <!-- 左侧树结构 -->
        <div class="tree-container">
          <position-structure-tree
            title="岗位结构"
            :default-expanded-keys="['type-1', 'type-2']"
            all-node-title="全部"
            @select="handleTreeSelect"
          />
        </div>
        <!-- 右侧表格 -->
        <div class="table-container">
          <exam-config ref="examConfigRef" :selected-node="selectedNode" />
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ExamConfig from '../components/ExamConfig.vue';
import PositionStructureTree from '@/components/PositionStructureTree.vue';

// 当前选中的树节点
const selectedNode = ref({
  type: 'all',
  key: 'all',
  title: '全部'
});
const examConfigRef = ref(null);

// 处理树节点选择
const handleTreeSelect = (nodeData) => {
  console.log('选择树节点:', nodeData);
  selectedNode.value = nodeData;
  
  // 确保 examConfigRef 已经加载
  if (examConfigRef.value) {
    // 重置分页然后执行搜索
    examConfigRef.value.resetPagination?.();
    
    // 根据节点类型构建不同的搜索参数
    const searchParams = {};
    
    if (nodeData.type === 'all') {
      // 如果是全部节点，不传递筛选参数，所有参数设为空
      searchParams.positionBelong = '';
      searchParams.positionName = '';
      searchParams.level = '';
    } else if (nodeData.type === 'category') {
      // 如果是岗位类型节点，只传递positionBelong参数，其他参数设为空
      searchParams.positionBelong = nodeData.category || nodeData.typeId;
      searchParams.positionName = '';
      searchParams.level = '';
    } else if (nodeData.type === 'position') {
      // 如果是岗位名称节点，传递positionBelong和positionName参数，level参数设为空
      searchParams.positionBelong = nodeData.category || nodeData.typeId;
      searchParams.positionName = nodeData.position || nodeData.title;
      searchParams.level = '';
    } else if (nodeData.type === 'level') {
      // 如果是岗位等级节点，传递positionBelong、positionName和level参数
      searchParams.positionBelong = nodeData.category || nodeData.typeId;
      searchParams.positionName = nodeData.position || nodeData.positionName;
      searchParams.level = nodeData.level || nodeData.levelId;
    }
    
    // 执行搜索，确保传递完整的参数对象
    examConfigRef.value.handleSearch(searchParams);
  }
};
</script>

<style scoped>


.tree-table-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.tree-container {
  width: 230px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  padding: 16px;
  overflow: auto;
  height: calc(100vh - 180px);
}

.table-container {
  flex: 1;
  overflow: hidden;
  height: calc(100vh - 180px);
}

/* 全局按钮样式 */
:deep(.ant-btn-primary) {
  background: #a18cd1;
  border-color: #a18cd1;
}

:deep(.ant-btn-primary:hover),
:deep(.ant-btn-primary:focus) {
  background: #9370db;
  border-color: #9370db;
}

/* 全局表格样式 */
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-select-selector:hover),
:deep(.ant-input-affix-wrapper:hover),
:deep(.ant-input-affix-wrapper-focused),
:deep(.ant-input:hover),
:deep(.ant-input:focus) {
  border-color: #a18cd1 !important;
  box-shadow: 0 0 0 2px rgba(161, 140, 209, 0.2) !important;
}

:deep(.ant-pagination-item-active) {
  border-color: #a18cd1 !important;
}
</style> 