/**
 * 知识库文档工具函数
 */
export const useDocumentUtils = () => {
  /**
   * 格式化文件大小
   * @param {number} size 文件大小（字节）
   * @returns {string} 格式化后的文件大小
   */
  const formatFileSize = (size) => {
    if (!size) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let unitIndex = 0;
    let fileSize = size;
    
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }
    
    return `${fileSize.toFixed(2)} ${units[unitIndex]}`;
  };

  /**
   * 格式化日期时间
   * @param {string} dateString 日期字符串
   * @param {boolean} includeTime 是否包含时间
   * @returns {string} 格式化后的日期时间
   */
  const formatDate = (dateString, includeTime = true) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '无效日期';
    
    // 使用自定义格式化，确保yyyy-MM-dd HH:mm:ss格式
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    let result = `${year}-${month}-${day}`;
    
    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      result += ` ${hours}:${minutes}:${seconds}`;
    }
    
    return result;
  };

  /**
   * 获取文件类型的图标名称
   * @param {string} fileType 文件类型
   * @returns {string} 图标名称
   */
  const getFileIconName = (fileType) => {
    const iconMap = {
      'pdf': 'pdf',
      'word': 'word',
      'excel': 'excel',
      'ppt': 'ppt',
      'image': 'image',
      'text': 'text',
      'video': 'video',
      'audio': 'audio',
      'archive': 'archive'
    };
    
    return `file-${iconMap[fileType] || 'unknown'}-outlined`;
  };

  /**
   * 获取文件类型的颜色
   * @param {string} fileType 文件类型
   * @returns {string} 颜色代码
   */
  const getFileTypeColor = (fileType) => {
    const colorMap = {
      'pdf': '#f56c6c',
      'word': '#409eff',
      'excel': '#67c23a',
      'ppt': '#e6a23c',
      'image': '#9c27b0',
      'text': '#607d8b',
      'video': '#ff5722',
      'audio': '#ff9800',
      'archive': '#795548'
    };
    
    return colorMap[fileType] || '#909399';
  };

  /**
   * 获取分类标签
   * @param {string} categoryValue 分类值
   * @returns {string} 分类标签
   */
  const getCategoryLabel = (categoryValue) => {
    const categories = {
      'company_policy': '公司制度',
      'tech_doc': '技术文档',
      'training': '培训资料',
      'meeting': '会议记录',
      'other': '其他'
    };
    
    return categories[categoryValue] || categoryValue || '未分类';
  };

  /**
   * 获取位置标签
   * @param {string} positionValue 位置值
   * @returns {string} 位置标签
   */
  const getPositionLabel = (positionValue) => {
    const positions = {
      'hr': '人事部',
      'tech': '技术部',
      'marketing': '市场部',
      'finance': '财务部',
      'management': '总经办'
    };
    
    return positions[positionValue] || positionValue || '未知位置';
  };

  /**
   * 获取文件类型标签
   * @param {string} fileTypeValue 文件类型值
   * @returns {string} 文件类型标签
   */
  const getFileTypeLabel = (fileTypeValue) => {
    const fileTypes = {
      'pdf': 'PDF文件',
      'word': 'Word文档',
      'excel': 'Excel表格',
      'ppt': 'PPT演示',
      'image': '图片',
      'text': '文本文件',
      'video': '视频',
      'audio': '音频',
      'archive': '压缩包'
    };
    
    return fileTypes[fileTypeValue] || fileTypeValue || '未知类型';
  };

  /**
   * 获取文件扩展名对应的类型
   * @param {string} extension 文件扩展名
   * @returns {string} 文件类型
   */
  const getFileTypeByExtension = (extension) => {
    if (!extension) return 'unknown';
    
    const ext = extension.toLowerCase().replace('.', '');
    const typeMap = {
      // PDF
      'pdf': 'pdf',
      
      // Word
      'doc': 'word',
      'docx': 'word',
      'rtf': 'word',
      
      // Excel
      'xls': 'excel',
      'xlsx': 'excel',
      'csv': 'excel',
      
      // PowerPoint
      'ppt': 'ppt',
      'pptx': 'ppt',
      
      // Images
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'bmp': 'image',
      'svg': 'image',
      
      // Text
      'txt': 'text',
      'md': 'text',
      'json': 'text',
      'xml': 'text',
      'html': 'text',
      'css': 'text',
      'js': 'text',
      
      // Video
      'mp4': 'video',
      'avi': 'video',
      'mov': 'video',
      'wmv': 'video',
      
      // Audio
      'mp3': 'audio',
      'wav': 'audio',
      'ogg': 'audio',
      
      // Archives
      'zip': 'archive',
      'rar': 'archive',
      '7z': 'archive',
      'tar': 'archive',
      'gz': 'archive'
    };
    
    return typeMap[ext] || 'unknown';
  };

  /**
   * 从文件名获取文件类型
   * @param {string} filename 文件名
   * @returns {string} 文件类型
   */
  const getFileTypeFromFilename = (filename) => {
    if (!filename) return 'unknown';
    
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) return 'unknown';
    
    const extension = filename.substr(lastDotIndex + 1);
    return getFileTypeByExtension(extension);
  };

  return {
    formatFileSize,
    formatDate,
    getFileIconName,
    getFileTypeColor,
    getCategoryLabel,
    getPositionLabel,
    getFileTypeLabel,
    getFileTypeByExtension,
    getFileTypeFromFilename
  };
}; 