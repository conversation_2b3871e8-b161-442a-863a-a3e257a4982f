export const promotionConfigs = [
  {
    id: 1,
    category: '前厅',
    positionName: '前厅经理',
    positionLevel: '管理级',
    certificateName: '管理能力认证',
    fileName: 'management_ability.pdf',
    status: '必考',
    validDays: 365,
    createTime: '2023-02-10 09:00:00',
    updateTime: '2023-04-15 10:30:00'
  },
  {
    id: 2,
    category: '前厅',
    positionName: '前厅经理',
    positionLevel: '管理级',
    certificateName: '团队领导力',
    fileName: 'team_leadership.pdf',
    status: '必练',
    validDays: null,
    createTime: '2023-02-10 09:15:00',
    updateTime: '2023-02-10 09:15:00'
  },
  {
    id: 3,
    category: '前厅',
    positionName: '收银员',
    positionLevel: '中级',
    certificateName: '收银操作认证',
    fileName: 'cashier_operation.pdf',
    status: '必考',
    validDays: 180,
    createTime: '2023-02-10 09:30:00',
    updateTime: '2023-03-20 14:00:00'
  },
  {
    id: 4,
    category: '前厅',
    positionName: '迎宾员',
    positionLevel: '初级',
    certificateName: '礼仪接待标准',
    fileName: 'etiquette_standard.pdf',
    status: '必考',
    validDays: 180,
    createTime: '2023-02-10 09:45:00',
    updateTime: '2023-02-10 09:45:00'
  },
  {
    id: 5,
    category: '前厅',
    positionName: '服务员',
    positionLevel: '初级',
    certificateName: '服务流程认证',
    fileName: 'service_process.pdf',
    status: '必考',
    validDays: 180,
    createTime: '2023-02-10 10:00:00',
    updateTime: '2023-05-05 11:20:00'
  },
  {
    id: 6,
    category: '前厅',
    positionName: '服务员',
    positionLevel: '初级',
    certificateName: '投诉处理技巧',
    fileName: 'complaint_handling.pdf',
    status: '必练',
    validDays: null,
    createTime: '2023-02-10 10:15:00',
    updateTime: '2023-02-10 10:15:00'
  },
  {
    id: 7,
    category: '后厨',
    positionName: '厨师长',
    positionLevel: '管理级',
    certificateName: '厨房管理认证',
    fileName: 'kitchen_management.pdf',
    status: '必考',
    validDays: 365,
    createTime: '2023-02-10 10:30:00',
    updateTime: '2023-02-10 10:30:00'
  },
  {
    id: 8,
    category: '后厨',
    positionName: '厨师长',
    positionLevel: '管理级',
    certificateName: '食品安全管理',
    fileName: 'food_safety_management.pdf',
    status: '必考',
    validDays: 365,
    createTime: '2023-02-10 10:45:00',
    updateTime: '2023-04-25 16:00:00'
  },
  {
    id: 9,
    category: '后厨',
    positionName: '厨师',
    positionLevel: '中级',
    certificateName: '烹饪技能认证',
    fileName: 'cooking_skills.pdf',
    status: '必考',
    validDays: 180,
    createTime: '2023-02-10 11:00:00',
    updateTime: '2023-02-10 11:00:00'
  },
  {
    id: 10,
    category: '后厨',
    positionName: '厨师',
    positionLevel: '中级',
    certificateName: '刀工技巧',
    fileName: 'knife_skills.pdf',
    status: '必练',
    validDays: null,
    createTime: '2023-02-10 11:15:00',
    updateTime: '2023-02-10 11:15:00'
  },
  {
    id: 11,
    category: '后厨',
    positionName: '配菜员',
    positionLevel: '初级',
    certificateName: '食材处理标准',
    fileName: 'ingredients_processing.pdf',
    status: '必考',
    validDays: 180,
    createTime: '2023-02-10 11:30:00',
    updateTime: '2023-03-30 09:45:00'
  },
  {
    id: 12,
    category: '后厨',
    positionName: '面点师',
    positionLevel: '专家级',
    certificateName: '面点制作技术',
    fileName: 'pastry_techniques.pdf',
    status: '必考',
    validDays: 365,
    createTime: '2023-02-10 11:45:00',
    updateTime: '2023-02-10 11:45:00'
  }
]; 