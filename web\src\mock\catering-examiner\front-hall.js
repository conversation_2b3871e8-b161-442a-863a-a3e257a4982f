// 前厅餐考师的mock数据
export const frontHallExaminers = [
  {
    id: 1,
    positionCategory: '前厅',
    positionName: '服务员',
    positionLevel: '初级',
    employeeName: '张三',
    title: '顾客对菜品的反馈',
    chatSummary: '顾客反映主食甜度过高，建议改善',
    createTime: '2023-05-10 14:30:22',
    updateTime: '2023-05-10 18:45:36',
    messageCount: 12
  },
  {
    id: 2,
    positionCategory: '前厅',
    positionName: '接待员',
    positionLevel: '中级',
    employeeName: '李四',
    title: '关于门店环境的讨论',
    chatSummary: '员工提出改善门店照明和音乐氛围的建议',
    createTime: '2023-06-15 09:12:45',
    updateTime: '2023-06-15 16:20:30',
    messageCount: 8
  },
  {
    id: 3,
    positionCategory: '前厅',
    positionName: '收银员',
    positionLevel: '高级',
    employeeName: '王五',
    title: '促销活动反馈',
    chatSummary: '讨论了最新促销活动的客户接受度和改进方向',
    createTime: '2023-07-22 11:05:18',
    updateTime: '2023-07-22 17:40:12',
    messageCount: 15
  },
  {
    id: 4,
    positionCategory: '前厅',
    positionName: '大堂经理',
    positionLevel: '专家',
    employeeName: '赵六',
    title: '顾客投诉处理',
    chatSummary: '探讨了一次顾客投诉的处理方法和后续改进措施',
    createTime: '2023-08-05 10:30:42',
    updateTime: '2023-08-05 19:15:27',
    messageCount: 20
  },
  {
    id: 5,
    positionCategory: '前厅',
    positionName: '服务员',
    positionLevel: '初级',
    employeeName: '孙七',
    title: '服务流程改进',
    chatSummary: '讨论了点餐服务流程的潜在改进点',
    createTime: '2023-09-18 08:45:33',
    updateTime: '2023-09-18 15:30:25',
    messageCount: 10
  }
];

// 前厅餐考师对话详情
export const frontHallChats = {
  1: [
    { id: 1, sender: '张三', role: '服务员', content: '您好餐考师，最近有顾客反映我们的主食甜度有点高，特别是甜点类的，这个问题应该如何处理？', time: '2023-05-10 14:30:22' },
    { id: 2, sender: '餐考师', role: '前厅餐考师', content: '您好张三，关于顾客对甜度的反馈，这是很有价值的信息。您可以先收集更多顾客的意见，看是否是普遍现象，然后向厨房反馈。', time: '2023-05-10 14:35:15' },
    { id: 3, sender: '张三', role: '服务员', content: '我已经收集了10位顾客的反馈，有8位认为甜度过高，尤其是双皮奶和提拉米苏。', time: '2023-05-10 14:40:33' },
    { id: 4, sender: '餐考师', role: '前厅餐考师', content: '这确实是需要关注的问题。建议您可以：1. 将具体反馈传达给后厨；2. 建议后厨适当降低甜度；3. 可以考虑在菜单上标注甜度等级，让顾客有所选择。', time: '2023-05-10 14:45:42' },
    { id: 5, sender: '张三', role: '服务员', content: '明白了，我会按照您的建议执行。另外，我们是否可以增加一些低糖甜点选择？', time: '2023-05-10 14:50:18' },
    { id: 6, sender: '餐考师', role: '前厅餐考师', content: '这是个很好的想法！增加低糖甜点不仅能满足对甜度敏感的顾客，也能吸引健康饮食人群。建议您可以向管理层提出这个建议，并提供一些市场上流行的低糖甜点餐考。', time: '2023-05-10 14:55:30' },
    { id: 7, sender: '张三', role: '服务员', content: '感谢您的指导，我会整理一份低糖甜点的提案，包括市场分析和潜在供应商。', time: '2023-05-10 15:00:12' },
    { id: 8, sender: '餐考师', role: '前厅餐考师', content: '非常好！这种主动思考和解决问题的态度非常值得赞赏。如果需要帮助编写提案或收集资料，随时可以来咨询我。', time: '2023-05-10 15:05:45' },
    { id: 9, sender: '张三', role: '服务员', content: '好的，谢谢您。我会在下周之前完成提案，到时再向您请教。', time: '2023-05-10 15:10:33' },
    { id: 10, sender: '餐考师', role: '前厅餐考师', content: '期待看到您的提案，祝您工作顺利！', time: '2023-05-10 15:15:28' },
    { id: 11, sender: '张三', role: '服务员', content: '提案已经完成，我把电子版发给您餐考了，您有时间吗？', time: '2023-05-10 18:30:15' },
    { id: 12, sender: '餐考师', role: '前厅餐考师', content: '已收到您的提案，内容很全面，尤其是市场调研部分做得很好。建议您在成本分析部分再详细一些，增加实施后的预期收益分析。其他方面都很出色！', time: '2023-05-10 18:45:36' }
  ],
  // 其他对话记录...
}; 