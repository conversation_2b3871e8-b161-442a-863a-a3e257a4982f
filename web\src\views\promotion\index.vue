<template>
  <div class="page-container">
    <page-header 
    >
      <template #search>
        <search-form-card
        :model-value="searchForm"
        :items="searchFormItems"
        :key="formKey"
        @search="handleSearch"
        @reset="resetSearch"
      />
      </template>
      <template #actions>
        <a-button type="primary" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          新增
        </a-button>
      </template>
    </page-header>
    
    
    <!-- 列表区域 -->
    <base-table
      :columns="columns"
      :data-source="filteredPromotions"
      :loading="loading"
      @edit="handleEdit"
      :action-config="actionConfig"
      :pagination="pagination"
      @change="handleTableChange"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'category'">
          <a-tag :color="record.category === '前厅' ? '#fbc2eb' : '#a18cd1'">
            {{ record.category }}
          </a-tag>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status === '必考' ? '#f5222d' : '#52c41a'">
            {{ record.status }}
          </a-tag>
        </template>
        <template v-if="column.key === 'validDays'">
          <span v-if="record.validDays">{{ record.validDays }}天</span>
          <span v-else>-</span>
        </template>
      </template>
    </base-table>
    
    <!-- 添加/编辑晋升配置弹窗 -->
    <a-modal
      :title="modalType === 'add' ? '新增晋升配置' : '编辑晋升配置'"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="700px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="岗位名称" name="positionName">
              <a-select
                v-model:value="formData.positionName"
                placeholder="请选择岗位名称"
                @change="handlePositionChange"
              >
                <a-select-option v-for="pos in positions" :key="pos.id" :value="pos.name">
                  {{ pos.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="岗位等级" name="positionLevel">
              <a-select
                v-model:value="formData.positionLevel"
                placeholder="请选择岗位等级"
              >
                <a-select-option v-for="level in positionLevels" :key="level.id" :value="level.level">
                  {{ level.level }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <div class="certificate-setting-section">
          <div class="certificate-setting-title">证书设置</div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="文件名称" name="fileName">
                <a-select
                  v-model:value="formData.fileName"
                  placeholder="请选择文件名称"
                  @change="handleFileNameChange"
                >
                  <a-select-option v-for="(file, index) in fileList" :key="index" :value="file">
                    {{ file }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="状态" name="status">
                <a-select
                  v-model:value="formData.status"
                  placeholder="请选择状态"
                  @change="handleStatusChange"
                >
                  <a-select-option value="必考">必考</a-select-option>
                  <a-select-option value="必练">必练</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item 
                label="证书名称" 
                name="certificateName"
                :rules="formData.status === '必考' ? [{ required: true, message: '必考状态下证书名称必填', trigger: 'blur' }] : []"
              >
                <a-input v-model:value="formData.certificateName" placeholder="请输入证书名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item 
                label="证书有效时间（天）" 
                name="validDays"
                :rules="formData.status === '必考' ? [{ required: true, message: '必考状态下证书有效时间必填', trigger: 'blur' }] : []"
              >
                <a-input-number 
                  v-model:value="formData.validDays" 
                  placeholder="请输入天数" 
                  :min="1" 
                  :disabled="formData.status !== '必考'"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { promotionConfigs } from '@/mock/promotion';
import { positions } from '@/mock/position-management';
import { positionLevels } from '@/mock/position-level';
import { SearchFormCard } from '@/components/SearchForm';

import { useTablePagination } from '@/utils/common';

// 模拟文件列表
const fileList = [
  'management_ability.pdf',
  'team_leadership.pdf',
  'cashier_operation.pdf',
  'etiquette_standard.pdf',
  'service_process.pdf',
  'complaint_handling.pdf',
  'kitchen_management.pdf',
  'food_safety_management.pdf',
  'cooking_skills.pdf',
  'knife_skills.pdf',
  'ingredients_processing.pdf',
  'pastry_techniques.pdf'
];

const actionConfig = {
  edit: true
}

// 添加key值用于强制重新渲染搜索表单
const formKey = ref(0);

// 查询表单
const searchForm = reactive({
  category: undefined,
  positionName: undefined,
  positionLevel: undefined,
  status: undefined,
  certificateName: '',
  sortField: '',
  sortOrder: ''
});

// 根据岗位类型筛选岗位
const filteredPositionsByCategory = computed(() => {
  if (!searchForm.category) {
    return positions;
  }
  return positions.filter(item => item.category === searchForm.category);
});

// 表格列定义
const columns = [
  {
    title: '岗位类型',
    dataIndex: 'category',
    key: 'category',
    sorter: true
  },
  {
    title: '岗位名称',
    dataIndex: 'positionName',
    key: 'positionName',
    sorter: true
  },
  {
    title: '岗位等级',
    dataIndex: 'positionLevel',
    key: 'positionLevel',
    sorter: true
  },
  {
    title: '证书名称',
    dataIndex: 'certificateName',
    key: 'certificateName',
    sorter: true
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    sorter: true
  },
  {
    title: '证书有效时间',
    dataIndex: 'validDays',
    key: 'validDays',
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
  },
];

// 数据与状态
const loading = ref(false);
const dataSource = ref([...promotionConfigs]);

// 获取晋升配置数据
const fetchPromotionData = () => {
  loading.value = true;
  
  // 使用setTimeout模拟异步请求
  setTimeout(() => {
    // 根据排序条件对数据排序
    let sortedData = [...promotionConfigs];
    
    if (searchForm.sortField && searchForm.sortOrder) {
      const field = searchForm.sortField;
      const order = searchForm.sortOrder === 'ascend' ? 1 : -1;
      
      sortedData.sort((a, b) => {
        if (a[field] < b[field]) return -1 * order;
        if (a[field] > b[field]) return 1 * order;
        return 0;
      });
    }
    
    dataSource.value = sortedData;
    loading.value = false;
    
    // 更新分页信息
    updatePagination({
      total: filteredPromotions.value.length,
      current: pagination.current,
      pageSize: pagination.pageSize
    });
  }, 500);
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchPromotionData,
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showTotal: (total) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
  searchForm
});

// 根据查询条件过滤晋升配置
const filteredPromotions = computed(() => {
  let filtered = dataSource.value;
  
  if (searchForm.category) {
    filtered = filtered.filter(item => item.category === searchForm.category);
  }
  
  if (searchForm.positionName) {
    filtered = filtered.filter(item => item.positionName === searchForm.positionName);
  }
  
  if (searchForm.positionLevel) {
    filtered = filtered.filter(item => item.positionLevel === searchForm.positionLevel);
  }
  
  if (searchForm.status) {
    filtered = filtered.filter(item => item.status === searchForm.status);
  }
  
  if (searchForm.certificateName) {
    filtered = filtered.filter(item => 
      item.certificateName && item.certificateName.includes(searchForm.certificateName)
    );
  }
  
  return filtered;
});

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formData = reactive({
  id: null,
  category: '',
  positionName: undefined,
  positionLevel: undefined,
  certificateName: '',
  fileName: undefined,
  status: undefined,
  validDays: undefined
});

const formRules = {
  positionName: [
    { required: true, message: '请选择岗位名称', trigger: 'change' }
  ],
  positionLevel: [
    { required: true, message: '请选择岗位等级', trigger: 'change' }
  ],
  fileName: [
    { required: true, message: '请选择文件名称', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 查询
const handleSearch = (values) => {
  console.log('执行搜索，表单值:', values);
  // 如果values不为空，使用values更新searchForm
  if (values) {
    // 保留排序设置
    const { sortField, sortOrder } = searchForm;
    Object.assign(searchForm, values, { sortField, sortOrder });
  }
  // 重置到第一页
  resetPagination();
  fetchPromotionData();
};

// 重置搜索
const resetSearch = () => {
  console.log('重置搜索表单');
  // 重置所有搜索字段
  searchForm.category = undefined;
  searchForm.positionName = undefined;
  searchForm.positionLevel = undefined;
  searchForm.status = undefined;
  searchForm.certificateName = '';
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  
  // 重置到第一页
  resetPagination();
  fetchPromotionData();
  
  // 增加key值触发搜索表单重新渲染
  formKey.value += 1;
};

// 当选择岗位时，自动设置岗位类型
const handlePositionChange = (value) => {
  const selectedPosition = positions.find(item => item.name === value);
  if (selectedPosition) {
    formData.category = selectedPosition.category;
  }
};

// 当选择文件名称时，默认设置证书名称
const handleFileNameChange = (value) => {
  if (value) {
    // 将文件名转换为证书名称（去掉扩展名并替换下划线为空格）
    const baseName = value.split('.')[0];
    const certificateName = baseName.replace(/_/g, ' ');
    
    // 首字母大写
    formData.certificateName = certificateName
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
};

// 当状态变更时，处理相关字段
const handleStatusChange = (value) => {
  if (value === '必练') {
    formData.validDays = undefined;
  }
};

// 显示添加模态框
const showAddModal = () => {
  modalType.value = 'add';
  formData.id = null;
  formData.category = '';
  formData.positionName = undefined;
  formData.positionLevel = undefined;
  formData.certificateName = '';
  formData.fileName = undefined;
  formData.status = undefined;
  formData.validDays = undefined;
  modalVisible.value = true;
};

// 编辑
const handleEdit = (record) => {
  modalType.value = 'edit';
  formData.id = record.id;
  formData.category = record.category;
  formData.positionName = record.positionName;
  formData.positionLevel = record.positionLevel;
  formData.certificateName = record.certificateName;
  formData.fileName = record.fileName;
  formData.status = record.status;
  formData.validDays = record.validDays;
  modalVisible.value = true;
};

// 确认添加/编辑
const handleOk = () => {
  formRef.value.validate().then(() => {
    modalLoading.value = true;
    
    setTimeout(() => {
      if (modalType.value === 'add') {
        // 模拟添加
        const newId = Math.max(...dataSource.value.map(item => item.id)) + 1;
        const now = new Date().toLocaleString();
        const newRecord = {
          id: newId,
          category: formData.category,
          positionName: formData.positionName,
          positionLevel: formData.positionLevel,
          certificateName: formData.certificateName,
          fileName: formData.fileName,
          status: formData.status,
          validDays: formData.status === '必考' ? formData.validDays : null,
          createTime: now,
          updateTime: now
        };
        dataSource.value.push(newRecord);
        message.success('添加成功');
      } else {
        // 模拟编辑
        const index = dataSource.value.findIndex(item => item.id === formData.id);
        if (index !== -1) {
          const now = new Date().toLocaleString();
          dataSource.value[index] = {
            ...dataSource.value[index],
            category: formData.category,
            positionName: formData.positionName,
            positionLevel: formData.positionLevel,
            certificateName: formData.certificateName,
            fileName: formData.fileName,
            status: formData.status,
            validDays: formData.status === '必考' ? formData.validDays : null,
            updateTime: now
          };
          message.success('编辑成功');
        }
      }
      
      modalLoading.value = false;
      modalVisible.value = false;
      
      // 刷新数据
      fetchPromotionData();
    }, 500);
  }).catch(error => {
    console.log('验证失败', error);
  });
};

// 取消添加/编辑
const handleCancel = () => {
  modalVisible.value = false;
};

// 搜索表单配置
const searchFormItems = computed(() => {
  const positionOptions = filteredPositionsByCategory.value && filteredPositionsByCategory.value.length ? 
    filteredPositionsByCategory.value.map(pos => ({ label: pos.name, value: pos.name })) : [];
  
  const levelOptions = positionLevels && positionLevels.length ? 
    positionLevels.map(level => ({ label: level.level, value: level.level })) : [];
  
  return [
    {
      label: '岗位类型',
      field: 'category',
      type: 'select',
      placeholder: '请选择岗位类型',
      width: '160px',
      options: [
        { label: '前厅', value: '前厅' },
        { label: '后厨', value: '后厨' }
      ]
    },
    {
      label: '岗位名称',
      field: 'positionName',
      type: 'select',
      placeholder: '请选择岗位名称',
      width: '160px',
      options: positionOptions
    },
    {
      label: '岗位等级',
      field: 'positionLevel',
      type: 'select',
      placeholder: '请选择岗位等级',
      width: '160px',
      options: levelOptions
    },
    {
      label: '状态',
      field: 'status',
      type: 'select',
      placeholder: '请选择状态',
      width: '160px',
      options: [
        { label: '必考', value: '必考' },
        { label: '必练', value: '必练' }
      ]
    },
    {
      label: '证书名称',
      field: 'certificateName',
      type: 'input',
      placeholder: '请输入证书名称'
    }
  ];
});

// 初始化
onMounted(() => {
  fetchPromotionData();
});
</script>

<style scoped>
.certificate-setting-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.certificate-setting-title {
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}
</style> 