import request from '@/utils/request';

/**
 * 获取部门树形结构
 * @returns {Promise} 请求结果
 */
export function getDepartmentTree() {
  return request({
    url: '/organization/department/tree',
    method: 'get'
  });
}

/**
 * 获取部门列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getDepartmentList(params) {
  return request({
    url: '/organization/department/list',
    method: 'get',
    params
  });
}

/**
 * 获取部门详情
 * @param {Number} id - 部门ID
 * @returns {Promise} 请求结果
 */
export function getDepartmentDetail(id) {
  return request({
    url: `/organization/department/${id}`,
    method: 'get'
  });
}

/**
 * 新增部门
 * @param {Object} data - 部门数据
 * @returns {Promise} 请求结果
 */
export function addDepartment(data) {
  return request({
    url: '/organization/department',
    method: 'post',
    data
  });
}

/**
 * 更新部门
 * @param {Object} data - 部门数据
 * @returns {Promise} 请求结果
 */
export function updateDepartment(data) {
  return request({
    url: '/organization/department',
    method: 'put',
    data
  });
}

/**
 * 删除部门
 * @param {Number} id - 部门ID
 * @returns {Promise} 请求结果
 */
export function deleteDepartment(id) {
  return request({
    url: `/organization/department/${id}`,
    method: 'delete'
  });
}

/**
 * 更新部门状态
 * @param {Number} id - 部门ID
 * @param {Boolean} status - 状态
 * @returns {Promise} 请求结果
 */
export function updateDepartmentStatus(id, status) {
  return request({
    url: '/organization/department/status',
    method: 'put',
    data: { id, status }
  });
} 