import request from '@/utils/request';

/**
 * 获取岗位等级列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getLevelList(params) {
  return request({
    url: '/organization/level/list',
    method: 'get',
    params
  });
}

/**
 * 获取岗位等级选项（下拉列表用）
 * @returns {Promise} 请求结果
 */
export function getLevelOptions() {
  return request({
    url: '/organization/level/options',
    method: 'get'
  });
}

/**
 * 获取岗位等级详情
 * @param {Number} id - 岗位等级ID
 * @returns {Promise} 请求结果
 */
export function getLevelDetail(id) {
  return request({
    url: `/organization/level/${id}`,
    method: 'get'
  });
}

/**
 * 新增岗位等级
 * @param {Object} data - 岗位等级数据
 * @returns {Promise} 请求结果
 */
export function addLevel(data) {
  return request({
    url: '/organization/level',
    method: 'post',
    data
  });
}

/**
 * 更新岗位等级
 * @param {Object} data - 岗位等级数据
 * @returns {Promise} 请求结果
 */
export function updateLevel(data) {
  return request({
    url: '/organization/level',
    method: 'put',
    data
  });
}

/**
 * 删除岗位等级
 * @param {Number} id - 岗位等级ID
 * @returns {Promise} 请求结果
 */
export function deleteLevel(id) {
  return request({
    url: `/organization/level/${id}`,
    method: 'delete'
  });
} 