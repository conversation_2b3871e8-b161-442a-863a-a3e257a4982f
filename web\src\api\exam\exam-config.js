import request from '@/utils/request';

// 获取练考配置列表
export function getExamConfigs(params) {
  return request({
    url: '/exam/configs',
    method: 'get',
    params
  });
}

// 获取练考配置详情
export function getExamConfig(id) {
  return request({
    url: `/exam/configs/${id}`,
    method: 'get'
  });
}

// 新增练考配置
export function addExamConfig(data) {
  return request({
    url: '/exam/configs',
    method: 'post',
    data
  });
}

// 更新练考配置
export function updateExamConfig(data) {
  return request({
    url: `/exam/configs/${data.id}`,
    method: 'put',
    data
  });
}

// 删除练考配置
export function deleteExamConfig(id) {
  return request({
    url: `/exam/configs/${id}`,
    method: 'delete'
  });
}

// 获取考试规则
export function getExamRules(id) {
    // 获取特定配置的规则
    return request({
      url: `/exam/rules/global`,
      method: 'get',
      params: id ? { id } : undefined
    });
}

// 更新通用练习配置
export function updatePracticeRules(data) {
  return request({
    url: `/exam/rules/practice`,
    method: 'put',
    data
  });
}

// 更新通用考试配置
export function updateExamRules(data) {
    return request({
      url: `/exam/rules/exam`,
      method: 'put',
      data
    });
}

// 更新单个配置的练习规则
export function updateConfigPracticeRules(id, data) {
  return request({
    url: `/exam/configs/${id}/practice-rules`,
    method: 'put',
    data
  });
}

// 更新单个配置的考试规则
export function updateConfigExamRules(id, data) {
  return request({
    url: `/exam/configs/${id}/exam-rules`,
    method: 'put',
    data
  });
}

// 同步通用规则到所有必考配置
export function syncExamRules() {
  return request({
    url: `/exam/rules/sync`,
    method: 'post'
  });
}
