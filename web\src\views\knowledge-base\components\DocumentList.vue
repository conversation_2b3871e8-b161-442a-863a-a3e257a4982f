<template>
  <div class="document-list">
    <!-- 筛选条件 -->
    <div class="document-filter">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索文档名称或描述"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        <a-col :span="5">
          <a-select
            v-model:value="filterCategory"
            placeholder="选择文档分类"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option v-for="category in categories" :key="category.value" :value="category.value">
              {{ category.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="5">
          <a-select
            v-model:value="filterPosition"
            placeholder="选择文档位置"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option v-for="position in positions" :key="position.value" :value="position.value">
              {{ position.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="5">
          <a-select
            v-model:value="filterFileType"
            placeholder="选择文件类型"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option v-for="fileType in fileTypes" :key="fileType.value" :value="fileType.value">
              {{ fileType.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3" class="text-right">
          <a-button type="primary" @click="handleAddDocument">
            <plus-outlined /> 上传文档
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 文档列表 -->
    <div class="document-grid">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="document in filteredDocuments" :key="document.id">
          <document-card
            :document="document"
            @view="handleViewDocument"
            @download="handleDownloadDocument"
            @edit="handleEditDocument"
            @delete="handleDeleteDocument"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 空状态 -->
    <a-empty v-if="filteredDocuments.length === 0" description="暂无文档数据" />

    <!-- 分页 -->
    <div class="document-pagination">
      <a-pagination
        v-model:current="currentPage"
        :total="total"
        :page-size="pageSize"
        :page-size-options="['12', '24', '48', '96']"
        show-size-changer
        show-quick-jumper
        :show-total="total => `共 ${total} 条`"
        @change="handlePageChange"
        @showSizeChange="handlePageSizeChange"
      />
    </div>

    <!-- 文档预览抽屉 -->
    <view-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :document="currentDocument"
      @close="closeDrawer"
      @download="handleDownloadDocument"
      @edit="handleEditDocument"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import DocumentCard from './DocumentCard.vue';
import ViewDrawer from './ViewDrawer.vue';

// 分类选项
const categories = [
  { label: '公司制度', value: 'company_policy' },
  { label: '技术文档', value: 'tech_doc' },
  { label: '培训资料', value: 'training' },
  { label: '会议记录', value: 'meeting' },
  { label: '其他', value: 'other' }
];

// 位置选项
const positions = [
  { label: '人事部', value: 'hr' },
  { label: '技术部', value: 'tech' },
  { label: '市场部', value: 'marketing' },
  { label: '财务部', value: 'finance' },
  { label: '总经办', value: 'management' }
];

// 文件类型选项
const fileTypes = [
  { label: 'PDF文件', value: 'pdf' },
  { label: 'Word文档', value: 'word' },
  { label: 'Excel表格', value: 'excel' },
  { label: 'PPT演示', value: 'ppt' },
  { label: '图片', value: 'image' },
  { label: '文本文件', value: 'text' }
];

// 状态
const documents = ref([]);
const searchKeyword = ref('');
const filterCategory = ref(null);
const filterPosition = ref(null);
const filterFileType = ref(null);
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const loading = ref(false);
const drawerVisible = ref(false);
const currentDocument = ref(null);

// 模拟数据，实际项目中应从API获取
const fetchDocuments = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    documents.value = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      name: `文档${index + 1}-${Math.random().toString(36).substring(2, 8)}`,
      fileType: fileTypes[Math.floor(Math.random() * fileTypes.length)].value,
      fileSize: Math.floor(Math.random() * 10000000),
      category: categories[Math.floor(Math.random() * categories.length)].value,
      position: positions[Math.floor(Math.random() * positions.length)].value,
      remarks: Math.random() > 0.3 ? `这是一个示例文档描述，用于展示文档卡片组件的效果。文档ID: ${index + 1}` : '',
      createBy: `用户${Math.floor(Math.random() * 10) + 1}`,
      createTime: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
      updateTime: new Date(Date.now() - Math.floor(Math.random() * 1000000000)).toISOString(),
      fileUrl: 'https://example.com/document.pdf'
    }));
    
    total.value = documents.value.length;
  } catch (error) {
    message.error('获取文档列表失败');
    console.error('获取文档列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 筛选后的文档
const filteredDocuments = computed(() => {
  let result = [...documents.value];
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(doc => 
      doc.name.toLowerCase().includes(keyword) || 
      (doc.remarks && doc.remarks.toLowerCase().includes(keyword))
    );
  }
  
  // 分类筛选
  if (filterCategory.value) {
    result = result.filter(doc => doc.category === filterCategory.value);
  }
  
  // 位置筛选
  if (filterPosition.value) {
    result = result.filter(doc => doc.position === filterPosition.value);
  }
  
  // 文件类型筛选
  if (filterFileType.value) {
    result = result.filter(doc => doc.fileType === filterFileType.value);
  }
  
  // 分页处理
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  
  total.value = result.length;
  return result.slice(startIndex, endIndex);
});

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
};

// 处理筛选条件变化
const handleFilterChange = () => {
  currentPage.value = 1;
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 处理每页数量变化
const handlePageSizeChange = (current, size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 添加文档
const handleAddDocument = () => {
  // 实际项目中应打开上传文档对话框或导航到上传页面
  message.info('即将实现上传文档功能');
};

// 查看文档
const handleViewDocument = (document) => {
  currentDocument.value = document;
  drawerVisible.value = true;
};

// 下载文档
const handleDownloadDocument = (document) => {
  // 实际项目中应调用下载API
  message.success(`正在下载文档: ${document.name}`);
};

// 编辑文档
const handleEditDocument = (document) => {
  // 实际项目中应打开编辑对话框或导航到编辑页面
  message.info(`即将编辑文档: ${document.name}`);
};

// 删除文档
const handleDeleteDocument = (document) => {
  // 实际项目中应弹出确认对话框并调用删除API
  message.success(`已删除文档: ${document.name}`);
  documents.value = documents.value.filter(doc => doc.id !== document.id);
};

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
  currentDocument.value = null;
};

// 初始化
onMounted(() => {
  fetchDocuments();
});
</script>

<style lang="scss" scoped>
.document-list {
  .document-filter {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    
    .text-right {
      text-align: right;
    }
  }
  
  .document-grid {
    margin-bottom: 24px;
  }
  
  .document-pagination {
    text-align: right;
    margin-top: 24px;
  }
}
</style> 