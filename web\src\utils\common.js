import { reactive } from 'vue'

/**
 * 表格分页、排序处理的组合式函数
 * @param {Object} options 配置选项
 * @param {Function} options.fetchData 获取数据的函数
 * @param {Object} options.initialPagination 初始分页设置
 * @param {Object} options.searchForm 搜索表单对象(可选)
 * @returns {Object} 分页相关方法和数据
 */
export function useTablePagination(options) {
  const { 
    fetchData, 
    initialPagination = { current: 1, pageSize: 10, total: 0 },
    searchForm = null
  } = options

  // 分页信息
  const pagination = reactive({
    current: initialPagination.current,
    pageSize: initialPagination.pageSize,
    total: initialPagination.total
  })

  // 处理表格变化（分页、筛选、排序）
  const handleTableChange = (pag, filters, sorter) => {
    // 更新分页信息
    Object.assign(pagination, {
      current: pag.pagination && pag.pagination.current ? pag.pagination.current : 1,
      pageSize: pag.pagination && pag.pagination.pageSize ? pag.pagination.pageSize : 10
    })
    
    // 如果提供了searchForm且需要处理排序
    if (searchForm && sorter) {
      // 处理排序
      if (sorter.field) {
        searchForm.sortField = sorter.field
        searchForm.sortOrder = sorter.order // 'ascend' 或 'descend'
      } else {
        // 清除排序
        searchForm.sortField = ''
        searchForm.sortOrder = ''
      }
    }
    
    // 调用获取数据函数
    fetchData()
  }

  // 更新分页数据（用于API响应后更新）
  const updatePagination = (paginationData) => {
    if (paginationData) {
      Object.assign(pagination, {
        total: paginationData.total || 0,
        current: paginationData.current || paginationData.pageNum || pagination.current,
        pageSize: paginationData.pageSize || pagination.pageSize
      })
    }
  }

  // 重置到第一页
  const resetPagination = () => {
    pagination.current = 1
  }

  return {
    pagination,
    handleTableChange,
    updatePagination,
    resetPagination
  }
} 

/**
 * tooltip获取指定字符
 * @param {string} value 需要处理的文本
 * @param {number} number 最大显示字符数，默认为6
 * @returns {string} 处理后的文本，超长时添加省略号
 */
export function getToolTip(value, number = 6) {
  if (!value) return '';
  
  // 只有当文本长度超过指定限制时，才进行截取并添加省略号
  if (value.length > number) {
    return value.substring(0, number) + '...';
  }
  
  // 文本未超过限制，直接返回原值
  return value;
}
