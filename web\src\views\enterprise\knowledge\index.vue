<template>
  <div class="page-container">
    <div class="knowledge-list-container">
      <page-header 
    >
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="searchItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-button class="custom-button" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          新增知识库
        </a-button>
      </template>
    </page-header>
    
      
      <!-- 数据表格 -->
      <div class="table-box">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          bordered
          rowKey="id"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
        >
          <!-- 自定义列渲染 -->
          <template #bodyCell="{ column, record }">
            <!-- 状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="record.status ? 'success' : 'error'">
                {{ record.status ? '启用' : '禁用' }}
              </a-tag>
            </template>
            
            <!-- 文档数量列 -->
            <template v-if="column.dataIndex === 'documentCount'">
              <a-badge :count="record.documentCount" :numberStyle="{ backgroundColor: '#a18cd1' }" />
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.key === 'action'">
              <a-space size="small" class="action-buttons">
                <a-button type="link" size="small" @click="handleEdit(record)" class="action-button">编辑</a-button>
                <a-button type="link" size="small" @click="handleDocument(record)" class="action-button">文档管理</a-button>
                <a-button type="link" size="small" @click="handleToggleStatus(record)" class="action-button">
                  {{ record.status ? '禁用' : '启用' }}
                </a-button>
                <a-popconfirm
                  title="确定要删除此知识库吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" size="small" class="action-button">删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
      
      <!-- 新增/编辑对话框 -->
      <a-modal
        v-model:visible="modalVisible"
        :title="modalTitle"
        @ok="handleModalSubmit"
        @cancel="handleModalCancel"
        :confirm-loading="modalLoading"
      >
        <a-form
          :model="formData"
          :rules="formRules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="知识库名称" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入知识库名称" />
          </a-form-item>
          
          <a-form-item label="所属企业" name="enterpriseId">
            <a-select
              v-model:value="formData.enterpriseId"
              placeholder="请选择所属企业"
            >
              <a-select-option 
                v-for="item in enterpriseOptions" 
                :key="item.id" 
                :value="item.id"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="知识库描述" name="description">
            <a-textarea
              v-model:value="formData.description"
              placeholder="请输入知识库描述"
              :rows="4"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { 
  getKnowledgeList, 
  createKnowledge, 
  updateKnowledge, 
  deleteKnowledge 
} from '@/api/enterprise/knowledge';
import { getEnterpriseList } from '@/api/enterprise';
import { SearchFormCard } from '@/components/SearchForm';

const router = useRouter();
const route = useRoute();
const formRef = ref(null);

// 查询表单数据
const searchForm = reactive({
  name: '',
  enterpriseId: undefined,
  status: undefined
});

// 搜索表单项配置
const searchItems = [
  {
    label: '知识库名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入知识库名称'
  },
  {
    label: '所属企业',
    field: 'enterpriseId',
    type: 'select',
    width: '220px',
    options: computed(() => {
      const baseOption = [{ label: '全部', value: '' }];
      return baseOption.concat(enterpriseList.value.filter(item => item.status).map(item => ({
        label: item.name,
        value: item.id
      })));
    })
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    width: '120px',
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ]
  }
];

// 表格加载状态
const loading = ref(false);

// 企业列表
const enterpriseList = ref([]);
const enterpriseLoading = ref(false);

// 表格分页设置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条数据`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列定义
const columns = [
  {
    title: '知识库名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    width: 180
  },
  {
    title: '所属企业',
    dataIndex: 'enterpriseName',
    key: 'enterpriseName',
    width: 200
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '文档数量',
    dataIndex: 'documentCount',
    key: 'documentCount',
    width: 120,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 320,
    fixed: 'right'
  }
];

// 企业选项
const enterpriseOptions = computed(() => {
  return enterpriseList.value.filter(item => item.status);
});

// 表格数据
const tableData = ref([]);

// 模态框相关
const modalVisible = ref(false);
const modalMode = ref('add'); // 'add' 或 'edit'
const modalTitle = computed(() => modalMode.value === 'add' ? '新增知识库' : '编辑知识库');
const modalLoading = ref(false);

// 表单数据
const formData = reactive({
  id: undefined,
  name: '',
  enterpriseId: undefined,
  description: '',
  documentCount: 0,
  status: true
});

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入知识库名称', trigger: 'blur' }],
  enterpriseId: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
  description: [{ required: true, message: '请输入知识库描述', trigger: 'blur' }]
};

// 初始化数据
onMounted(() => {
  fetchKnowledgeList();
  fetchEnterpriseList();
});

// 获取企业列表
const fetchEnterpriseList = async () => {
  try {
    enterpriseLoading.value = true;
    const res = await getEnterpriseList({ pageSize: 999 });
    enterpriseList.value = res.list;
  } catch (error) {
    message.error('获取企业列表失败: ' + error.message);
  } finally {
    enterpriseLoading.value = false;
  }
};

// 获取知识库列表数据
const fetchKnowledgeList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };
    
    const res = await getKnowledgeList(params);
    tableData.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    message.error('获取知识库列表失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchKnowledgeList();
};

// 查询处理
const handleSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  }
  pagination.current = 1;
  fetchKnowledgeList();
};

// 重置搜索
const resetSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  } else {
    searchForm.name = '';
    searchForm.enterpriseId = undefined;
    searchForm.status = undefined;
  }
  pagination.current = 1;
  fetchKnowledgeList();
};

// 显示新增对话框
const showAddModal = () => {
  modalMode.value = 'add';
  resetFormData();
  modalVisible.value = true;
};

// 重置表单数据
const resetFormData = () => {
  formData.id = undefined;
  formData.name = '';
  formData.enterpriseId = undefined;
  formData.description = '';
  formData.documentCount = 0;
  formData.status = true;
  
  // 重置表单校验状态
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理编辑
const handleEdit = (record) => {
  modalMode.value = 'edit';
  
  // 填充表单数据
  Object.assign(formData, record);
  
  modalVisible.value = true;
};

// 对话框提交
const handleModalSubmit = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    if (modalMode.value === 'add') {
      // 新增知识库
      await createKnowledge(formData);
      message.success('新增知识库成功');
    } else {
      // 编辑知识库
      await updateKnowledge(formData);
      message.success('编辑知识库成功');
    }
    
    modalVisible.value = false;
    fetchKnowledgeList();
  } catch (error) {
    message.error('操作失败: ' + error.message);
  } finally {
    modalLoading.value = false;
  }
};

// 对话框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 切换状态
const handleToggleStatus = (record) => {
  Modal.confirm({
    title: '提示',
    content: `确定要${record.status ? '禁用' : '启用'}【${record.name}】吗？`,
    onOk: async () => {
      try {
        loading.value = true;
        await updateKnowledge({
          ...record,
          status: !record.status
        });
        message.success(`${record.status ? '禁用' : '启用'}成功`);
        fetchKnowledgeList();
      } catch (error) {
        message.error(`操作失败: ${error.message}`);
      } finally {
        loading.value = false;
      }
    }
  });
};

// 处理删除
const handleDelete = async (record) => {
  try {
    loading.value = true;
    await deleteKnowledge(record.id);
    message.success('删除知识库成功');
    
    // 如果当前页只有一条数据且不是第一页，则跳转到上一页
    if (tableData.value.length === 1 && pagination.current > 1) {
      pagination.current--;
    }
    
    fetchKnowledgeList();
  } catch (error) {
    message.error('删除知识库失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 文档管理
const handleDocument = (record) => {
  router.push({
    path: '/enterprise/document',
    query: {
      knowledgeId: record.id,
      knowledgeName: record.name
    }
  });
};
</script>

<style scoped>
/* 移除重复的.action-buttons和.action-button样式定义，使用全局样式 */

.table-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

/* 确保表格等宽列 */
:deep(.ant-table) th {
  text-align: center;
}

:deep(.ant-table-cell) {
  white-space: nowrap;
}

/* 固定列样式优化 */
:deep(.ant-table-cell-fix-right) {
  background-color: #fff;
}

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}
</style>