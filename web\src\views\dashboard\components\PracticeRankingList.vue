<template>
  <div class="practice-ranking">
    <a-table
      :columns="columns"
      :data-source="data"
      :pagination="false"
      :row-class-name="setRowClassName"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'rank'">
          <span class="rank-number" :class="getRankClass(record.rank)">{{ record.rank }}</span>
        </template>
        <template v-if="column.key === 'department'">
          <a-tag :color="getDepartmentColor(record.department)">
            {{ record.department }}
          </a-tag>
        </template>
        <template v-if="column.key === 'position'">
          <a-tag color="blue">{{ record.position }}</a-tag>
        </template>
        <template v-if="column.key === 'practiceTime'">
          <div class="duration-bar">
            <div class="duration-progress" :style="{ width: `${(record.practiceTime / getMaxPracticeTime()) * 100}%` }"></div>
            <span class="duration-text">{{ record.practiceTime }} 分钟</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
});

const columns = [
  { title: '排名', dataIndex: 'rank', key: 'rank', width: 60 },
  { title: '姓名', dataIndex: 'name', key: 'name', width: 100 },
  { title: '部门', dataIndex: 'department', key: 'department', width: 100 },
  { title: '岗位', dataIndex: 'position', key: 'position', width: 100 },
  { title: '练习时长', dataIndex: 'practiceTime', key: 'practiceTime', width: 200 },
  { title: '练习次数', dataIndex: 'practiceCount', key: 'practiceCount', width: 100 },
  { title: '题量', dataIndex: 'totalQuestions', key: 'totalQuestions', width: 100 }
];

const getMaxPracticeTime = () => {
  if (props.data && props.data.length > 0) {
    return Math.max(...props.data.map(item => item.practiceTime || 0));
  }
  return 100;
};

const getRankClass = (rank) => {
  if (rank === 1) return 'rank-first';
  if (rank === 2) return 'rank-second';
  if (rank === 3) return 'rank-third';
  return '';
};

const getDepartmentColor = (department) => {
  const departmentColors = {
    '前厅': '#fbc2eb',
    '后厨': '#a18cd1',
    '财务': '#84fab0',
    '人事': '#8fd3f4',
  };
  return departmentColors[department] || '#1890ff';
};

const setRowClassName = (record) => {
  if (record.rank <= 3) {
    return 'top-three-row';
  }
  return '';
};
</script>

<style scoped>
.practice-ranking {
  margin-top: 16px;
}

.rank-number {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  color: #fff;
  background-color: #d9d9d9;
}

.rank-first {
  background-color: #f5222d;
}

.rank-second {
  background-color: #fa8c16;
}

.rank-third {
  background-color: #faad14;
}

.duration-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.duration-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(to right, #a18cd1, #fbc2eb);
  border-radius: 10px;
  transition: width 0.5s ease;
}

.duration-text {
  position: absolute;
  right: 10px;
  top: 0;
  line-height: 20px;
  color: #333;
  font-size: 12px;
  font-weight: bold;
}

.top-three-row {
  background-color: rgba(251, 194, 235, 0.1);
}

.top-three-row:hover td {
  background-color: rgba(251, 194, 235, 0.2) !important;
}
</style> 