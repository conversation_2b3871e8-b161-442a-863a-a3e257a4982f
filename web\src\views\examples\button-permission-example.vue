<template>
  <div class="page-container">
    <page-header>
      <template #title>按钮权限控制示例</template>
    </page-header>

    <div class="content">
      <a-card title="1. 使用 v-permission 指令控制按钮显示/隐藏" style="margin-bottom: 16px;">
        <a-space>
          <!-- 单个权限控制 -->
          <a-button type="primary" v-permission="'user.add'">
            <template #icon><plus-outlined /></template>
            新增用户（需要user.add权限）
          </a-button>

          <!-- 多个权限控制（任意一个） -->
          <a-button type="default" v-permission="['user.edit', 'user.update']">
            <template #icon><edit-outlined /></template>
            编辑用户（需要user.edit或user.update权限）
          </a-button>

          <!-- 多个权限控制（全部拥有） -->
          <a-button type="danger" v-permission:all="['user.delete', 'user.admin']">
            <template #icon><delete-outlined /></template>
            删除用户（需要user.delete和user.admin权限）
          </a-button>

          <!-- 没有权限的按钮（演示隐藏） -->
          <a-button v-permission="'super.admin'">
            超级管理员按钮（需要super.admin权限）
          </a-button>
        </a-space>
      </a-card>

      <a-card title="2. 使用计算属性控制按钮状态" style="margin-bottom: 16px;">
        <a-space>
          <!-- 禁用按钮而不是隐藏 -->
          <a-button 
            type="primary" 
            :disabled="!hasAddPermission"
            @click="handleAdd"
          >
            <template #icon><plus-outlined /></template>
            新增（{{ hasAddPermission ? '有权限' : '无权限' }}）
          </a-button>

          <a-button 
            type="default" 
            :disabled="!hasEditPermission"
            @click="handleEdit"
          >
            <template #icon><edit-outlined /></template>
            编辑（{{ hasEditPermission ? '有权限' : '无权限' }}）
          </a-button>

          <!-- 根据权限显示不同文本 -->
          <a-button 
            :type="hasDeletePermission ? 'danger' : 'default'"
            :disabled="!hasDeletePermission"
            @click="handleDelete"
          >
            <template #icon><delete-outlined /></template>
            {{ hasDeletePermission ? '删除' : '无删除权限' }}
          </a-button>
        </a-space>
      </a-card>

      <a-card title="3. 表格操作列权限控制" style="margin-bottom: 16px;">
        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          :pagination="false"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <!-- 查看按钮 - 所有人都能看到 -->
                <a-button type="link" size="small" @click="handleView(record)">
                  <template #icon><eye-outlined /></template>
                  查看
                </a-button>

                <!-- 编辑按钮 - 需要编辑权限 -->
                <a-button 
                  type="link" 
                  size="small" 
                  v-permission="'user.edit'"
                  @click="handleEdit(record)"
                >
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>

                <!-- 删除按钮 - 需要删除权限 -->
                <a-button 
                  type="link" 
                  size="small" 
                  danger
                  v-permission="'user.delete'"
                  @click="handleDelete(record)"
                >
                  <template #icon><delete-outlined /></template>
                  删除
                </a-button>

                <!-- 重置密码 - 需要管理员权限 -->
                <a-button 
                  type="link" 
                  size="small" 
                  v-permission="'user.reset.password'"
                  @click="handleResetPassword(record)"
                >
                  <template #icon><lock-outlined /></template>
                  重置密码
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>

      <a-card title="4. 权限信息展示">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="当前用户权限">
            <a-tag v-for="permission in userPermissions" :key="permission" color="blue">
              {{ permission }}
            </a-tag>
            <span v-if="userPermissions.length === 0">无权限信息</span>
          </a-descriptions-item>
          <a-descriptions-item label="权限检查结果">
            <p>user.add: {{ hasPermission('user.add') ? '✅ 有权限' : '❌ 无权限' }}</p>
            <p>user.edit: {{ hasPermission('user.edit') ? '✅ 有权限' : '❌ 无权限' }}</p>
            <p>user.delete: {{ hasPermission('user.delete') ? '✅ 有权限' : '❌ 无权限' }}</p>
            <p>super.admin: {{ hasPermission('super.admin') ? '✅ 有权限' : '❌ 无权限' }}</p>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { hasPermission, getUserPermissions } from '@/utils/permission'

// 表格数据
const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: '正常' },
  { id: 2, name: '李四', email: '<EMAIL>', status: '禁用' },
  { id: 3, name: '王五', email: '<EMAIL>', status: '正常' }
])

// 表格列定义
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action', width: 300 }
]

// 权限计算属性
const hasAddPermission = computed(() => hasPermission('user.add'))
const hasEditPermission = computed(() => hasPermission('user.edit'))
const hasDeletePermission = computed(() => hasPermission('user.delete'))

// 获取用户权限
const userPermissions = computed(() => getUserPermissions())

// 事件处理函数
const handleAdd = () => {
  message.success('执行新增操作')
}

const handleEdit = (record) => {
  message.success(`编辑用户: ${record?.name || ''}`)
}

const handleDelete = (record) => {
  message.success(`删除用户: ${record?.name || ''}`)
}

const handleView = (record) => {
  message.info(`查看用户: ${record.name}`)
}

const handleResetPassword = (record) => {
  message.success(`重置用户密码: ${record.name}`)
}
</script>

<style scoped>
.page-container {
  padding: 24px;
}

.content {
  max-width: 1200px;
}

:deep(.ant-descriptions-item-label) {
  width: 150px;
}
</style> 