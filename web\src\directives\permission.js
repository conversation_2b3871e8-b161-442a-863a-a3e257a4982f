import { hasPermission } from '@/utils/permission'

/**
 * v-permission 权限指令
 * 用法：
 * v-permission="'user.add'" 单个权限
 * v-permission="['user.add', 'user.edit']" 多个权限（任意一个）
 * v-permission:all="['user.add', 'user.edit']" 多个权限（全部拥有）
 */
export const permission = {
  // Vue 3 指令语法
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el, binding) {
  const { value, arg } = binding
  
  let hasAuth = false
  
  if (arg === 'all') {
    // v-permission:all 要求拥有所有权限
    if (Array.isArray(value)) {
      hasAuth = value.every(permission => hasPermission(permission))
    } else {
      hasAuth = hasPermission(value)
    }
  } else {
    // 默认行为：拥有任意一个权限即可
    hasAuth = hasPermission(value)
  }
  
  if (!hasAuth) {
    // 移除元素
    el.style.display = 'none'
    // 或者完全移除DOM节点（可选）
    // el.parentNode && el.parentNode.removeChild(el)
  } else {
    // 显示元素
    el.style.display = ''
  }
}

// 默认导出，方便全局注册
export default permission 