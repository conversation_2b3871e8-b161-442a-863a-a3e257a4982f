<template>
  <div class="table-container">
    <div class="table-top">
      <div class="table-top-left">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索知识库名称/文件名称"
          style="width: 250px"
          @search="$emit('search')"
        />
        
        <!-- 文件归属筛选 -->
        <a-select
          v-model:value="selectedFileCategory"
          placeholder="文件归属"
          style="width: 150px;"
          allow-clear
          :default-open="false"
          :default-value="undefined"
        >
          <a-select-option v-for="item in positionTypeOptions" :key="item.id" :value="item.dictLabel">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
        
        <!-- 所属岗位名称筛选 -->
        <a-select
          v-model:value="selectedPosition"
          placeholder="所属岗位名称"
          style="width: 180px;"
          allow-clear
          :default-open="false"
          :default-value="undefined"
        >
          <a-select-option v-for="item in positionNameOptions" :key="item.id" :value="item.dictLabel">
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
        
        <!-- 查询按钮 -->
        <a-button type="primary" @click="$emit('search')" class="query-button">
          <search-outlined /> 查询
        </a-button>
        
        <!-- 重置按钮 -->
        <a-button @click="$emit('reset')" class="reset-button">
          <reload-outlined /> 重置
        </a-button>
      </div>
      <div class="table-top-right">
        <a-button type="primary" @click="$emit('add')" class="add-button">
          <upload-outlined /> 上传文档
        </a-button>
      </div>
    </div>
    
    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 1500 }"
      bordered
      rowKey="id"
      @change="handleTableChange"
    >
      <!-- 证书名称 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <span>{{ record.name }}</span>
        </template>
        
        <!-- 文件归属 -->
        <template v-if="column.dataIndex === 'category'">
          <a-tag :color="record.category === '前厅' ? '#a18cd1' : '#fbc2eb'">
            {{ record.category }}
          </a-tag>
        </template>
        
        <!-- 文件类型 -->
        <template v-if="column.dataIndex === 'fileType'">
          <a-tag :color="getFileTypeColor(record.fileType)">
            {{ record.fileTypeChinese || getFileTypeChinese(record.fileType) }}
          </a-tag>
        </template>
        
        <!-- 操作按钮 -->
        <template v-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button class="custom-button" @click="$emit('edit', record)">
              <edit-outlined style="font-size: 14px; margin-right: 2px;" />编辑
            </a-button>
            <a-button class="custom-button" @click="$emit('delete', record)">
              <delete-outlined style="font-size: 14px; margin-right: 2px;" />删除
            </a-button>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>

// 接收传入的props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  dataSource: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  pagination: {
    type: Object,
    required: true
  },
  positionTypeOptions: {
    type: Array,
    default: () => []
  },
  positionNameOptions: {
    type: Array,
    default: () => []
  },
  searchText: {
    type: String,
    default: ''
  },
  selectedFileCategory: {
    type: String,
    default: undefined
  },
  selectedPosition: {
    type: String,
    default: undefined
  }
});

// 定义事件
const emit = defineEmits([
  'search', 
  'reset', 
  'add', 
  'edit', 
  'delete',
  'table-change'
]);

// 处理表格变化
const handleTableChange = (pagination) => {
  emit('table-change', pagination);
};

// 获取文件类型颜色
const getFileTypeColor = (type) => {
  const colorMap = {
    'pdf': '#f56c6c',
    'word': '#409eff', 
    'excel': '#67c23a',
    'ppt': '#e6a23c',
    'image': '#a18cd1',
    'text': '#909399',
    'archive': '#67c23a',
    'audio': '#e6a23c',
    'video': '#f56c6c',
    'unknown': '#909399'
  };
  
  return colorMap[type] || '#909399';
};

// 获取文件类型中文名称
const getFileTypeChinese = (fileType) => {
  const typeMap = {
    'pdf': 'PDF文档',
    'word': 'Word文档',
    'excel': 'Excel表格',
    'ppt': 'PPT演示',
    'image': '图片',
    'text': '文本',
    'archive': '压缩包',
    'audio': '音频',
    'video': '视频',
    'unknown': '未知类型'
  };
  return typeMap[fileType] || '未知类型';
};
</script>

<style lang="scss" scoped>
@import '../styles/table.scss';
</style> 