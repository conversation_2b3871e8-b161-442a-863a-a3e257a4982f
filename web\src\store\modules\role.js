import { defineStore } from 'pinia'
import {
  getRoleList,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  updateRolePermissions
} from '@/api/system/role'

export const useRoleStore = defineStore('role', {
  state: () => ({
    roles: []
  }),

  actions: {
    // 获取角色列表
    async getRoleList(params) {
      const response = await getRoleList(params)
      return response.data
    },

    // 创建角色
    async createRole(data) {
      const response = await createRole(data)
      return response.data
    },

    // 更新角色
    async updateRole(data) {
      const response = await updateRole(data)
      return response.data
    },

    // 删除角色
    async deleteRole(id) {
      const response = await deleteRole(id)
      return response.data
    },

    // 获取角色权限
    async getRolePermissions(roleId) {
      const response = await getRolePermissions(roleId)
      return response.data
    },

    // 更新角色权限
    async updateRolePermissions(data) {
      const response = await updateRolePermissions(data)
      return response.data
    }
  }
}) 