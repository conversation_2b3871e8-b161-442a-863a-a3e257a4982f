import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { getPositionTypeOptions, getPositionNameOptions } from '@/api/organization/position';
import { 
  getKnowledgeBaseList,
  updateKnowledgeDocument,
  deleteKnowledgeDocument
} from '@/api/knowledge-base';

/**
 * 知识库管理核心逻辑的可复用composable
 */
export default function useKnowledgeBase() {
  // 表格加载状态
  const loading = ref(false);

  // 筛选参数
  const fileCategoryFilter = ref(undefined);
  const positionFilter = ref(undefined);
  const searchText = ref('');
  
  // 表格数据源
  const dataSource = ref([]);

  // 表格列定义
  const columns = [
    {
      title: '证书名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 180,
    },
    {
      title: '文件归属',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: '所属岗位名称',
      dataIndex: 'position',
      key: 'position',
      width: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      key: 'createdTime',
      width: 170,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      width: 170,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 140,
    }
  ];

  // 分页设置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50'],
    showTotal: total => `共 ${total} 条`
  });

  // 筛选后的数据
  const filteredData = computed(() => {
    return dataSource.value;
  });

  // 岗位类型选项和岗位名称选项
  const positionTypeOptions = ref([]);
  const positionNameOptions = ref([]);

  // 弹窗可见性
  const modalVisible = ref(false);
  const isEdit = ref(false);

  // 表单数据
  const formData = reactive({
    id: null,
    name: '',
    fileName: '',
    fileType: '',
    category: '',
    position: '',
    positionLevel: '',
    fileCategory: '',
    certificateType: '',
    fileUpload: []
  });

  // 表单引用
  const formRef = ref(null);

  // 表单校验规则
  const formRules = {
    name: [{ required: true, message: '请输入知识库名称', trigger: 'blur' }],
    fileName: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
    fileType: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
    category: [{ required: true, message: '请选择文件归属', trigger: 'change' }],
    position: [{ required: true, message: '请选择所属岗位名称', trigger: 'change' }],
    positionLevel: [{ required: true, message: '请选择所属岗位等级', trigger: 'change' }],
    fileCategory: [{ required: true, message: '请选择文件分类', trigger: 'change' }],
    certificateType: [{ required: true, message: '请选择证书类型', trigger: 'change' }]
  };

  // 生命周期钩子
  onMounted(() => {
    fetchData();
    fetchPositionOptions();
  });

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params = {
        searchText: searchText.value,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        fileCategory: fileCategoryFilter.value,  // 文件归属筛选参数
        position: positionFilter.value          // 岗位名称筛选参数
      };
      
      console.log('请求参数:', params);
      const response = await getKnowledgeBaseList(params);
      console.log('获取到的知识库响应:', response);
      
      // 检查响应中是否有list字段
      if (response && response.list) {
        // 响应直接包含list (request工具已处理过data字段)
        dataSource.value = response.list.map(item => {
          // 如果category为空，则使用fileCategory
          if (!item.category && item.fileCategory) {
            item.category = item.fileCategory;
          }
          return item;
        });
        
        // 更新分页信息
        pagination.total = response.total;
        pagination.current = response.page || pagination.current;
        pagination.pageSize = response.pageSize || pagination.pageSize;
      } else {
        dataSource.value = [];
        pagination.total = 0;
      }
      
    } catch (error) {
      console.error('获取知识库数据失败', error);
      message.error('获取知识库数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 获取岗位类型和岗位名称选项
  const fetchPositionOptions = async () => {
    try {
      const [typeRes, nameRes] = await Promise.all([
        getPositionTypeOptions(),
        getPositionNameOptions()
      ]);
      
      // 处理岗位类型数据
      positionTypeOptions.value = typeRes;
      
      // 处理岗位名称数据
      positionNameOptions.value = nameRes;
      
    } catch (error) {
      console.error('获取岗位选项失败', error);
      message.error('获取岗位选项失败');
    }
  };

  // 处理表格变化
  const handleTableChange = (pag) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    fetchData();
  };

  // 处理搜索
  const handleSearch = () => {
    pagination.current = 1; // 搜索时重置到第一页
    fetchData();
  };

  // 显示新增弹窗
  const showAddModal = () => {
    isEdit.value = false;
    resetForm();
    modalVisible.value = true;
  };

  // 编辑知识库
  const editKnowledge = (record) => {
    isEdit.value = true;
    
    // 只填充必要的表单数据
    formData.id = record.id;
    formData.name = record.name;
    formData.fileName = record.fileName;
    formData.fileType = record.fileType;
    formData.category = record.category;
    formData.position = record.position;
    formData.positionLevel = record.positionLevel;
    formData.fileCategory = record.fileCategory;
    formData.certificateType = record.certificateType;
    
    // 更新文件列表，但设为只读
    const fileList = record.fileName ? [{
      uid: '-1',
      name: record.fileName,
      status: 'done',
      url: '#'
    }] : [];
    
    modalVisible.value = true;
    
    return fileList;
  };

  // 处理文件分类变更
  const handleFileCategoryChange = () => {
    // 当文件分类变更时可以做一些处理
  };

  // 确认删除
  const confirmDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除知识库 "${record.name}" 吗？此操作不可恢复。`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        handleDelete(record);
      }
    });
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      const response = await deleteKnowledgeDocument(record.id);
      message.success('删除成功');
      fetchData(); // 重新加载数据
    } catch (error) {
      console.error('删除知识库文档失败', error);
      message.error('删除失败');
    }
  };

  // 处理编辑提交
  const handleEditSubmit = async () => {
    try {
      const response = await updateKnowledgeDocument(formData.id, {
        name: formData.name
      });
      
      message.success('更新成功');
      modalVisible.value = false;
      resetForm();
      fetchData(); // 重新加载数据

    } catch (error) {
      console.error('更新知识库文档失败', error);
      message.error('更新失败');
    }
  };

  // 重置表单
  const resetForm = () => {
    formRef.value?.resetFields();
    formData.id = null;
    formData.name = '';
    formData.fileName = '';
    formData.fileType = '';
    formData.category = '';
    formData.position = '';
    formData.positionLevel = '';
    formData.fileCategory = '';
    formData.certificateType = '';
  };

  // 处理弹窗取消
  const handleModalCancel = () => {
    resetForm();
    modalVisible.value = false;
  };

  // 重置筛选器
  const resetFilters = () => {
    fileCategoryFilter.value = undefined;
    positionFilter.value = undefined;
    searchText.value = '';
    fetchData();
  };

  return {
    loading,
    fileCategoryFilter,
    positionFilter,
    searchText,
    dataSource,
    columns,
    pagination,
    filteredData,
    positionTypeOptions,
    positionNameOptions,
    modalVisible,
    isEdit,
    formData,
    formRef,
    formRules,
    fetchData,
    fetchPositionOptions,
    handleTableChange,
    handleSearch,
    showAddModal,
    editKnowledge,
    handleFileCategoryChange,
    confirmDelete,
    handleDelete,
    handleEditSubmit,
    resetForm,
    handleModalCancel,
    resetFilters
  };
} 