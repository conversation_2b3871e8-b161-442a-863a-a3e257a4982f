<!-- 练考配置组件 -->
<template>
  <div class="exam-config-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-button type="primary" class="custom-primary-button" @click="showRulesModal(null, 'practice')">
          <setting-outlined /> 通用练习配置
        </a-button>
        <a-button type="primary" class="custom-primary-button" style="margin-left: 16px;" @click="showRulesModal(null, 'exam')">
          <setting-outlined /> 通用考试配置
        </a-button>
        <a-button type="primary" class="custom-primary-button" style="margin-left: 16px;" @click="showAddModal">
          <plus-outlined /> 新增
        </a-button>
      </template>
    </page-header>

    <!-- 表格 -->
      <base-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        rowKey="id"
        :delete-title="deleteTitle"
        :pagination="pagination"
      @edit="editConfig"
      @delete="handleDelete"
      :action-config="actionConfig"
      @change="handleTableChange"
    >
      <!-- 状态 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === '必练' ? '#a18cd1' : '#fbc2eb'">
            {{ record.status }}
          </a-tag>
        </template>

        <!-- 是否需要确认成绩 -->
        <template v-if="column.dataIndex === 'needConfirmScore'">
          <a-tag :color="record.needConfirmScore ? '#52c41a' : '#d9d9d9'">
            {{ record.needConfirmScore ? '是' : '否' }}
          </a-tag>
        </template>

        <!-- 成绩发布规则 -->
        <template v-if="column.dataIndex === 'scoreReleaseRule'">
          <a-tag :color="record.scoreReleaseRule === '自动发布' ? '#52c41a' : '#a18cd1'">
            {{ record.scoreReleaseRule }}
          </a-tag>
        </template>

        <!-- 操作按钮 -->
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" size="small" class="custom-button" @click="showRulesModal(record, 'practice')">
            <setting-outlined />练习配置
          </a-button>
          <a-button v-if="record.status === '必考'" type="link" size="small" class="custom-button" @click="showRulesModal(record, 'exam')">
            <setting-outlined />考试配置
          </a-button>
        </template>
      </template>
    </base-table>

    <!-- 新增/编辑配置弹窗 -->
    <a-modal
      v-model:visible="configModalVisible"
      :title="isEdit ? '编辑关联证书' : '新增关联证书'"
      @ok="handleConfigModalOk"
      @cancel="handleConfigModalCancel"
      width="700px"
    >

      <a-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="岗位类型" name="positionBelongId">
          <a-select
            v-model:value="formData.positionBelongId"
            placeholder="请选择岗位类型"
            :disabled="selectedNode.type !== 'all'"
            @change="handlePositionBelongChange"
            :options="positionTypeOptions"
            label-in-value
            :field-names="{
              label: 'name',
              value: 'id'
            }"
          />
        </a-form-item>

        <a-form-item label="岗位名称" name="positionNameId">
          <a-select
            v-model:value="formData.positionNameId"
            placeholder="请选择岗位名称"
            :disabled=" !formData.positionBelongId"
            @change="handlePositionNameChange"
            :options="filteredAPIPositionNames"
            label-in-value
            :field-names="{
              label: 'name',
              value: 'id'
            }"
          />
        </a-form-item>

        <a-form-item label="岗位等级" name="positionLevelId">
          <a-select
            v-model:value="formData.positionLevelId"
            placeholder="请选择岗位等级"
            :disabled="selectedNode.type === 'level'"
            @change="handlePositionLevelChange"
            :options="levelOptions"
            label-in-value
            :field-names="{
              label: 'name',
              value: 'id'
            }"
          />
        </a-form-item>

        <a-form-item label="模式" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择模式">
            <a-select-option value="必练">必练</a-select-option>
            <a-select-option value="必考">必考</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="练考科目" name="examSubject">
          <a-select
            v-model:value="formData.examSubject"
            placeholder="请先选择岗位类型和岗位名称"
            :options="filteredKnowledgeOptions"
            :fieldNames="{ label: 'name', value: 'id' }"
            :loading="knowledgeLoading"
            :disabled="!formData.positionBelongId || !formData.positionNameId"
            :mode="isEdit ? undefined : 'multiple'"
            :max-tag-count="3"
            :max-tag-placeholder="(omittedValues) => `+${omittedValues.length}个科目`"
          />
          <div v-if="!formData.positionBelongId || !formData.positionNameId" style="color: #ff4d4f; font-size: 12px;">
            必须同时选择岗位类型和岗位名称才能选择练考科目
          </div>
          <!-- <div v-if="isEdit" style="color: #1890ff; font-size: 12px;">
            编辑模式下只能选择单个练考科目，若需添加多个科目请使用新增功能
          </div> -->
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 考试通用规则配置弹窗 -->
    <a-modal
      v-model:visible="rulesModalVisible"
      :title="currentRecord ? (rulesForm.configType === 'practice' ? '练习配置' : '考试配置') : (rulesForm.configType === 'practice' ? '通用练习配置' : '通用考试配置')"
      @ok="handleRulesModalOk"
      @cancel="handleRulesModalCancel"
      width="700px"
    >
    <div style="margin-bottom: 16px;">
        <a-alert
          type="info"
          show-icon
          message="提示"
          :html-content="true"
        >
        <template #description>
          <div v-html="getDescription(rulesForm.configType)"></div>
        </template>
        </a-alert>
      </div>
      <template #footer>
        <div>
          <a-button key="cancel" @click="handleRulesModalCancel">取消</a-button>
          <a-button key="submit" type="primary" @click="handleRulesModalOk">确定</a-button>
        </div>
      </template>

      <a-form
        :model="rulesForm"
        ref="rulesFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 练习配置部分 - 练习类型或全局配置时显示 -->
        <template v-if="rulesForm.configType === 'practice'">
          <!-- <div class="section-title">练习配置</div>
          <a-divider style="margin-top: 0;" /> -->

          <a-form-item label="考试资格" name="questionMode">
            <a-select v-model:value="rulesForm.questionMode" placeholder="请选择考试资格类型">
              <a-select-option value="时长">时长</a-select-option>
              <a-select-option value="题数">题数</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="练习时长(分钟)" name="practiceDuration" v-if="rulesForm.questionMode === '时长'">
            <a-input-number
              v-model:value="rulesForm.practiceDuration"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="练习题目数量" name="practiceQuestionCount" v-if="rulesForm.questionMode === '题数'">
            <a-input-number
              v-model:value="rulesForm.practiceQuestionCount"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <!-- 考试配置部分 - 只有考试类型配置或全局配置时显示 -->
        <template v-if="(rulesForm.configType === 'exam' && (!currentRecord || currentRecord.status === '必考'))">
          <!-- <div class="section-title" style="margin-top: 20px;">考试配置</div>
          <a-divider style="margin-top: 0;" /> -->

          <a-form-item label="考试次数" name="examCount">
            <a-input-number
              v-model:value="rulesForm.examCount"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="考试时长(分钟)" name="examDuration">
            <a-input-number
              v-model:value="rulesForm.examDuration"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="正确题目数量" name="passScore">
            <a-input-number
              v-model:value="rulesForm.passScore"
              :min="0"
              :max="100"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="考试题目数量" name="questionCount">
            <a-input-number
              v-model:value="rulesForm.questionCount"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="证书有效时间(天)" name="certificateValidDays">
            <a-input-number
              v-model:value="rulesForm.certificateValidDays"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="人工出题占比（%）" name="manualQuestionRatio">
            <a-input-number
              v-model:value="rulesForm.manualQuestionRatio"
              :min="0"
              :max="100"
              style="width: 100%"
              @change="handleManualRatioChange"
              placeholder="总占比为100"
            />
          </a-form-item>

          <a-form-item label="智能出题占比（%）" name="intelligentQuestionRatio">
            <a-input-number
              v-model:value="rulesForm.intelligentQuestionRatio"
              :min="0"
              :max="100"
              style="width: 100%"
              @change="handleIntelligentRatioChange"
              placeholder="总占比为100"
            />
          </a-form-item>
        </template>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import {
  SettingOutlined,
  PlusOutlined,
  SyncOutlined
} from '@ant-design/icons-vue';
import {
  getPositionStructureTree,
  getPositionNameOptions,
  getPositionTypeOptions,
  getLevelOptions
} from '@/api/organization/position';
import {
  getExamConfigs,
  addExamConfig,
  updateExamConfig,
  deleteExamConfig,
  getExamRules,
  updatePracticeRules,
  updateExamRules,
  updateConfigPracticeRules,
  updateConfigExamRules,
  syncExamRules
} from '@/api/exam/exam-config';
import {
  getKnowledgeBaseList,
} from '@/api/knowledge-base';
import {
  getExamSubjectOptions
} from '@/api/exam/exam-review';
import SearchFormCard from '@/components/SearchForm/index.vue';
import { useTablePagination } from '@/utils/common';

const formKey = ref(0);

const deleteTitle = '确定要删除此数据吗？';
const actionConfig = ref({
  edit: true,
  delete: true,
});

const getDescription = (configType) => {
  if (configType === 'practice') {

    return ' 1. 修改练习的通用配置会给所有练习科目换上新规则哦，这个变动影响可不小，所以请谨慎操作呀！<br/>2. 如果只想对某个练习科目做些规则调整，试试列表里的【练习配置】按钮吧，轻轻一点，随心修改～';
  } else if (configType === 'exam') {
    return '1. 修改考试的通用配置会给所有考试科目换上新规则哦，这个变动影响可不小，所以请谨慎操作呀！<br/>2. 如果只想对某个考试科目做些规则调整，试试列表里的【考试配置】按钮吧，轻轻一点，随心修改～';
  }
};
// 定义props接收父组件传递的选中节点
const props = defineProps({
  selectedNode: {
    type: Object,
    default: () => ({
      type: 'all',
      key: 'all',
      title: '全部',
      category: '',
      position: '',
      level: ''
    })
  }
});

// 表格加载状态
const loading = ref(false);

// 岗位结构树数据
const positionTreeData = ref([]);
const positionCategories = ref([]);
const positionNamesMap = ref({});
const positionLevelsMap = ref({});

// 岗位相关数据数组
const positionNames = ref([]); // 岗位名称数组
const positionTypes = ref([]); // 岗位类型数组
const levelOptions = ref([]); // 岗位等级数组

// 定义组织结构相关的数据映射缓存
const positionMap = ref({}); // ID到名称的映射
const departmentMap = ref({}); // ID到名称的映射
const levelMap = ref({}); // ID到名称的映射


const formItems = computed(() =>  {
  return [
  {
    label: '练考科目',
    field: 'searchText',
    type: 'select',
    placeholder: '请选择考试科目',
    get options() { return examSubjectOptions.value; },
    showSearch: true,
    optionFilterProp: 'children',
    allowClear: true,
    width: '300px'
  },
  {
    label: '岗位名称',
    field: 'positionName',
    type: 'select',
    placeholder: '请选择岗位名称',
    get options() { return positionNames.value; }, // 使用getter确保每次获取最新值
    selectLabel: 'name',
    selectValue: 'id'
  },
  {
    label: '模式',
    field: 'statusFilter',
    type: 'select',
    options: [
      {
        label: '必练',
        value: '必练'
      },
      {
        label: '必考',
        value: '必考'
      }
    ]
  }
  ]
});

// const { PositionType, PositionName, Level, KnowledgeBase } = require('../../models');

const searchForm = reactive({
  level: '',
  searchText: '',
  positionName: '',
  statusFilter: ''
});

// 表格数据源
const dataSource = ref([]);

// 知识库列表数据
const knowledgeBaseList = ref([]);
const knowledgeLoading = ref(false);

// 考试科目选项数据
const examSubjectOptions = ref([]);

// 获取考试科目选项
const fetchExamSubjectOptions = async () => {
  try {
    // 调用获取考试科目列表的API
    const res = await getExamSubjectOptions();
    
    // 处理API响应数据
    if (res && res.code === 200 && res.data) {
      // 如果后端返回的数据格式是 { code: 200, data: [...] }
      const subjects = Array.isArray(res.data) ? res.data : [];
      examSubjectOptions.value = subjects.map(item => ({
        label: item.name || item.title || item.label,
        value: item.id || item.value // 使用id作为value
      }));
    } else if (Array.isArray(res)) {
      // 如果后端直接返回数组
      examSubjectOptions.value = res.map(item => ({
        label: item.name || item.title || item.label,
        value: item.id || item.value // 使用id作为value
      }));
    } else {
      // 如果没有数据，使用空数组
      examSubjectOptions.value = [];
    }
    
    console.log('已加载考试科目选项:', examSubjectOptions.value);
  } catch (error) {
    console.error('获取考试科目选项失败:', error);
    // 如果API调用失败，使用模拟数据作为备用
    const mockData = [
      { label: '安全生产基础知识', value: '1' },
      { label: '消防安全管理', value: '2' },
      { label: '职业健康安全', value: '3' },
      { label: '环境保护法规', value: '4' },
      { label: '特种设备操作', value: '5' }
    ];
    examSubjectOptions.value = mockData;
    console.warn('使用模拟数据作为考试科目选项');
  }
};

// 获取知识库列表
const fetchKnowledgeBaseList = async () => {
  knowledgeLoading.value = true;
  try {
    // 不传分页参数，获取全部数据
    const response = await getKnowledgeBaseList({
      pageSize: 9999, // 设置一个足够大的数值以获取所有数据
      documentType: 'exam' // 只获取考试类型的文档
    });

    if (response && response.list) {
      knowledgeBaseList.value = response.list;
    } else {
      knowledgeBaseList.value = [];
    }
  } catch (error) {
    console.error('获取知识库数据失败：', error);
    message.error('获取知识库数据失败：' + error.message);
  } finally {
    knowledgeLoading.value = false;
  }
};

// 获取练考配置数据
const fetchExamConfigs = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };


    // 调用API获取数据
    const response = await getExamConfigs(params);


    dataSource.value = response.rows || [];

    // 更新分页信息
    updatePagination({
      total: response.total || dataSource.value.length,
      pageNum: response.pageNum,
      pageSize: response.pageSize
    });
  } catch (error) {
    message.error('获取练考配置失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: fetchExamConfigs,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 获取岗位名称选项
const fetchPositionNameOptions = async () => {
  try {
    const res = await getPositionNameOptions();
    // 从PositionName表获取数据，不再使用字典
    positionNames.value = res.rows || [];
  } catch (error) {
    console.error('获取岗位名称选项失败', error);
  }
};

// 获取岗位类别选项
const fetchPositionTypeOptions = async () => {
  try {
    const res = await getPositionTypeOptions();
    // 从PositionType表获取数据，不再使用字典
    positionTypes.value = res || [];
  } catch (error) {
    console.error('获取岗位类型选项失败', error);
  }
};

// 获取岗位等级选项
const fetchLevelOptions = async () => {
  try {
    const res = await getLevelOptions();
    levelOptions.value = res || [];
  } catch (error) {
    console.error('获取岗位等级选项失败', error);
  }
};

// 生命周期钩子
onMounted(async () => {
  // 初始化数据
  loading.value = true;

  try {
    // 并行加载所有数据
    await Promise.all([
    // 加载岗位结构树数据
      getPositionStructureTree().then(response => {
    positionTreeData.value = response;
    // 提取岗位类型（类别）
    positionCategories.value = response.filter(node => node.type === 'category');

    // 构建岗位名称映射表和岗位等级映射表
    positionNamesMap.value = {};
    positionLevelsMap.value = {};

    positionCategories.value.forEach(category => {
      // 添加部门ID到名称的映射
      if (category.id) {
        departmentMap.value[category.id] = category.title;
      }

      if (category.children && category.children.length > 0) {
        // 保存每个类别下的岗位
        positionNamesMap.value[category.key] = category.children;

        // 保存每个岗位下的等级
        category.children.forEach(position => {
          // 添加岗位ID到名称的映射
          if (position.id) {
            positionMap.value[position.id] = position.title;
          }

          if (position.children && position.children.length > 0) {
            positionLevelsMap.value[position.key] = position.children;

            // 添加等级ID到名称的映射
            position.children.forEach(level => {
              if (level.id) {
                levelMap.value[level.id] = level.title;
              }
            });
          }
        });
      }
    });
      }),

      // 获取岗位名称、类型和等级选项
      fetchPositionNameOptions(),
      fetchPositionTypeOptions(),
      fetchLevelOptions(),

      // 获取考试科目选项
      fetchExamSubjectOptions(),

    // 获取练考配置数据
      fetchExamConfigs(),


    ]);
    formKey.value +=1
      // 获取知识库列表数据
      fetchKnowledgeBaseList()



  } catch (error) {
    message.error('获取数据失败：' + error.message);
  } finally {
    loading.value = false;
  }
});

// 根据选择的岗位类型和岗位名称过滤知识库选项
const filteredKnowledgeOptions = computed(() => {
  // 添加调试日志

  // 如果没有同时选择岗位类型和岗位名称，则返回空数组
  if (!formData.positionBelongId || !formData.positionNameId) {
    return [];
  }

  if (!knowledgeBaseList.value || !knowledgeBaseList.value.length) {
    return [];
  }

  // 提取岗位ID，考虑labelInValue模式
  const positionBelongId = typeof formData.positionBelongId === 'object' ?
    formData.positionBelongId.value : formData.positionBelongId;

  const positionNameId = typeof formData.positionNameId === 'object' ?
    formData.positionNameId.value : formData.positionNameId;


  // 根据岗位类型和岗位名称进行过滤
  let result = knowledgeBaseList.value.filter(item => {
    const certificateTypeMatch = String(item.certificateType) === String(positionBelongId);
    // 修改：岗位匹配时包含指定岗位或"通用"岗位
    const positionMatch = String(item.position) === String(positionNameId) ||
                         String(item.position) === 'COMMON';

    return certificateTypeMatch && positionMatch;
  });


    return result.map(item => ({
      id: item.id,
      name: item.name
    }));
});

// 过滤后的岗位名称列表 - 使用API返回的数据
const filteredAPIPositionNames = computed(() => {
  // 如果没有选择岗位类型，返回空数组
  if (!formData.positionBelongId || !formData.positionBelongId.value) return [];

  const typeId = typeof formData.positionBelongId === 'object' ?
    formData.positionBelongId.value : formData.positionBelongId;


  // 根据选择的岗位类型过滤岗位名称
  return positionNames.value.filter(position =>
    String(position.typeId) === String(typeId)
  );
});

// 添加 positionTypeOptions 计算属性
const positionTypeOptions = computed(() => {
  return positionTypes.value;
});

// 岗位类型变更处理
const handlePositionBelongChange = (value) => {

  // 清空岗位名称和岗位等级
  formData.positionNameId = undefined;
  formData.positionLevelId = undefined;
  formData.positionName = '';
  formData.positionLevel = '';

  // 在labelInValue模式下，value是一个对象，包含value和label
  // value.value是实际的ID值，value.label是显示的文本
  formData.positionBelongId = value;
  formData.positionBelong = value.label;

  // 清空练考科目
  formData.examSubject = undefined;
};

// 岗位名称变更处理
const handlePositionNameChange = (value) => {

  // 清空岗位等级
  formData.positionLevelId = undefined;
  formData.positionLevel = '';

  // 在labelInValue模式下，value是一个对象
  formData.positionNameId = value;
  formData.positionName = value.label;

  // 清空练考科目
  formData.examSubject = undefined;
};

// 岗位等级变更处理函数
const handlePositionLevelChange = (value) => {

  // 在labelInValue模式下，value是一个对象
      formData.positionLevelId = value;
  formData.positionLevel = value.label;


};

// 根据选择的岗位类型过滤的岗位名称
const filteredPositionNames = computed(() => {
  if (!formData.positionBelongId) return [];

  // 查找选中的岗位类型节点
  const selectedCategory = positionCategories.value.find(category =>
    category.id === formData.positionBelongId || category.key === formData.positionBelongId
  );

  if (!selectedCategory || !selectedCategory.key) return [];

  formData.positionBelong = selectedCategory.title || selectedCategory.name;

  // 修正这里：使用key而不是positionId
  return positionNamesMap.value[selectedCategory.key] || [];
});


// 根据选择的岗位名称过滤的岗位等级
const filteredPositionLevels = computed(() => {
  if (!formData.positionNameId || !formData.positionBelongId) return [];

  // 查找选中的岗位类型节点
  const selectedCategory = positionCategories.value.find(category =>
    category.id === formData.positionBelongId || category.key === formData.positionBelongId
  );

  if (!selectedCategory || !selectedCategory.key) return [];

  // 查找选中的岗位名称节点
  const selectedPosition = positionNamesMap.value[selectedCategory.key]?.find(position =>
    position.id === formData.positionNameId ||
    position.key === formData.positionNameId ||
    position.positionId === formData.positionNameId
  );

  if (!selectedPosition || !selectedPosition.key) return [];

  formData.positionName = selectedPosition.title || selectedPosition.name;

  // 使用position的实际key，而不是构造key
  return positionLevelsMap.value[selectedPosition.key] || [];
});

// 帮助函数 - 根据ID获取岗位类型名称
const getPositionTypeName = (id) => {
  if (!id) return '-';
  const idStr = String(id);
  const positionType = positionTypes.value.find(type => String(type.id) === idStr);
  return positionType ? positionType.name : '-';
};

// 帮助函数 - 根据ID获取岗位名称
const getPositionName = (id) => {
  if (!id) return '-';
  const idStr = String(id);
  const position = positionNames.value.find(pos => String(pos.id) === idStr);
  return position ? position.name : '-';
};

// 帮助函数 - 根据ID获取岗位等级名称
const getPositionLevelName = (id) => {
  if (!id) return '-';
  const idStr = String(id);
  const level = levelOptions.value.find(lvl => String(lvl.id) === idStr);
  return level ? level.name : '-';
};

// 表格列定义
const columns = computed(() => {
  // 基础列 - 所有状态都显示
  const baseColumns = [
    {
      title: '岗位类型',
      dataIndex: 'positionBelong',
      key: 'positionBelong',
      width: 100,
      customRender: ({ record }) => {
        // 尝试多种可能的ID字段
        const typeId = record.positionBelong || record.typeId || record.positionTypeId || record.positionBelongId;

        // 如果找到ID，使用ID查找名称
        if (typeId) {
          const typeName = getPositionTypeName(typeId);
          if (typeName !== '-') {
            return typeName;
          }
        }

        // 回退到原始文本字段
        return record.departmentName || record.positionBelong || '-';
      }
    },
    {
      title: '岗位名称',
      dataIndex: 'positionName',
      key: 'positionName',
      width: 120,
      customRender: ({ record }) => {
        // 尝试多种可能的ID字段
        const positionId = record.positionName || record.nameId || record.positionNameId;

        // 如果找到ID，使用ID查找名称
        if (positionId) {
          const name = getPositionName(positionId);
          if (name !== '-') {
            return name;
          }
        }

        // 回退到原始文本字段
        return record.positionFullName || record.positionName || '-';
      }
    },
    {
      title: '岗位等级',
      dataIndex: 'positionLevel',
      key: 'positionLevel',
      width: 100,
      customRender: ({ record }) => {
        // 尝试多种可能的ID字段
        const levelId = record.positionLevel || record.levelId || record.positionLevelId;

        // 如果找到ID，使用ID查找名称
        if (levelId) {
          const levelName = getPositionLevelName(levelId);
          if (levelName !== '-') {
            return levelName;
          }
        }

        // 回退到原始文本字段
        return record.levelFullName || record.positionLevel || '-';
      }
    },
    {
      title: '练考科目',
      dataIndex: 'knowledgeBaseName',
      key: 'examSubject',
      width: 160,
    },
    {
      title: '模式',
      dataIndex: 'status',
      key: 'status',
      width: 80,
    },
  ];

  // 共有字段 - 必考和必练都显示
  const commonColumns = [
    {
      title: '练习时长(分钟)',
      dataIndex: 'practiceDuration',
      key: 'practiceDuration',
      width: 120,
    },
    {
      title: '练习题目数量',
      dataIndex: 'practiceQuestionCount',
      key: 'practiceQuestionCount',
      width: 120,
    }
  ];

  // 必考特有字段
  const examColumns = [
    {
      title: '考试时长(分钟)',
      dataIndex: 'examDuration',
      key: 'examDuration',
      width: 120,
    },
    {
      title: '考试题目数量',
      dataIndex: 'questionCount',
      key: 'questionCount',
      width: 120,
    },
    {
      title: '正确题目数量',
      dataIndex: 'passScore',
      key: 'passScore',
      width: 100,
    },
    {
      title: '考试次数',
      dataIndex: 'examCount',
      key: 'examCount',
      width: 100,
    },
    // {
    //   title: '人工出题占比',
    //   dataIndex: 'manualQuestionRatio',
    //   key: 'manualQuestionRatio',
    //   width: 120,
    //   customRender: ({ record }) => {
    //     return record.manualQuestionRatio ? `${record.manualQuestionRatio}/10` : '-';
    //   }
    // },
    // {
    //   title: '智能出题占比',
    //   dataIndex: 'intelligentQuestionRatio',
    //   key: 'intelligentQuestionRatio',
    //   width: 120,
    //   customRender: ({ record }) => {
    //     return record.intelligentQuestionRatio ? `${record.intelligentQuestionRatio}/10` : '-';
    //   }
    // },
    {
      title: '证书有效时间(天)',
      dataIndex: 'certificateValidDays',
      key: 'certificateValidDays',
      width: 140,
    }
  ];

  // 末尾列 - 所有状态都显示
  const endColumns = [
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      width: 150
    },
    {
      title: '更新时间',
      dataIndex: 'updated_time',
      key: 'updated_time',
      width: 150
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 350,
    }
  ];

  // 返回基础列 + 必考或必练状态列 + 末尾列
  if (searchForm.DeleteOutlinedstatusFilter === '必考') {
    return [...baseColumns, ...commonColumns, ...examColumns, ...endColumns];
  } else if (searchForm.statusFilter === '必练') {
    return [...baseColumns, ...commonColumns, ...endColumns];
  } else {
    // 默认显示所有列
    return [...baseColumns, ...commonColumns, ...examColumns, ...endColumns];
  }
});

// 过滤岗位选项
const filteredPositions = computed(() => {
  // 获取字典ID对应的字典标签
  const getCategoryLabelById = (typeId) => {
    const typeIdStr = String(typeId);
    // 查找匹配的category，使用positionCategories替代positions
    const categoryPosition = positionCategories.value.find(cat => String(cat.id) === typeIdStr);
    return categoryPosition ? categoryPosition.title : typeIdStr;
  };

  // 将所有可用岗位整合为一个扁平数组
  const flattenedPositions = [];
  positionCategories.value.forEach(category => {
    const positions = positionNamesMap.value[category.key] || [];
    positions.forEach(position => {
      flattenedPositions.push({
        id: position.id || position.key,
        name: position.title || position.name,
        category: category.title || category.name,
        typeId: category.id || category.key
      });
    });
  });

  if (props.selectedNode.type === 'category' && props.selectedNode.category) {
    const categoryId = props.selectedNode.category;
    const categoryLabel = getCategoryLabelById(categoryId);
    return flattenedPositions.filter(item => item.category === categoryLabel);
  }

  if (props.selectedNode.type === 'position' || props.selectedNode.type === 'level') {
    // 使用position属性进行过滤
    if (props.selectedNode.position) {
      return flattenedPositions.filter(item => item.name === props.selectedNode.position);
    }
  }

  if (formData.positionBelong) {
    return flattenedPositions.filter(item => item.category === formData.positionBelong);
  }

  return flattenedPositions;
});

// 筛选后的数据
const filteredData = computed(() => {
  let result = dataSource.value;

  // 获取字典ID对应的字典标签
  // const getCategoryLabelById = (typeId) => {
  //   const typeIdStr = String(typeId);
  //   // 查找匹配的category，使用positionCategories替代未定义的positions
  //   const categoryPosition = positionCategories.value.find(cat => String(cat.id) === typeIdStr);
  //   return categoryPosition ? categoryPosition.title : typeIdStr;
  // };

  // // 先根据树选中节点过滤
  // if (props.selectedNode.type === 'category' && props.selectedNode.category) {
  //   const categoryId = props.selectedNode.category;
  //   const categoryLabel = getCategoryLabelById(categoryId);

  //   // 双重过滤 - 使用原始字段和映射字段
  //   result = result.filter(item =>
  //     (item.departmentName === categoryLabel) ||
  //     (item.positionBelong === categoryLabel) ||
  //     (String(item.positionBelongDict?.id) === String(categoryId))
  //   );
  // }

  // if (props.selectedNode.type === 'position' && props.selectedNode.position) {
  //   // 这里需要同时匹配岗位类型和岗位名称
  //   const categoryName = props.selectedNode.category;
  //   const positionName = props.selectedNode.positionId;

  //   // 双重过滤 - 使用原始字段和映射字段，且同时匹配岗位类型和岗位名称
  //   result = result.filter(item => {
  //     // 转换为字符串进行宽松比较
  //     const categoryStr = String(categoryName).trim();
  //     const itemCategoryStr = String(item.positionBelong).trim();
  //     const positionStr = String(positionName).trim();
  //     const itemPositionStr = String(item.positionName).trim();

  //     // 多种匹配方式
  //     const positionExactMatch = (positionStr == itemPositionStr);
  //     const positionIncludesMatch = itemPositionStr.includes(positionStr) || positionStr.includes(itemPositionStr);
  //     const positionIgnoreCaseMatch = positionStr.toLowerCase() == itemPositionStr.toLowerCase();

  //     // 使用宽松比较(==)而不是严格比较(===)
  //     const categoryMatches = (categoryStr == itemCategoryStr);
  //     // 使用多种匹配方式的组合，提高匹配成功率
  //     const positionMatches = positionExactMatch || positionIncludesMatch || positionIgnoreCaseMatch;

  //     // 同时满足岗位类型和岗位名称条件
  //     return categoryMatches && positionMatches;
  //   });
  // }

  // if (props.selectedNode.type === 'level' && props.selectedNode.level) {
  //   // 合并职位和等级条件
  //   const categoryName = props.selectedNode.category;
  //   const positionName = props.selectedNode.positionId;
  //   const levelId = props.selectedNode.level;

  //   // 找到对应等级的文本名称
  //   const levelObj = filteredPositionLevels.value.find(level =>
  //     String(level.id) === String(levelId) ||
  //     level.title === levelId ||
  //     level.englishAbbr === levelId
  //   );
  //   const levelText = levelObj ? levelObj.title || levelObj.name : levelId;

  //   // 双重过滤 - 同时匹配岗位类型、岗位名称和等级
  //   result = result.filter(item => {
  //     // 转换为字符串进行宽松比较
  //     const categoryStr = String(categoryName).trim();
  //     const itemCategoryStr = String(item.positionBelong).trim();
  //     const positionStr = String(positionName).trim();
  //     const itemPositionStr = String(item.positionName).trim();
  //     const levelStr = String(levelText).trim();
  //     const itemLevelStr = String(item.positionLevel).trim();

  //     // 多种匹配方式
  //     const positionExactMatch = (positionStr == itemPositionStr);
  //     const positionIncludesMatch = itemPositionStr.includes(positionStr) || positionStr.includes(itemPositionStr);
  //     const positionIgnoreCaseMatch = positionStr.toLowerCase() == itemPositionStr.toLowerCase();

  //     // 使用宽松比较(==)而不是严格比较(===)
  //     const categoryMatches = (categoryStr == itemCategoryStr);
  //     // 使用多种匹配方式的组合，提高匹配成功率
  //     const positionMatches = positionExactMatch || positionIncludesMatch || positionIgnoreCaseMatch;
  //     const levelMatches = (levelStr == itemLevelStr);

  //     return categoryMatches && positionMatches && levelMatches;
  //   });
  // }

  // // 根据搜索文本筛选
  // if (searchForm.searchText) {
  //   const lowerSearchText = searchForm.searchText.toLowerCase();
  //   result = result.filter(item => {
  //     return (
  //       // 支持多个字段搜索
  //       (item.positionFullName && item.positionFullName.toLowerCase().includes(lowerSearchText)) ||
  //       (item.positionName && item.positionName.toLowerCase().includes(lowerSearchText)) ||
  //       (item.departmentName && item.departmentName.toLowerCase().includes(lowerSearchText)) ||
  //       (item.positionBelong && item.positionBelong.toLowerCase().includes(lowerSearchText)) ||
  //       (item.knowledgeBaseName && item.knowledgeBaseName.toLowerCase().includes(lowerSearchText)) ||
  //       (item.examSubject && item.examSubject.toLowerCase().includes(lowerSearchText))
  //     );
  //   });
  // }

  // // 根据下拉筛选条件
  // if (searchForm.positionBelong) {
  //   result = result.filter(item =>
  //     (item.departmentName === searchForm.positionBelong) ||
  //     (item.positionBelong === searchForm.positionBelong)
  //   );
  // }

  // if (searchForm.statusFilter) {
  //   result = result.filter(item => item.status === searchForm.statusFilter);
  // }

  return result;
});

// 弹窗可见性
const configModalVisible = ref(false);
const rulesModalVisible = ref(false);

// 是否为编辑模式
const isEdit = ref(false);

// 表单引用
const formRef = ref(null);
const rulesFormRef = ref(null);

// 表单数据
const formData = reactive({
  id: null,
  positionBelong: '',
  positionName: '',
  positionLevel: '',
  positionBelongId: undefined,
  positionNameId: undefined,
  positionLevelId: undefined,
  examSubject: [],
  status: '',
  practiceDuration: 60,
  practiceQuestionCount: 20,
  questionCount: 20,
  examCount: 3,
  examDuration: 90,
  needConfirmScore: true,
  passScore: 80,
  scoreReleaseRule: '自动发布',
  certificateValidDays: 90,
  questionMode: '时长', // 添加考试资格类型字段，默认为时长
  manualQuestionRatio: 50, // 人工出题占比，默认为50
  intelligentQuestionRatio: 50 // 智能出题占比，默认为50
});

// 规则表单数据
const rulesForm = reactive({
  examCount: 3,
  examDuration: 90,
  needConfirmScore: true,
  passScore: 80,
  scoreReleaseRule: '自动发布',
  questionDistribution: '选择题70%，问答题30%',
  questionType: ['单选', '多选'],
  difficultyLevel: '中级',
  questionCount: 20,
  certificateValidDays: 90,
  questionMode: '时长', // 添加考试资格类型字段，默认为时长
  practiceDuration: 60, // 练习时长
  practiceQuestionCount: 20, // 练习题目数量
  configType: 'practice', // 配置类型：practice-练习配置，exam-考试配置，all-通用配置
  manualQuestionRatio: 50, // 人工出题占比，默认为50
  intelligentQuestionRatio: 50 // 智能出题占比，默认为50
});

// 当前编辑的记录
const currentRecord = ref(null);

// 表单校验规则
const formRules = computed(() => {
  // 基本规则 - 所有状态都需要验证
  const baseRules = {
    positionBelongId: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
    positionNameId: [{ required: true, message: '请选择岗位名称', trigger: 'change' }],
    positionLevelId: [{ required: true, message: '请选择岗位等级', trigger: 'change' }],
    examSubject: [{
      required: true,
      message: '请选择练考科目（需先选择岗位类型和岗位名称）',
      trigger: 'change'
    }],
    status: [{ required: true, message: '请选择模式', trigger: 'change' }],
    practiceDuration: [{ required: true, type: 'number', message: '请输入练习时长', trigger: 'change' }],
    practiceQuestionCount: [{ required: true, type: 'number', message: '请输入练习题目数量', trigger: 'change' }],
  };

  // 必考状态特有规则
  if (formData.status === '必考') {
    return {
      ...baseRules,
      questionCount: [{ required: true, type: 'number', message: '请输入考试题目数量', trigger: 'change' }],
      examCount: [{ required: true, type: 'number', message: '请输入考试次数', trigger: 'change' }],
      examDuration: [{ required: true, type: 'number', message: '请输入考试时长', trigger: 'change' }],
      passScore: [{ required: true, type: 'number', message: '请输入正确题目数量', trigger: 'change' }],
      scoreReleaseRule: [{ required: true, message: '请选择考试规则', trigger: 'change' }]
    };
  }

  return baseRules;
});

// 搜索和筛选处理函数
const handleSearch = (values) => {
  // 更新搜索表单数据
  if (values) {
    // 先检查values中是否有undefined值，如果有，则清除对应的searchForm字段
    Object.keys(values).forEach(key => {
      if (values[key] === undefined) {
        searchForm[key] = '';
      } else {
        searchForm[key] = values[key];
      }
    });
  }

  // 重置到第一页
  resetPagination();

  // 从服务器获取数据
  fetchExamConfigs();
};

const resetSearch = () => {
  // 重置搜索表单，清除所有筛选条件
  searchForm.searchText = '';
  searchForm.positionName = '';
  searchForm.level = '';
  searchForm.statusFilter = '';

  // 重置到第一页
  resetPagination();

  // 从服务器重新获取数据
  fetchExamConfigs();
};

const handleFilterChange = () => {
  // 已通过计算属性实现
};

// 监听选中节点变化，预设表单值
watch(() => props.selectedNode, (newVal) => {
  if (newVal.type === 'category') {
    formData.positionBelong = newVal.category;
    formData.positionName = '';
    formData.positionLevel = '';
  } else if (newVal.type === 'position') {
    formData.positionBelong = newVal.category;
    formData.positionName = newVal.position;
    formData.positionLevel = '';
  } else if (newVal.type === 'level') {
    formData.positionBelong = newVal.category;
    formData.positionName = newVal.position;

    // 将级别ID或英文缩写转换为等级名称
    // 尝试从positionLevelsMap中查找匹配的级别
    let levelText = '';

    // 先找到对应的岗位分类
    const category = positionCategories.value.find(cat => cat.title === newVal.category);
    if (category && category.key) {
      // 再找到对应的岗位
      const positions = positionNamesMap.value[category.key] || [];
      const position = positions.find(pos => pos.title === newVal.position);

      if (position && position.key) {
        // 最后找到对应的等级
        const levels = positionLevelsMap.value[position.key] || [];
        const levelObj = levels.find(level =>
          level.englishAbbr === newVal.level ||
          String(level.id) === String(newVal.level) ||
          level.title === newVal.level
        );

        if (levelObj) {
          levelText = levelObj.title || levelObj.name;
        }
      }
    }

    formData.positionLevel = levelText || newVal.level;
  }
}, { immediate: true });

// 监听状态变化，进行表单重新验证并加载通用规则
watch(() => formData.status, async (newStatus) => {
  // 切换状态后需要重新验证表单
  if (formRef.value) {
    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      formRef.value.validate().catch(() => {
        // 忽略验证失败的错误，仅为触发验证
      });
    });
  }

  // 当切换到"必考"状态时，获取并填充通用规则配置值
  if (newStatus === '必考' && !isEdit.value) {
    try {
      loading.value = true;

      // 获取考试通用规则
      const rulesResponse = await getExamRules();

      if (rulesResponse && rulesResponse.id) {
        // 存在通用规则，填充表单数据
        formData.examCount = rulesResponse.examCount || 3;
        formData.examDuration = rulesResponse.examDuration || 90;
        formData.needConfirmScore = rulesResponse.needConfirmScore !== undefined ? rulesResponse.needConfirmScore : true;
        formData.passScore = rulesResponse.passScore || 80;
        formData.scoreReleaseRule = rulesResponse.scoreReleaseRule || '自动发布';
        // 同步考试资格类型
        formData.questionMode = rulesResponse.questionMode || '时长';

        // 如果通用规则中设置了证书有效期，也填充
        if (rulesResponse.certificateValidDays) {
          formData.certificateValidDays = rulesResponse.certificateValidDays;
        }

        message.success('已自动填充考试通用规则配置');
      }
    } catch (error) {
      console.error('获取考试通用规则失败:', error);
    } finally {
      loading.value = false;
    }
  }
});

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 完全清空formData中的所有字段
  Object.assign(formData, {
    id: null,
    positionBelong: '',
    positionName: '',
    positionLevel: '',
    positionBelongId: undefined,
    positionNameId: undefined,
    positionLevelId: undefined,
    examSubject: [],
    status: '',
    practiceDuration: 60,
    practiceQuestionCount: 20,
    questionCount: 20,
    examCount: 3,
    examDuration: 90,
    needConfirmScore: true,
    passScore: 80,
    scoreReleaseRule: '自动发布',
    certificateValidDays: 90,
    questionMode: '时长', // 添加考试资格类型字段，默认为时长
    manualQuestionRatio: 50, // 人工出题占比，默认为50
    intelligentQuestionRatio: 50 // 智能出题占比，默认为50
  });
};

// 修改showAddModal方法
const showAddModal = async () => {
  // 先检查是否已配置考试通用规则
  loading.value = true;
  try {
    // 获取考试通用规则
    const rulesResponse = await getExamRules();

    // 检查是否存在考试通用规则配置
    if (!rulesResponse || !rulesResponse.id) {
      // 如果不存在配置，提示用户先配置
      Modal.confirm({
        title: '配置提示',
        content: '您还未配置考试通用规则，请先配置后再新增。',
        okText: '去配置',
        cancelText: '取消',
        onOk: () => {
          // 打开规则配置弹窗
          showRulesModal();
        }
      });
      loading.value = false;
      return;
    }

    // 如果已配置考试通用规则，继续新增配置流程
    isEdit.value = false;
    // 先确保表单彻底重置
    resetForm();

    let dataRef = props.selectedNode.dataRef
    if(!dataRef){
      configModalVisible.value = true;
      loading.value = false;
      return
    }


    // 预填充选中节点的信息
    if (dataRef.type === 'category') {
      // 查找对应的岗位类别
      const selectedType = positionTypes.value.find(type => String(type.id) === String(dataRef.typeId));
      if (selectedType) {
        // 使用labelInValue格式设置岗位类型
        formData.positionBelongId = {
          value: selectedType.id,
          label: selectedType.name
        };
        formData.positionBelong = selectedType.name;
      }
    } else if (dataRef.type === 'position') {
      // 查找对应的岗位类别
      const selectedType = positionTypes.value.find(type => String(type.id) === String(dataRef.typeId));
      if (selectedType) {
        // 使用labelInValue格式设置岗位类型
        formData.positionBelongId = {
          value: selectedType.id,
          label: selectedType.name
        };
        formData.positionBelong = selectedType.name;
      }

      // 查找对应的岗位名称
      const selectedPosition = positionNames.value.find(pos => String(pos.id) === String(dataRef.positionId));
      if (selectedPosition) {
        // 使用labelInValue格式设置岗位名称
        formData.positionNameId = {
          value: selectedPosition.id,
          label: selectedPosition.name
        };
        formData.positionName = selectedPosition.name;
      }
    } else if (dataRef.type === 'level') {
      // 查找对应的岗位类别
      const selectedType = positionTypes.value.find(type => String(type.id) === String(dataRef.typeId));
      if (selectedType) {
        // 使用labelInValue格式设置岗位类型
        formData.positionBelongId = {
          value: selectedType.id,
          label: selectedType.name
        };
        formData.positionBelong = selectedType.name;
      }

      // 查找对应的岗位名称
      const selectedPosition = positionNames.value.find(pos => String(pos.id) === String(dataRef.positionId));
      if (selectedPosition) {
        // 使用labelInValue格式设置岗位名称
        formData.positionNameId = {
          value: selectedPosition.id,
          label: selectedPosition.name
        };
        formData.positionName = selectedPosition.name;
      }

      // 查找对应的岗位等级
      const selectedLevel = levelOptions.value.find(level => String(level.id) === String(dataRef.levelId));
      if (selectedLevel) {
        // 使用labelInValue格式设置岗位等级
        formData.positionLevelId = {
          value: selectedLevel.id,
          label: selectedLevel.name
        };
        formData.positionLevel = selectedLevel.name;
      }
    }



    // 设置默认状态为"必考"并填充通用规则
    formData.status = '必考';

    // 填充通用规则配置
    formData.examCount = rulesResponse.examCount || 3;
    formData.examDuration = rulesResponse.examDuration || 90;
    formData.needConfirmScore = rulesResponse.needConfirmScore !== undefined ? rulesResponse.needConfirmScore : true;
    formData.passScore = rulesResponse.passScore || 80;
    formData.scoreReleaseRule = rulesResponse.scoreReleaseRule || '自动发布';

    // 如果通用规则中设置了证书有效期，也填充
    if (rulesResponse.certificateValidDays) {
      formData.certificateValidDays = rulesResponse.certificateValidDays;
    }

    // 填充人工出题和智能出题占比
    if (rulesResponse.manualQuestionRatio !== undefined) {
      formData.manualQuestionRatio = rulesResponse.manualQuestionRatio;
    }
    if (rulesResponse.intelligentQuestionRatio !== undefined) {
      formData.intelligentQuestionRatio = rulesResponse.intelligentQuestionRatio;
    }

    // 使用nextTick确保数据加载完成后再显示模态框
    nextTick(() => {
      // 显示模态框
      configModalVisible.value = true;
    });
  } catch (error) {
    console.error('检查考试通用规则失败:', error);
    message.error('检查考试通用规则失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 编辑配置
const editConfig = (record) => {
  isEdit.value = true;
  currentRecord.value = record;

  // 先重置表单并清除验证
  resetForm();
  // formRef.value?.clearValidate();


  // 从记录中提取数据
  // 基础字段直接赋值
  formData.id = record.id;
  formData.status = record.status || '必练';
  formData.practiceDuration = record.practiceDuration || 60;
  formData.practiceQuestionCount = record.practiceQuestionCount || 20;

  // 必考特有字段
  if (record.status === '必考') {
    formData.questionCount = record.questionCount || 20;
    formData.examCount = record.examCount || 3;
    formData.examDuration = record.examDuration || 90;
    formData.needConfirmScore = record.needConfirmScore === undefined ? true : record.needConfirmScore;
    formData.passScore = record.passScore || 80;
    formData.scoreReleaseRule = record.scoreReleaseRule || '自动发布';
    formData.certificateValidDays = record.certificateValidDays || 90;
    // 添加人工出题和智能出题占比字段的回显
    formData.manualQuestionRatio = record.manualQuestionRatio ;
    formData.intelligentQuestionRatio = record.intelligentQuestionRatio ;
  }

  // 设置岗位相关字段 - 直接使用记录中的ID值设置下拉选择器
  // 尝试多种可能的ID字段
  const typeId = record.positionBelong || record.typeId || record.positionTypeId || record.positionBelongId;
  const positionId = record.positionName || record.nameId || record.positionNameId;
  const levelId = record.positionLevel || record.levelId || record.positionLevelId;

  // 查找匹配的岗位类型
  const selectedType = positionTypes.value.find(type => String(type.id) === String(typeId));
  if (selectedType) {
    // 使用labelInValue格式设置岗位类型
    formData.positionBelongId = {
      value: selectedType.id,
      label: selectedType.name
    };
    formData.positionBelong = selectedType.name;
  }

  // 查找匹配的岗位名称
  const selectedPosition = positionNames.value.find(pos => String(pos.id) === String(positionId));
  if (selectedPosition) {
    // 使用labelInValue格式设置岗位名称
    formData.positionNameId = {
      value: selectedPosition.id,
      label: selectedPosition.name
    };
    formData.positionName = selectedPosition.name;
  }

  // 查找匹配的岗位等级
  const selectedLevel = levelOptions.value.find(lvl => String(lvl.id) === String(levelId));
  if (selectedLevel) {
    // 使用labelInValue格式设置岗位等级
    formData.positionLevelId = {
      value: selectedLevel.id,
      label: selectedLevel.name
    };
    formData.positionLevel = selectedLevel.name;
  }



  // 设置练考科目，编辑模式下只允许单选
  if (record.examSubjectId) {
    // 编辑模式下直接设置为单个值，而不是数组
    formData.examSubject = record.examSubjectId;
  } else if (record.examSubject) {
    // 尝试从知识库列表中查找匹配的科目
    const knowledge = knowledgeBaseList.value.find(k => k.name === record.examSubject);
    // 编辑模式下直接设置为单个值，而不是数组
    formData.examSubject = knowledge ? knowledge.id : record.examSubject;
  }

  // 显示模态框
  configModalVisible.value = true;
};

// 显示规则配置弹窗
const showRulesModal = async (record = null, configType = 'practice') => {
  currentRecord.value = record;
  loading.value = true;
  // 存储配置类型
  rulesForm.configType = configType;

  try {
    // 配置默认值 - 无论是否存在配置，都设置初始默认值
    rulesForm.examCount = 3;
    rulesForm.examDuration = 90;
    rulesForm.needConfirmScore = true;
    rulesForm.passScore = 80;
    rulesForm.scoreReleaseRule = '自动发布';
    rulesForm.questionDistribution = '选择题70%，问答题30%';
    rulesForm.questionType = ['单选', '多选'];
    rulesForm.difficultyLevel = '中级';
    rulesForm.questionCount = 20;
    rulesForm.certificateValidDays = 90;
    rulesForm.questionMode = '时长';
    rulesForm.practiceDuration = 60;
    rulesForm.practiceQuestionCount = 20;
    rulesForm.manualQuestionRatio = 50;
    rulesForm.intelligentQuestionRatio = 50;

    if (record) {
      // 如果传入了记录，则回显该记录的规则配置
      rulesForm.examCount = record.examCount || rulesForm.examCount;
      rulesForm.examDuration = record.examDuration || rulesForm.examDuration;
      rulesForm.needConfirmScore = record.needConfirmScore !== undefined ? record.needConfirmScore : rulesForm.needConfirmScore;
      rulesForm.passScore = record.passScore || rulesForm.passScore;
      rulesForm.certificateValidDays = record.certificateValidDays || rulesForm.certificateValidDays;
      rulesForm.questionMode = record.questionMode || rulesForm.questionMode;
      rulesForm.practiceDuration = record.practiceDuration || rulesForm.practiceDuration;
      rulesForm.practiceQuestionCount = record.practiceQuestionCount || rulesForm.practiceQuestionCount;
      rulesForm.questionCount = record.questionCount || rulesForm.questionCount;
      rulesForm.manualQuestionRatio = record.manualQuestionRatio ;
      rulesForm.intelligentQuestionRatio = record.intelligentQuestionRatio ;
    } else {
      // 否则获取全局规则配置
      const response = await getExamRules();
      if (response && response.id) {
        rulesForm.examCount = response.examCount || rulesForm.examCount;
        rulesForm.examDuration = response.examDuration || rulesForm.examDuration;
        rulesForm.needConfirmScore = response.needConfirmScore !== undefined ? response.needConfirmScore : rulesForm.needConfirmScore;
        rulesForm.passScore = response.passScore || rulesForm.passScore;
        rulesForm.scoreReleaseRule = response.scoreReleaseRule || rulesForm.scoreReleaseRule;
        rulesForm.questionDistribution = response.questionDistribution || rulesForm.questionDistribution;
        rulesForm.questionType = response.questionType ? response.questionType.split(',') : rulesForm.questionType;
        rulesForm.difficultyLevel = response.difficultyLevel || rulesForm.difficultyLevel;
        rulesForm.questionCount = response.questionCount || rulesForm.questionCount;
        rulesForm.certificateValidDays = response.certificateValidDays || rulesForm.certificateValidDays;
        rulesForm.questionMode = response.questionMode || rulesForm.questionMode;
        rulesForm.practiceDuration = response.practiceDuration || rulesForm.practiceDuration;
        rulesForm.practiceQuestionCount = response.practiceQuestionCount || rulesForm.practiceQuestionCount;
        rulesForm.manualQuestionRatio = response.manualQuestionRatio ;
        rulesForm.intelligentQuestionRatio = response.intelligentQuestionRatio ;
      }
    }

    // 显示弹窗
    rulesModalVisible.value = true;
  } catch (error) {
    console.error('获取规则配置失败:', error);
    message.error('获取规则配置失败: ' + error.message);

    // 即使获取规则失败，也打开弹窗并使用默认值
    rulesModalVisible.value = true;
  } finally {
    loading.value = false;
  }
};

// 处理删除
const handleDelete = async (record) => {
  try {
    loading.value = true;
    await deleteExamConfig(record.id);
    message.success('删除成功');
    // 重新获取列表数据
    await fetchExamConfigs();
  } catch (error) {
    message.error('删除失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 处理配置弹窗确认
const handleConfigModalOk = async () => {
    // 继续表单验证
    try {
        await formRef.value.validate();
        loading.value = true;

        // 构建基础请求数据的辅助函数
        const buildBaseData = (examSubject = null) => {
          const data = {};
          
          // 基础字段 - 只传递必要的输入字段
          if (formData.positionBelongId?.value || formData.positionBelongId) {
            data.positionBelong = formData.positionBelongId?.value || formData.positionBelongId;
          }
          
          if (formData.positionNameId?.value || formData.positionNameId) {
            data.positionName = formData.positionNameId?.value || formData.positionNameId;
          }
          
          if (formData.positionLevelId?.value || formData.positionLevelId) {
            data.positionLevel = formData.positionLevelId?.value || formData.positionLevelId;
          }
          
          if (formData.status !== undefined) data.status = formData.status;

          // 处理练考科目
          const currentExamSubject = examSubject || formData.examSubject;
          if (currentExamSubject && typeof currentExamSubject !== 'string') {
            const matchedKnowledge = knowledgeBaseList.value.find(item => item.id === currentExamSubject);
            if (matchedKnowledge) {
              data.examSubject = matchedKnowledge.name;
            }
          } else if (currentExamSubject) {
            data.examSubject = currentExamSubject;
          }

          return data;
        };

        // 编辑模式：只传递基础字段
        if (isEdit.value) {
            const requestData = {
              id: formData.id,
              ...buildBaseData()
            };

            try {
                await updateExamConfig(requestData);
                message.success('更新成功');
                await fetchExamConfigs();
                configModalVisible.value = false;
            } catch (error) {
                console.error('更新失败:', error);
                message.error('更新失败: ' + (error.message || '未知错误'));
            }
            loading.value = false;
            return;
        }

        // 新增模式：包含规则字段用于初始化
        const buildNewConfigData = (examSubject = null) => {
          const data = buildBaseData(examSubject);
          
          // 练习相关字段（所有配置都需要）- 只传递输入框字段
          if (formData.questionMode !== undefined) data.questionMode = formData.questionMode;
          if (formData.practiceDuration !== undefined) data.practiceDuration = formData.practiceDuration;
          if (formData.practiceQuestionCount !== undefined) data.practiceQuestionCount = formData.practiceQuestionCount;

          // 如果是必考状态，添加考试相关字段 - 只传递输入框字段
          if (formData.status === '必考') {
            if (formData.examCount !== undefined) data.examCount = formData.examCount;
            if (formData.examDuration !== undefined) data.examDuration = formData.examDuration;
            if (formData.needConfirmScore !== undefined) data.needConfirmScore = formData.needConfirmScore;
            if (formData.passScore !== undefined) data.passScore = formData.passScore;
            if (formData.scoreReleaseRule !== undefined) data.scoreReleaseRule = formData.scoreReleaseRule;
            if (formData.certificateValidDays !== undefined) data.certificateValidDays = formData.certificateValidDays;
            if (formData.questionCount !== undefined) data.questionCount = formData.questionCount;
            if (formData.manualQuestionRatio !== undefined) data.manualQuestionRatio = formData.manualQuestionRatio;
            if (formData.intelligentQuestionRatio !== undefined) data.intelligentQuestionRatio = formData.intelligentQuestionRatio;
          }

          return data;
        };

        // 多选科目的情况
        if (Array.isArray(formData.examSubject) && formData.examSubject.length > 0) {
            try {
                const examSubjects = [...formData.examSubject];
                for (let i = 0; i < examSubjects.length; i++) {
                    const requestData = buildNewConfigData(examSubjects[i]);
                    await addExamConfig(requestData);
                }
                message.success('创建成功');
                await fetchExamConfigs();
                configModalVisible.value = false;
            } catch (error) {
                console.error('创建失败:', error);
                message.error('创建失败: ' + (error.message || '未知错误'));
            } finally {
                loading.value = false;
            }
            return;
        }

        // 单选情况
        try {
            const requestData = buildNewConfigData();
            await addExamConfig(requestData);
            message.success('创建成功');
            await fetchExamConfigs();
            configModalVisible.value = false;
        } catch (error) {
            console.error('创建失败:', error);
            message.error('创建失败: ' + (error.message || '未知错误'));
        }
    } catch (validationError) {
        console.error('表单验证失败:', validationError);
    } finally {
        loading.value = false;
    }
};

// 处理配置弹窗取消
const handleConfigModalCancel = () => {
  formRef.value?.clearValidate();
  // resetForm();
  configModalVisible.value = false;
};

// 处理规则弹窗确认
const handleRulesModalOk = async () => {
  try {
    loading.value = true;

    if (currentRecord.value) {
      // 更新单个配置的规则
      if (rulesForm.configType === 'practice') {
        // 更新练习配置，只传递练习相关的输入框字段
        const practiceData = {};
        if (rulesForm.questionMode !== undefined) practiceData.questionMode = rulesForm.questionMode;
        if (rulesForm.practiceDuration !== undefined) practiceData.practiceDuration = rulesForm.practiceDuration;
        if (rulesForm.practiceQuestionCount !== undefined) practiceData.practiceQuestionCount = rulesForm.practiceQuestionCount;
        
        await updateConfigPracticeRules(currentRecord.value.id, practiceData);
        message.success('练习配置更新成功');
      } else if (rulesForm.configType === 'exam') {
        // 更新考试配置，只传递考试相关的输入框字段
        const examData = {};
        if (rulesForm.examCount !== undefined) examData.examCount = rulesForm.examCount;
        if (rulesForm.examDuration !== undefined) examData.examDuration = rulesForm.examDuration;
        if (rulesForm.needConfirmScore !== undefined) examData.needConfirmScore = rulesForm.needConfirmScore;
        if (rulesForm.passScore !== undefined) examData.passScore = rulesForm.passScore;
        if (rulesForm.scoreReleaseRule !== undefined) examData.scoreReleaseRule = rulesForm.scoreReleaseRule;
        if (rulesForm.certificateValidDays !== undefined) examData.certificateValidDays = rulesForm.certificateValidDays;
        if (rulesForm.questionCount !== undefined) examData.questionCount = rulesForm.questionCount;
        if (rulesForm.manualQuestionRatio !== undefined) examData.manualQuestionRatio = rulesForm.manualQuestionRatio;
        if (rulesForm.intelligentQuestionRatio !== undefined) examData.intelligentQuestionRatio = rulesForm.intelligentQuestionRatio;
        
        await updateConfigExamRules(currentRecord.value.id, examData);
        message.success('考试配置更新成功');
      }

      // 重新获取列表数据
      await fetchExamConfigs();
      rulesModalVisible.value = false;
    } else {
      // 更新通用规则配置
      if (rulesForm.configType === 'practice') {
        // 显示确认对话框
        Modal.confirm({
          title: '练习规则更新确认',
          content: '更新通用练习规则将同步应用到所有配置，确定要更新吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 更新通用练习规则，只传递练习相关的输入框字段
              const practiceData = {};
              if (rulesForm.questionMode !== undefined) practiceData.questionMode = rulesForm.questionMode;
              if (rulesForm.practiceDuration !== undefined) practiceData.practiceDuration = rulesForm.practiceDuration;
              if (rulesForm.practiceQuestionCount !== undefined) practiceData.practiceQuestionCount = rulesForm.practiceQuestionCount;
              
              await updatePracticeRules(practiceData);
              message.success('通用练习规则配置更新成功，已同步应用到所有配置');

              // 重新获取列表数据
              await fetchExamConfigs();
              rulesModalVisible.value = false;
            } catch (error) {
              console.error('保存练习规则失败:', error);
              message.error('保存练习规则失败: ' + error.message);
            }
          }
        });
      } else if (rulesForm.configType === 'exam') {
        // 显示确认对话框
        Modal.confirm({
          title: '考试规则更新确认',
          content: '更新通用考试规则将同步应用到所有必考配置，确定要更新吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 更新通用考试规则，只传递考试相关的输入框字段
              const examData = {};
              if (rulesForm.examCount !== undefined) examData.examCount = rulesForm.examCount;
              if (rulesForm.examDuration !== undefined) examData.examDuration = rulesForm.examDuration;
              if (rulesForm.needConfirmScore !== undefined) examData.needConfirmScore = rulesForm.needConfirmScore;
              if (rulesForm.passScore !== undefined) examData.passScore = rulesForm.passScore;
              if (rulesForm.scoreReleaseRule !== undefined) examData.scoreReleaseRule = rulesForm.scoreReleaseRule;
              if (rulesForm.certificateValidDays !== undefined) examData.certificateValidDays = rulesForm.certificateValidDays;
              if (rulesForm.questionCount !== undefined) examData.questionCount = rulesForm.questionCount;
              if (rulesForm.manualQuestionRatio !== undefined) examData.manualQuestionRatio = rulesForm.manualQuestionRatio;
              if (rulesForm.intelligentQuestionRatio !== undefined) examData.intelligentQuestionRatio = rulesForm.intelligentQuestionRatio;
              
              await updateExamRules(examData);
              message.success('通用考试规则配置更新成功，已同步应用到所有必考配置');

              // 重新获取列表数据
              await fetchExamConfigs();
              rulesModalVisible.value = false;
            } catch (error) {
              console.error('保存考试规则失败:', error);
              message.error('保存考试规则失败: ' + error.message);
            }
          }
        });
      }
      return; // 等待确认对话框操作
    }
  } catch (error) {
    console.error('更新规则配置失败:', error);
    message.error('更新规则配置失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 处理规则弹窗取消
const handleRulesModalCancel = () => {
  rulesFormRef.value?.clearValidate();
  rulesModalVisible.value = false;
};

// 人工出题占比变更处理
const handleManualRatioChange = (value) => {
  if (value !== null && value !== undefined) {
    // 确保值在0-100范围内
    const manualRatio = Math.max(0, Math.min(100, value));
    rulesForm.manualQuestionRatio = manualRatio;
    // 自动计算智能出题占比
    rulesForm.intelligentQuestionRatio = 100 - manualRatio;
  }
};

// 智能出题占比变更处理
const handleIntelligentRatioChange = (value) => {
  if (value !== null && value !== undefined) {
    // 确保值在0-100范围内
    const intelligentRatio = Math.max(0, Math.min(100, value));
    rulesForm.intelligentQuestionRatio = intelligentRatio;
    // 自动计算人工出题占比
    rulesForm.manualQuestionRatio = 100 - intelligentRatio;
  }
};

// 处理同步规则
const syncLoading = ref(false);

const handleSyncRules = async () => {
  try {
    syncLoading.value = true;
    await syncExamRules();
    message.success('规则已同步到所有配置');
    // 重新获取列表数据
    await fetchExamConfigs();
  } catch (error) {
    console.error('同步规则失败:', error);
    message.error('同步规则失败: ' + error.message);
  } finally {
    syncLoading.value = false;
  }
};

// 向父组件暴露 resetPagination 和 handleSearch 方法
defineExpose({
  resetPagination,
  handleSearch
});
</script>

<style scoped>
.table-container {
  flex: 1;
  /* overflow: auto; */
  height: calc(100% - 64px);
}
.exam-config-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.table-top-left {
  display: flex;
  justify-content: flex-start;
}

.table-top-right {
  display: flex;
  justify-content: flex-end;
}

.custom-primary-button {
  background: #a18cd1;
  border-color: #a18cd1;
  min-width: 88px;
}

.custom-primary-button:hover,
.custom-primary-button:focus {
  background: #9370db;
  border-color: #9370db;
}

/* 分组标题样式 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

/* 移除重复的.custom-button样式及hover样式，使用全局样式 */

/* 使用特定格式的action-buttons样式，覆盖全局定义 */
</style>