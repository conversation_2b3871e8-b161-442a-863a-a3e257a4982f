<!-- 考试审核组件 -->
<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
    </page-header>

    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="hasBatchPermission">
      <a-space>
        <a-button
          type="primary"
          :disabled="!canBatchReview"
          @click="handleBatchReview"
          class="batch-button"
          v-permission="'exam.review.qualification'"
        >
          <check-circle-outlined />
          批量资格审核 {{ selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : '' }}
        </a-button>
        <a-button
          type="primary"
          :disabled="!canBatchConfirm"
          @click="handleBatchConfirm"
          class="batch-button"
          v-permission="'exam.review.score'"
        >
          <check-outlined />
          批量成绩审核 {{ selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : '' }}
        </a-button>
        <a-button 
          :disabled="selectedRowKeys.length === 0"
          @click="clearSelection" 
          class="clear-button"
        >
          清空选择
        </a-button>
      </a-space>
    </div>

    <!-- 表格 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      rowKey="id"
      :row-selection="hasBatchPermission ? rowSelection : null"
      :scroll="{ x: 1400, y: 'calc(100vh - 450px)' }"
      class="exam-review-table"
    >
      <!-- 自定义列渲染 -->
      <template #bodyCell="{ column, record }">
        <!-- 考试科目 -->
        <template v-if="column.dataIndex === 'examTitle'">
          {{ record.examTitle || record.examType || '' }}
        </template>

        <!-- 岗位名称 -->
        <template v-if="column.dataIndex === 'positionName'">
          <span v-if="record.positionLevel">{{ getPositionNameById(record.positionName) || '未知岗位' }}({{ getLevelNameById(record.positionLevel) || '' }})</span>
          <span v-else>{{ getPositionNameById(record.positionName) || '未知岗位' }}</span>
        </template>

        <!-- 审核状态 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ reviewStatusMap[record.status] || record.status || '未知' }}
          </a-tag>
        </template>

        <!-- 成绩确认状态 -->
        <template v-if="column.dataIndex === 'scoreConfirmStatus'">
          <a-tag :color="getScoreStatusColor(record.scoreConfirmStatus)">
            {{ getScoreStatusText(record.scoreConfirmStatus) }}
          </a-tag>
        </template>

        <!-- 审核驳回原因 -->
        <template v-if=" column.key === 'reviewComments'">
          <a-tooltip>
            <template #title>{{ record.reviewInfo?.reviewComments||'' }}</template>
            <!-- {{ getToolTip(record.reviewInfo?.reviewComments||'') }} -->
              <ExclamationCircleOutlined v-if="record.reviewInfo?.reviewComments" style="color: #a18cd1;" />
          </a-tooltip>
        </template>
        <!-- 成绩确认驳回原因 -->
        <template v-if=" column.key === 'confirmComments'">
          <a-tooltip>
            <template #title>{{ record.confirmInfo.confirmComments }}</template>
            <ExclamationCircleOutlined v-if="record.confirmInfo.confirmComments" style="color: #a18cd1;" />

          <!-- {{ getToolTip(record.confirmInfo.confirmComments) }} -->

          </a-tooltip>
        </template>

        <!-- 审核人 -->
        <template v-if="column.dataIndex === 'reviewInfo' && column.key === 'reviewer'">
          {{ getReviewerInfo(record.reviewInfo) }}
        </template>

        <!-- 审核时间 -->
        <template v-if="column.dataIndex === 'reviewInfo' && column.key === 'reviewTime'">
          {{ getReviewTimeInfo(record.reviewInfo) }}
        </template>

        <!-- 确认人 -->
        <template v-if="column.dataIndex === 'reviewInfo' && column.key === 'confirmPerson'">
          {{ getConfirmPersonInfo(record.reviewInfo) }}
        </template>

        <!-- 确认时间 -->
        <template v-if="column.dataIndex === 'reviewInfo' && column.key === 'confirmTime'">
          {{ getConfirmTimeInfo(record.reviewInfo) }}
        </template>

        <!-- 考试时间 -->
        <template v-if="column.dataIndex === 'examDate'">
          {{ record.examDate}}
        </template>

        <!-- 操作按钮 -->
        <template v-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button
              type="link"
              size="small"
              :disabled="record.status !== '1'"
              @click="handleReviewApplication(record)"
              class="custom-button"
              v-permission="'exam.review.qualification'"
            >
              <check-circle-outlined />资格审核
            </a-button>
            <a-button
              type="link"
              size="small"
              :disabled="record.scoreConfirmStatus!=='1'||record.status !== '2'"
              @click="handleConfirmScore(record)"
              class="custom-button"
              v-permission="'exam.review.score'"
            >
              <check-outlined />成绩审核
            </a-button>
          </div>
        </template>
      </template>
    </base-table>

    <!-- 审核申请弹窗 -->
    <a-modal
      v-model:visible="reviewModalVisible"
      title="资格审核"
      @ok="submitReview"
      @cancel="handleReviewModalCancel"
      width="600px"
    >
      <div class="review-modal-content">
        <div class="review-info">
          <p><strong>姓名：</strong>{{ currentRecord?.applicantName || '-' }}</p>
          <p><strong>考试科目：</strong>{{ currentRecord?.examTitle || '-' }}</p>
          <p><strong>岗位名称：</strong>{{ getPositionNameById(currentRecord?.positionName) || '未知岗位' }}({{ getLevelNameById(currentRecord?.positionLevel) || '-' }})</p>
          <p><strong>申请时间：</strong>{{ currentRecord?.created_at || '-' }}</p>
        </div>

        <a-divider />

        <a-form
          :model="reviewForm"
          :rules="reviewFormRules"
          ref="reviewFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="审核结果" name="result">
            <a-radio-group v-model:value="reviewForm.result">
              <a-radio value="2">通过</a-radio>
              <a-radio value="3">不通过</a-radio>
            </a-radio-group>
          </a-form-item>

          <template v-if="reviewForm.result === '2'">
            <a-form-item label="考试开始时间" name="examTime">
              <a-radio-group v-model:value="reviewForm.timeOption">
                <a-radio value="不限">不限时间</a-radio>
                <a-radio value="指定">指定时间</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item
              v-if="reviewForm.timeOption === '指定'"
              label="选择时间"
              name="specificTime"
            >
              <a-date-picker
                v-model:value="reviewForm.specificTime"
                show-time
                placeholder="选择日期时间"
                style="width: 100%"
              />
            </a-form-item>
          </template>

          <a-form-item
            v-if="reviewForm.result === '3'"
            label="不通过原因"
            name="rejectReason"
          >
            <a-textarea
              v-model:value="reviewForm.rejectReason"
              :rows="4"
              placeholder="请输入不通过原因"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 成绩确认弹窗 -->
    <a-modal
      v-model:visible="confirmModalVisible"
      title="成绩审核"
      @ok="handleConfirmModalOk"
      @cancel="handleConfirmModalCancel"
      width="800px"
    >
      <div class="confirm-modal-content">
        <div class="confirm-info">
          <p><strong>姓名：</strong>{{ currentRecord?.applicantName || '-' }}</p>
          <p><strong>考试科目：</strong>{{ currentRecord?.examTitle || '-' }}</p>
          <p><strong>岗位名称：</strong>{{ getPositionNameById(currentRecord?.positionName) || '未知岗位' }}({{ getLevelNameById(currentRecord?.positionLevel) || '-' }})</p>
          <p><strong>申请时间：</strong>{{ currentRecord?.created_at || '-' }}</p>
        </div>

        <a-divider />

        <div class="exam-records">
          <h3>考试记录</h3>

          <a-empty v-if="!currentRecord || !currentRecord.examRecords || currentRecord.examRecords.length === 0" description="暂无考试记录" />

          <a-table
            v-else
            :columns="examRecordColumns"
            :dataSource="currentRecord.examRecords"
            :pagination="false"
            size="small"
            :scroll="{ y: 240 }"
            bordered
          >
            <!-- 分数列，添加颜色标识 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'score'">
                <span :class="getScoreClass(record.score)">{{ record.score }}</span>
              </template>
            </template>
          </a-table>

          <div v-if="currentRecord && currentRecord.examRecords && currentRecord.examRecords.length > 0" class="score-summary">            <p><strong>总考试次数：</strong>{{ currentRecord.examRecords.length }} 次</p>            <p><strong>平均分：</strong><span :class="getScoreClass(averageScore)">{{ averageScore }}</span></p>            <p><strong>最高分：</strong><span :class="getScoreClass(maxScore)">{{ maxScore }}</span></p>            <p><strong>最低分：</strong><span :class="getScoreClass(minScore)">{{ minScore }}</span></p>          </div>
        </div>

        <a-divider />

        <a-form
          :model="confirmForm"
          :rules="confirmFormRules"
          ref="confirmFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="确认结果" name="result">
            <a-radio-group v-model:value="confirmForm.result">
              <a-radio value="2">通过</a-radio>
              <a-radio value="3">不通过</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item
            v-if="confirmForm.result === '3'"
            label="不通过原因"
            name="rejectReason"
          >
            <a-textarea
              v-model:value="confirmForm.rejectReason"
              :rows="4"
              placeholder="请输入不通过原因"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 批量资格审核弹窗 -->
    <a-modal
      v-model:visible="batchReviewModalVisible"
      title="批量资格审核"
      @ok="submitBatchReview"
      @cancel="batchReviewModalVisible = false"
      width="600px"
    >
      <div class="batch-review-modal-content">
        <div class="batch-info">
          <p><strong>选中记录数：</strong>{{ selectedRowKeys.length }} 条</p>
          <p><strong>操作说明：</strong>将对所有选中的记录执行相同的审核操作</p>
        </div>

        <a-divider />

        <a-form
          :model="batchReviewForm"
          :rules="batchReviewFormRules"
          ref="batchReviewFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="审核结果" name="result">
            <a-radio-group v-model:value="batchReviewForm.result">
              <a-radio value="2">通过</a-radio>
              <a-radio value="3">不通过</a-radio>
            </a-radio-group>
          </a-form-item>

          <template v-if="batchReviewForm.result === '2'">
            <a-form-item label="考试开始时间" name="timeOption">
              <a-radio-group v-model:value="batchReviewForm.timeOption">
                <a-radio value="不限">不限时间</a-radio>
                <a-radio value="指定">指定时间</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item
              v-if="batchReviewForm.timeOption === '指定'"
              label="选择时间"
              name="specificTime"
            >
              <a-date-picker
                v-model:value="batchReviewForm.specificTime"
                show-time
                placeholder="选择日期时间"
                style="width: 100%"
              />
            </a-form-item>
          </template>

          <a-form-item
            v-if="batchReviewForm.result === '3'"
            label="不通过原因"
            name="rejectReason"
          >
            <a-textarea
              v-model:value="batchReviewForm.rejectReason"
              :rows="4"
              placeholder="请输入不通过原因"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 批量成绩审核弹窗 -->
    <a-modal
      v-model:visible="batchConfirmModalVisible"
      title="批量成绩审核"
      @ok="submitBatchConfirm"
      @cancel="batchConfirmModalVisible = false"
      width="600px"
    >
      <div class="batch-confirm-modal-content">
        <div class="batch-info">
          <p><strong>选中记录数：</strong>{{ selectedRowKeys.length }} 条</p>
          <p><strong>操作说明：</strong>将对所有选中的记录执行相同的成绩审核操作</p>
        </div>

        <a-divider />

        <a-form
          :model="batchConfirmForm"
          :rules="batchConfirmFormRules"
          ref="batchConfirmFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="确认结果" name="result">
            <a-radio-group v-model:value="batchConfirmForm.result">
              <a-radio value="2">通过</a-radio>
              <a-radio value="3">不通过</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item
            v-if="batchConfirmForm.result === '3'"
            label="不通过原因"
            name="rejectReason"
          >
            <a-textarea
              v-model:value="batchConfirmForm.rejectReason"
              :rows="4"
              placeholder="请输入不通过原因"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CheckOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import {
  getApplicationList,
  reviewApplication, 
  deleteApplication,
  updateScoreConfirmStatus,
  getApplicationDetail,
  batchReviewApplication,
  batchUpdateScoreConfirmStatus,
  getExamSubjectOptions
} from '@/api/exam/exam-review';
import {
  getPositionNameOptions,
  getPositionTypeOptions,
  getLevelOptions
} from '@/api/organization/position';
import SearchFormCard from '@/components/SearchForm/index.vue';
import { useTablePagination ,getToolTip } from '@/utils/common';

// 导入权限工具函数
import { hasPermission } from '@/utils/permission';

// 数据加载状态
const loading = ref(false);

// 初始化关键数据结构，避免空引用错误
const positionNames = ref([]);
const levelOptions = ref([]);
const tableData = ref([]);
const currentRecord = ref(null);
// 添加考试科目选项
const examSubjectOptions = ref([]);

// 批量操作相关
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 批量操作弹窗
const batchReviewModalVisible = ref(false);
const batchConfirmModalVisible = ref(false);

// 批量操作表单
const batchReviewForm = reactive({
  result: '2',
  timeOption: '不限',
  specificTime: null,
  rejectReason: ''
});

const batchConfirmForm = reactive({
  result: '2',
  rejectReason: ''
});

// 表单引用
const batchReviewFormRef = ref(null);
const batchConfirmFormRef = ref(null);

const searchForm = reactive({
  applicantName: '',
  examTitle: '',
  positionName: '',
  reviewStatus: '',
  scoreConfirmStatus: ''
});

const formKey = ref(0);

// 表单引用
const reviewFormRef = ref(null);
const confirmFormRef = ref(null);

// 弹窗可见性
const reviewModalVisible = ref(false);
const confirmModalVisible = ref(false);

// 审核表单数据
const reviewForm = reactive({
  result: '2', // 默认通过，使用数字ID
  timeOption: '不限',
  specificTime: null,
  rejectReason: ''
});

// 确认表单数据
const confirmForm = reactive({
  result: '2', // 默认通过
  rejectReason: ''
});

// 表格行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
  },
  getCheckboxProps: (record) => ({
    disabled: false,
    name: record.id,
  }),
}));

// 判断是否有批量操作权限
const hasBatchPermission = computed(() => {
  return hasPermission('exam.review.qualification') || hasPermission('exam.review.score');
});

// 判断是否可以批量资格审核
const canBatchReview = computed(() => {
  if (selectedRows.value.length === 0) return false;
  // 检查权限和状态：需要有权限且所有选中记录状态为'1'(审核中)
  if (!hasPermission('exam.review.qualification')) return false;
  return selectedRows.value.every(row => row.status === '1');
});

// 判断是否可以批量成绩审核
const canBatchConfirm = computed(() => {
  if (selectedRows.value.length === 0) return false;
  // 检查权限和状态：需要有权限且所有选中记录状态为'2'(已通过)且成绩确认状态为'1'(待审核)
  if (!hasPermission('exam.review.score')) return false;
  return selectedRows.value.every(row => row.status === '2' && row.scoreConfirmStatus === '1');
});

// 加载数据列表
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      limit: pagination.pageSize,
      pageSize: pagination.pageSize,
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === null || params[key] === '') {
        delete params[key];
      }
    });

    const response = await getApplicationList(params);
    // 检查API返回的数据结构
    if (response && Array.isArray(response.list)) {
      tableData.value = response.list.map(item => item || {});
      // 更新分页信息
      updatePagination({
        total: response.total || 0,
        pageNum: response.pageNum,
        pageSize: response.pageSize
      });
    } else {
      console.error('API返回的数据结构不正确:', response);
      tableData.value = [];
      updatePagination({
        total: 0,
        pageNum: 1,
      });
    }
    
    // 清空选择
    clearSelection();
  } catch (error) {
    console.error('获取考试审核列表失败:', error);
    message.error('获取考试审核列表失败，请重试');
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: loadData,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm: searchForm
});


const resetSearch = () => {
  searchForm.applicantName = '';
  searchForm.examTitle = '';
  searchForm.positionName = '';
  searchForm.status = '';
  searchForm.scoreConfirmStatus = '';
  resetPagination();
  loadData();
};

// 表格列定义
const columns = [
  {
    title: '姓名',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 100,
    fixed: 'left'
  },
  {
    title: '岗位名称',
    dataIndex: 'positionName',
    key: 'positionName',
    width: 180,
    fixed: 'left'
  },
  {
    title: '考试科目',
    dataIndex: 'examTitle',
    key: 'examTitle',
    width: 180,
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150,
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '审核时间',
    dataIndex: ['reviewInfo','reviewedAt'],
    key: 'reviewTime',
    width: 150,
  },
   {
    title: '审核人',
    dataIndex: ['reviewInfo','reviewedByName'],
    key: 'reviewer',
    width: 100,
  },
   {
    title: '审核驳回原因',
    dataIndex: ['reviewInfo','reviewComments'],
    key: 'reviewComments',
    width: 120,
  },
  {
    title: '成绩确认状态',
    dataIndex: 'scoreConfirmStatus',
    key: 'scoreConfirmStatus',
    width: 120,
  },
 
  {
    title: '确认人',
    dataIndex: ['confirmInfo','confirmedByName'],
    key: 'confirmPerson',
    width: 100,
  },
  {
    title: '确认时间',
    dataIndex: ['confirmInfo','confirmedAt'],
    key: 'confirmTime',
    width: 150,
  },
   {
    title: '成绩确认驳回原因',
    dataIndex: ['confirmInfo','confirmComments'],
    key: 'confirmComments',
    width: 120,
  },
  {
    title: '考试时间',
    dataIndex: 'examDate',
    key: 'examDate',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200,
    align: 'center'
  }
];

// 考试记录表格列定义
const examRecordColumns = [
  {
    title: '考试科目',
    dataIndex: 'examSubject',
    key: 'examSubject',
    width: 150,
  },
  {
    title: '考试时间',
    dataIndex: 'examTime',
    key: 'examTime',
    width: 150,
    customRender: ({ text }) => formatDate(text),
  },
  {
    title: '餐考人',
    dataIndex: 'examinee',
    key: 'examinee',
    width: 100,
  },
  {
    title: '答题数量',
    key: 'answerCount',
    width: 100,
    align: 'center',
    customRender: ({ record }) => getAnswerCount(record),
  },
  {
    title: '正确题数',
    key: 'correctCount',
    width: 100,
    align: 'center',
    customRender: ({ record }) => getCorrectCount(record),
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    width: 80,
    align: 'center',
  },
  {
    title: '考试用时(分钟)',
    dataIndex: 'usedDuration',
    key: 'usedDuration',
    width: 120,
    align: 'center',
  }
];

// 搜索表单
const formItems = computed(() => [
  {
    label: '姓名',
    field: 'applicantName',
    type: 'input',
    placeholder: '请输入姓名'
  },
  {
    label: '考试科目',
    field: 'examTitle',
    type: 'select', // 从 input 改为 select
    placeholder: '请选择考试科目',
    options: examSubjectOptions.value, // 使用考试科目选项
    showSearch: true, // 启用搜索功能
    optionFilterProp: 'children', // 按选项文本过滤
    allowClear: true, // 允许清空
    width: '300px', // 尝试使用width属性替代style
    style: { width: '300px' }, // 同时保留style方式，增大宽度
    fieldStyle: 'width: 300px', // 尝试fieldStyle属性
    selectProps: { style: { width: '300px' } } // 尝试通过selectProps传递样式
  },
  {
    label: '岗位名称',
    field: 'positionName',
    type: 'select',
    placeholder: '请选择岗位名称',
    // options: [],  // 初始为空数组
    options: positionNames.value,
    // 根据组件实际要求设置label和value字段
    selectLabel: 'name', // 可能需要修改为实际的label字段
    selectValue: 'id'  // 可能需要修改为实际的value字段
  },
  {
    label: '审核状态',
    field: 'status',
    type: 'select',
    placeholder: '请选择审核状态',
    options: [
      { label: '审核中', value: '1' },
      { label: '已通过', value: '2' },
      { label: '已驳回', value: '3' },
      { label: '已完成', value: '4' }
    ]
  },
  {
    label: '成绩确认状态',
    field: 'scoreConfirmStatus',
    type: 'select',
    placeholder: '请选择成绩确认状态',
    options: [
      { label: '待审核', value: '1' },
      { label: '已通过', value: '2' },
      { label: '已驳回', value: '3' }
    ]
  }
]);

// 状态映射
const reviewStatusMap = {
  '1': '审核中',
  '2': '已通过',
  '3': '已驳回',
  '4': '已完成'
};

const getPositionNameById = (id) => {
  if (!id || !positionNames.value || !positionNames.value.length) return null;

  // 确保id是字符串，以便比较
  const idStr = String(id);

  // 使用id字段进行匹配
  const found = positionNames.value.find(pos =>
    pos && (String(pos.id || '') === idStr)
  );

  // 返回找到的名称，优先使用positionName字段
  return found ? (found.positionName || found.name || found.dictLabel || '') : null;
};

// 辅助函数，根据id查找岗位等级
const getLevelNameById = (id) => {
  if (!id || !levelOptions.value || !levelOptions.value.length) return null;

  // 确保id是字符串，以便比较
  const idStr = String(id);

  const found = levelOptions.value.find(level =>
    (level && String(level.id || '') === idStr) || (level && String(level.dictValue || '') === idStr)
  );
  // 返回找到的等级名称
  return found ? (found.name || found.dictLabel || '') : null;
};

// 筛选后的数据
// const filteredData = computed(() => {
//   let result = tableData.value;

//   // 根据搜索条件筛选
//   if (searchForm.applicant) {
//     result = result.filter(item =>
//       item.applicant.includes(searchForm.applicant)
//     );
//   }

//   if (searchForm.certificateName) {
//     result = result.filter(item =>
//       item.certificateName.includes(searchForm.certificateName)
//     );
//   }

//   if (searchForm.positionName) {
//     const selectedPosition = positionNames.value.find(pos =>
//       (pos.id && pos.id.toString() === searchForm.positionName.toString()) ||
//       (pos.dictValue && pos.dictValue.toString() === searchForm.positionName.toString())
//     );

//     if (selectedPosition) {
//       result = result.filter(item =>
//         item.positionName && item.positionName.includes(selectedPosition.dictLabel)
//       );
//     }
//   }

//   if (searchForm.reviewStatus) {
//     result = result.filter(item =>
//       item.reviewStatus === searchForm.reviewStatus
//     );
//   }

//   if (searchForm.scoreConfirmStatus) {
//     result = result.filter(item =>
//       item.scoreConfirmStatus === searchForm.scoreConfirmStatus
//     );
//   }

//   return result;
// });

// 辅助函数，根据状态名称获取id
const getReviewStatusId = (statusName) => {
  for (const [id, name] of Object.entries(reviewStatusMap)) {
    if (name === statusName) return id;
  }
  return null;
};

const getScoreConfirmStatusId = (statusName) => {
  const option = scoreConfirmStatusOptions.find(option => option.label === statusName);
  return option ? option.value : null;
};

// 获取成绩确认状态颜色
const getScoreStatusColor = (status) => {
  if (status === null || status === undefined) return '#d9d9d9'; // 不可审批状态使用灰色

  const colorMap = {
    '1': '#ffa940', // 待审核-橙色
    '2': '#52c41a', // 已通过-绿色
    '3': '#ff4d4f'  // 已驳回-红色
  };
  return colorMap[status] || '#d9d9d9';
};

// 获取成绩确认状态文本
const getScoreStatusText = (status) => {
  if (status === null || status === undefined) return '不可审批';

  const textMap = {
    '1': '待审核',
    '2': '已通过',
    '3': '已驳回'
  };
  return textMap[status] || '未知';
};

// 批量操作表单校验规则
const batchReviewFormRules = {
  result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  rejectReason: [{
    required: true,
    message: '请输入不通过原因',
    trigger: 'blur',
    validator: (rule, value) => {
      if (batchReviewForm.result === '3' && !value) {
        return Promise.reject('请输入不通过原因');
      }
      return Promise.resolve();
    }
  }],
  specificTime: [{
    required: true,
    message: '请选择考试时间',
    trigger: 'change',
    validator: (rule, value) => {
      if (batchReviewForm.result === '2' && batchReviewForm.timeOption === '指定' && !value) {
        return Promise.reject('请选择考试时间');
      }
      return Promise.resolve();
    }
  }]
};

const batchConfirmFormRules = {
  result: [{ required: true, message: '请选择确认结果', trigger: 'change' }],
  rejectReason: [{
    required: true,
    message: '请输入不通过原因',
    trigger: 'blur',
    validator: (rule, value) => {
      if (batchConfirmForm.result === '3' && !value) {
        return Promise.reject('请输入不通过原因');
      }
      return Promise.resolve();
    }
  }]
};

// 表单校验规则
const reviewFormRules = {
  result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  rejectReason: [{
    required: true,
    message: '请输入不通过原因',
    trigger: 'blur',
    validator: (rule, value) => {
      if (reviewForm.result === '3' && !value) {
        return Promise.reject('请输入不通过原因');
      }
      return Promise.resolve();
    }
  }],
  specificTime: [{
    required: true,
    message: '请选择考试时间',
    trigger: 'change',
    validator: (rule, value) => {
      if (reviewForm.result === '2' && reviewForm.timeOption === '指定' && !value) {
        return Promise.reject('请选择考试时间');
      }
      return Promise.resolve();
    }
  }]
};

// 确认表单校验规则
const confirmFormRules = {
  result: [{ required: true, message: '请选择确认结果', trigger: 'change' }],
  rejectReason: [{
    required: true,
    message: '请输入不通过原因',
    trigger: 'blur',
    validator: (rule, value) => {
      if (confirmForm.result === '3' && !value) {
        return Promise.reject('请输入不通过原因');
      }
      return Promise.resolve();
    }
  }]
};

// 计算关联考试记录的平均分
const averageScore = computed(() => {
  if (!currentRecord.value || !currentRecord.value.examRecords || currentRecord.value.examRecords.length === 0) {
    return 0;
  }

  const total = currentRecord.value.examRecords.reduce((sum, record) => {
    return sum + Number(record.score || 0);
  }, 0);

  return (total / currentRecord.value.examRecords.length).toFixed(0);
});

// 计算关联考试记录的最高分
const maxScore = computed(() => {
  if (!currentRecord.value || !currentRecord.value.examRecords || currentRecord.value.examRecords.length === 0) {
    return 0;
  }

  const scores = currentRecord.value.examRecords.map(record => Number(record.score || 0));
  return Math.max(...scores);
});

// 计算关联考试记录的最低分
const minScore = computed(() => {
  if (!currentRecord.value || !currentRecord.value.examRecords || currentRecord.value.examRecords.length === 0) {
    return 0;
  }

  const scores = currentRecord.value.examRecords.map(record => Number(record.score || 0));
  return Math.min(...scores);
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '未设置';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '无效日期';

  // 使用特定格式而不是toLocaleString
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取分数对应的样式类
const getScoreClass = (score) => {
  if (!score) return 'fail';
  score = Number(score);
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 60) return 'pass';
  return 'fail';
};

// 处理搜索
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }

  resetPagination();
  loadData();
};

// 删除申请
const handleDelete = async (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条申请记录吗？删除后无法恢复。',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await deleteApplication(record.id);
        message.success('删除成功');
        loadData();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败，请重试');
      }
    }
  });
};

// 提交审核
const submitReview = async () => {
  try {
    await reviewFormRef.value.validate();

    // 对于指定时间，格式化为yyyy-MM-dd HH:mm:ss格式
    let formattedExamTime = null;
    if (reviewForm.result === '2' && reviewForm.timeOption === '指定' && reviewForm.specificTime) {
      // 检查日期对象类型并相应处理
      const date = reviewForm.specificTime;
      // 处理不同类型的日期对象
      if (date instanceof Date) {
        // 原生Date对象
        formattedExamTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
      } else if (date && typeof date === 'object' && date.format) {
        // Moment 或 Dayjs 对象 (有format方法)
        formattedExamTime = date.format('YYYY-MM-DD HH:mm:ss');
      } else if (date && typeof date === 'object' && date.$d) {
        // Vue日期对象 (可能有$d属性)
        const dateObj = date.$d;
        formattedExamTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}:${String(dateObj.getSeconds()).padStart(2, '0')}`;
      } else if (date && typeof date.toISOString === 'function') {
        // 有toISOString方法的对象
        const isoString = date.toISOString();
        const dateObj = new Date(isoString);
        formattedExamTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}:${String(dateObj.getSeconds()).padStart(2, '0')}`;
      } else if (date && typeof date === 'string') {
        // 已经是字符串
        formattedExamTime = date;
      }
    }

    const params = {
      id: currentRecord.value.id,
      status: reviewForm.result, // 直接使用form中的状态值
      reviewInfo: {
        reviewedBy: '当前用户', // 暂时使用固定值，实际应该使用当前登录用户ID
        reviewedAt: new Date().toISOString(),
        reviewComments: reviewForm.result === '3' ? reviewForm.rejectReason : ''
      },
      examTime: formattedExamTime
    };
    
    await reviewApplication(currentRecord.value.id, params);
    message.success('审核操作成功');
    reviewModalVisible.value = false;

    // 刷新数据
    loadData();
  } catch (error) {
    console.error('审核操作失败:', error);
    message.error('审核操作失败，请重试');
  }
};

// 生命周期钩子
onMounted(async () => {
  try {
    // 先加载选项数据，再加载表格数据
    await fetchPositionOptions();
    await fetchExamSubjectOptions(); // 添加考试科目选项加载
    formKey.value += 1;
    await loadData();
  } catch (error) {
    console.error('组件初始化失败:', error);
    message.error('页面初始化失败，请刷新重试');
  }
});

// 成绩确认状态选项数组
const scoreConfirmStatusOptions = [
  { label: '待审核', value: '1' },
  { label: '已通过', value: '2' },
  { label: '已驳回', value: '3' }
];

// 获取岗位相关选项数据
const fetchPositionOptions = async () => {
  try {
    // 获取岗位名称选项
    const nameRes = await getPositionNameOptions();
    // 从返回的rows属性获取岗位数据
    positionNames.value = nameRes.rows || [];

    // 获取岗位等级选项
    const levelRes = await getLevelOptions();
    levelOptions.value = Array.isArray(levelRes) ? levelRes : [];

    // 更新下拉选项
    // updateFormOptions();

 

    return Promise.resolve();
  } catch (error) {
    console.error('获取岗位选项数据失败', error);
    positionNames.value = [];
    levelOptions.value = [];
    return Promise.resolve();
  }
};

// 添加获取考试科目选项的函数
const fetchExamSubjectOptions = async () => {
  try {
    // 调用获取考试科目列表的API
    const res = await getExamSubjectOptions();
    
    // 处理API响应数据
    if (res && res.code === 200 && res.data) {
      // 如果后端返回的数据格式是 { code: 200, data: [...] }
      const subjects = Array.isArray(res.data) ? res.data : [];
      examSubjectOptions.value = subjects.map(item => ({
        label: item.name || item.title || item.label,
        value: item.name || item.title || item.label // 改为使用name作为value
      }));
    } else if (Array.isArray(res)) {
      // 如果后端直接返回数组
      examSubjectOptions.value = res.map(item => ({
        label: item.name || item.title || item.label,
        value: item.name || item.title || item.label // 改为使用name作为value
      }));
    } else {
      // 如果没有数据，使用空数组
      examSubjectOptions.value = [];
    }
    
    console.log('已加载考试科目选项:', examSubjectOptions.value);
    return Promise.resolve();
  } catch (error) {
    console.error('获取考试科目选项失败:', error);
    // 如果API调用失败，使用模拟数据作为备用
    const mockData = [
      { label: '安全生产基础知识', value: '安全生产基础知识' },
      { label: '消防安全管理', value: '消防安全管理' },
      { label: '职业健康安全', value: '职业健康安全' },
      { label: '环境保护法规', value: '环境保护法规' },
      { label: '特种设备操作', value: '特种设备操作' }
    ];
    examSubjectOptions.value = mockData;
    console.warn('使用模拟数据作为考试科目选项');
    return Promise.resolve();
  }
};

// 更新表单选项
// const updateFormOptions = () => {
//   // 确保positionNames已初始化
//   if (!positionNames.value) {
//     console.warn('岗位名称数据未初始化');
//     return;
//   }
  
//   // 更新岗位名称选项
//   const positionItem = formItems.value.find(item => item && item.field === 'positionName');
//   if (positionItem && positionNames.value && positionNames.value.length > 0) {
//     // 转换为组件需要的格式
//     positionItem.options = positionNames.value.map(item => {
//       if (!item) return { label: '-', value: '' };
      
//       // 使用positionName作为显示标签，id作为值
//       const label = item.positionName || item.name || String(item.id || '');
//       const value = item.id || '';
//       return { label, value };
//     }).filter(option => option && option.label && option.value); // 过滤掉无效选项

//   } else {
//     console.warn('未找到岗位名称下拉框或数据为空');
//   }
// };

// 过滤选项
const filterOption = (input, option) => {
  if (!input || input.trim() === '') {
    return true;
  }

  if (!option || !option.label) {
    return false;
  }

  // 尝试将选项标签转换为小写字符串
  const optionLabel = typeof option.label === 'string' ? option.label.toLowerCase() : '';
  // 将输入转换为小写
  const inputValue = input.toLowerCase();

  return optionLabel.indexOf(inputValue) >= 0;
};

// 获取状态对应的颜色
const getStatusColor = (status) => {
  const colorMap = {
    '1': '#faad14', // 审核中-橙色
    '2': '#52c41a', // 已通过-绿色
    '3': '#f5222d', // 已驳回-红色
    '4': '#1890ff'  // 已完成-蓝色
  };

  return colorMap[status] || '#d9d9d9';
};

// 处理审核申请
const handleReviewApplication = (record) => {
  currentRecord.value = record;
  // 重置表单
  reviewForm.result = '2';
  reviewForm.timeOption = '不限';
  reviewForm.specificTime = null;
  reviewForm.rejectReason = '';

  reviewModalVisible.value = true;
};

// 处理成绩确认
const handleConfirmScore = async (record) => {
  if (!record || !record.id) {
    message.error('无效的记录');
    return;
  }
  
  try {
    loading.value = true;

    // 获取审核申请的详细信息，包括关联的考试记录
    const detailResult = await getApplicationDetail(record.id);

    // 检查详情数据
    if (!detailResult) {
      message.error('获取申请详情失败，返回数据为空');
      return;
    }

    // 设置当前记录
    currentRecord.value = {
      ...record,
      ...detailResult,
      examRecords: Array.isArray(detailResult.examRecords) ? detailResult.examRecords : [],
      _originalData: detailResult // 保存原始数据
    };

    // 重置确认表单
    confirmForm.result = '2';
    confirmForm.rejectReason = '';

    // 打开确认弹窗
    confirmModalVisible.value = true;
  } catch (error) {
    console.error('获取审核申请详情失败', error);
    message.error('获取审核申请详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理审核弹窗确认
const handleReviewModalOk = () => {
  reviewFormRef.value.validate().then(() => {
    if (!currentRecord.value) return;

    const index = tableData.value.findIndex(item => item.id === currentRecord.value.id);
    if (index === -1) return;

    // 更新记录
    const updatedRecord = { ...tableData.value[index] };

    if (reviewForm.result === '2') {
      updatedRecord.reviewStatus = '已通过'; // 对应id为2
      updatedRecord.reviewer = '当前用户';
      updatedRecord.reviewTime = new Date().toLocaleString();

      if (reviewForm.timeOption === '不限') {
        updatedRecord.examTime = '不限';
      } else if (reviewForm.timeOption === '指定' && reviewForm.specificTime) {
        // 格式化日期时间
        const date = reviewForm.specificTime;
        updatedRecord.examTime = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`;
      }
    } else {
      updatedRecord.reviewStatus = '已驳回'; // 对应id为3
      updatedRecord.reviewer = '当前用户';
      updatedRecord.reviewTime = new Date().toLocaleString();
      // 可以在这里存储驳回原因
    }

    tableData.value[index] = updatedRecord;
    message.success('审核操作已完成');
    reviewModalVisible.value = false;
  }).catch(error => {
    console.log('表单验证失败', error);
  });
};

// 处理审核弹窗取消
const handleReviewModalCancel = () => {
  reviewModalVisible.value = false;
};

// 处理确认弹窗确认
const handleConfirmModalOk = async () => {
  try {
    await confirmFormRef.value.validate();

    if (!currentRecord.value) return;

    // 准备API调用参数
    const params = {
      id: currentRecord.value.id,
      scoreConfirmStatus: confirmForm.result, // 直接使用表单中的状态值 '2'-已通过 '3'-已驳回
      reviewInfo: {
        // 保留原有审核信息
        ...currentRecord.value._originalData.reviewInfo,
        // 添加确认信息
        confirmInfo: {
          confirmedBy: '当前用户', // 暂时使用固定值，实际应该使用当前登录用户ID
          confirmedAt: new Date().toISOString(),
          confirmComments: confirmForm.result === '3' ? confirmForm.rejectReason : ''
        }
      }
    };

    // 调用API更新成绩确认状态
    await updateScoreConfirmStatus(params);

    // API调用成功后更新表格数据
    const index = tableData.value.findIndex(item => item.id === currentRecord.value.id);
    if (index > -1) {
      // 更新记录
      const updatedRecord = { ...tableData.value[index] };

      // 直接使用数字状态码
      updatedRecord.scoreConfirmStatus = confirmForm.result; // '2'-已通过 '3'-已驳回
        updatedRecord.confirmPerson = params.reviewInfo.confirmInfo.confirmedBy;
        updatedRecord.confirmTime = new Date().toLocaleString();

        // 如果审核和成绩确认都通过，则设置为已完成状态
      if (updatedRecord.reviewStatus === '已通过' && confirmForm.result === '2') {
          updatedRecord.reviewStatus = '已完成'; // 对应id为4
      }

      tableData.value[index] = updatedRecord;
    }

    message.success('成绩确认操作已完成');
    confirmModalVisible.value = false;

    // 刷新数据列表
    loadData();
  } catch (error) {
    console.error('成绩确认操作失败', error);
    message.error('考试未及格，无法通过');
  }
};

// 处理确认弹窗取消
const handleConfirmModalCancel = () => {
  confirmModalVisible.value = false;
};

// 获取审核人信息
const getReviewerInfo = (reviewInfo) => {
  if (!reviewInfo) return '-';
  try {
    const info = typeof reviewInfo === 'string' && reviewInfo ? JSON.parse(reviewInfo) : reviewInfo;
    return info?.reviewedBy || '-';
  } catch (error) {
    console.error('解析审核人信息失败', error);
    return '-';
  }
};

// 获取审核时间信息
const getReviewTimeInfo = (reviewInfo) => {
  if (!reviewInfo) return '-';
  try {
    const info = typeof reviewInfo === 'string' && reviewInfo ? JSON.parse(reviewInfo) : reviewInfo;
    return info?.reviewedAt || '-';
  } catch (error) {
    console.error('解析审核时间信息失败', error);
    return '-';
  }
};

// 获取确认人信息
const getConfirmPersonInfo = (reviewInfo) => {
  if (!reviewInfo) return '-';
  try {
    const info = typeof reviewInfo === 'string' && reviewInfo ? JSON.parse(reviewInfo) : reviewInfo;
    return info?.confirmInfo?.confirmedBy || '-';
  } catch (error) {
    console.error('解析确认人信息失败', error);
    return '-';
  }
};

// 获取确认时间信息
const getConfirmTimeInfo = (reviewInfo) => {
  if (!reviewInfo) return '-';
  try {
    const info = typeof reviewInfo === 'string' && reviewInfo ? JSON.parse(reviewInfo) : reviewInfo;
    return info?.confirmInfo?.confirmedAt || '-';
  } catch (error) {
    console.error('解析确认时间信息失败', error);
    return '-';
  }
};

// 获取审核状态文本
const getReviewStatusText = (status) => {
  const textMap = {
    '1': '审核中',
    '2': '已通过',
    '3': '已驳回',
    '4': '已完成'
  };
  return textMap[status] || '未知';
};

// 获取答题数量
const getAnswerCount = (record) => {
  if (!record || !record.examContent || !Array.isArray(record.examContent)) {
    return 0;
  }
  // 只计算有用户回答的题目
  return record.examContent.filter(item => item.userAnswer !== undefined && item.userAnswer !== null).length;
};

// 获取正确题数
const getCorrectCount = (record) => {
  if (!record || !record.examContent || !Array.isArray(record.examContent)) {
    return 0;
  }
  // 计算result为true的题目数量
  return record.examContent.filter(item => item.result === true).length;
};

// 清空选择
const clearSelection = () => {
  selectedRowKeys.value = [];
  selectedRows.value = [];
};

// 处理批量资格审核
const handleBatchReview = () => {
  if (!canBatchReview.value) {
    message.warning('选中的记录中包含无法进行资格审核的数据');
    return;
  }
  
  // 重置批量审核表单
  batchReviewForm.result = '2';
  batchReviewForm.timeOption = '不限';
  batchReviewForm.specificTime = null;
  batchReviewForm.rejectReason = '';
  
  batchReviewModalVisible.value = true;
};

// 处理批量成绩审核
const handleBatchConfirm = () => {
  if (!canBatchConfirm.value) {
    message.warning('选中的记录中包含无法进行成绩审核的数据');
    return;
  }
  
  // 重置批量确认表单
  batchConfirmForm.result = '2';
  batchConfirmForm.rejectReason = '';
  
  batchConfirmModalVisible.value = true;
};

// 提交批量资格审核
const submitBatchReview = async () => {
  try {
    await batchReviewFormRef.value.validate();

    // 格式化考试时间
    let formattedExamTime = null;
    if (batchReviewForm.result === '2' && batchReviewForm.timeOption === '指定' && batchReviewForm.specificTime) {
      const date = batchReviewForm.specificTime;
      if (date instanceof Date) {
        formattedExamTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
      } else if (date && typeof date === 'object' && date.format) {
        formattedExamTime = date.format('YYYY-MM-DD HH:mm:ss');
      } else if (date && typeof date === 'object' && date.$d) {
        const dateObj = date.$d;
        formattedExamTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}:${String(dateObj.getSeconds()).padStart(2, '0')}`;
      }
    }

    const params = {
      ids: selectedRowKeys.value,
      status: batchReviewForm.result,
      reviewInfo: {
        reviewedBy: '当前用户',
        reviewedAt: new Date().toISOString(),
        reviewComments: batchReviewForm.result === '3' ? batchReviewForm.rejectReason : ''
      },
      examTime: formattedExamTime
    };
    
    await batchReviewApplication(params);
    message.success(`批量资格审核操作成功，共处理 ${selectedRowKeys.value.length} 条记录`);
    batchReviewModalVisible.value = false;
    clearSelection();
    loadData();
  } catch (error) {
    console.error('批量资格审核操作失败:', error);
    message.error('批量资格审核操作失败，请重试');
  }
};

// 提交批量成绩审核
const submitBatchConfirm = async () => {
  try {
    await batchConfirmFormRef.value.validate();

    const params = {
      ids: selectedRowKeys.value,
      scoreConfirmStatus: batchConfirmForm.result,
      reviewInfo: {
        confirmInfo: {
          confirmedBy: '当前用户',
          confirmedAt: new Date().toISOString(),
          confirmComments: batchConfirmForm.result === '3' ? batchConfirmForm.rejectReason : ''
        }
      }
    };
    
    console.log('批量成绩审核参数:', params); // 添加调试日志
    
    await batchUpdateScoreConfirmStatus(params);
    message.success(`批量成绩审核操作成功，共处理 ${selectedRowKeys.value.length} 条记录`);
    batchConfirmModalVisible.value = false;
    clearSelection();
    loadData();
  } catch (error) {
    console.error('批量成绩审核操作失败:', error);
    message.error('批量成绩审核操作失败，请重试');
  }
};
</script>

<style scoped>
/* 移除重复的.action-buttons样式定义，使用全局样式 */

/* 页面容器样式 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 120px);
  padding: 16px;
}

/* 批量操作按钮样式 */
.batch-actions {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f0f2f5;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.batch-button {
  margin-right: 8px;
}

.batch-button:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

.clear-button {
  margin-left: 8px;
}

.clear-button:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

.batch-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.batch-info p {
  margin-bottom: 8px;
}

/* 表格固定列样式修复 */
.exam-review-table {
  width: 100%;
  /* 确保表格容器不会超出父容器 */
  max-width: 100%;
  overflow: hidden;
}

/* 表格容器样式调整 */
:deep(.ant-table-wrapper) {
  overflow: hidden;
}

:deep(.ant-table-container) {
  overflow-x: auto;
  /* 确保分页组件有足够的显示空间 */
  margin-bottom: 16px;
}

:deep(.ant-table-scroll) {
  overflow-x: auto;
  overflow-y: auto;
  max-height: calc(100vh - 450px);
}

/* 分页组件样式调整 */
:deep(.ant-pagination) {
  margin-top: 16px !important;
  margin-bottom: 0 !important;
  text-align: right;
  /* 确保分页组件始终可见 */
  position: relative;
  z-index: 1;
  background-color: #fff;
  padding: 8px 0;
  /* 确保分页组件不会被表格遮挡 */
  clear: both;
  display: block !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.ant-pagination) {
    text-align: center;
    padding: 12px 0;
  }
  
  .exam-review-table {
    font-size: 12px;
  }
  
  :deep(.ant-table-cell) {
    padding: 8px 4px !important;
  }
}

:deep(.ant-table-fixed-left),
:deep(.ant-table-fixed-right) {
  z-index: 2;
  background-color: #fff;
}

:deep(.ant-table-fixed-right) {
  box-shadow: -6px 0 6px -4px rgba(0,0,0,.15);
}

:deep(.ant-table-fixed-left) {
  box-shadow: 6px 0 6px -4px rgba(0,0,0,.15);
}

:deep(.ant-table-body) {
  overflow-x: auto !important;
  overflow-y: auto !important;
  max-height: calc(100vh - 500px) !important;
}

/* 避免固定列与内容宽度不一致导致的错位 */
:deep(.ant-table-fixed-right .ant-table-cell) {
  text-align: center;
}

/* 避免与原有样式冲突 */
:deep(.ant-table-fixed-right) {
  background-color: #fff;
  right: 0;
}

/* 其他样式保留... */

.search-form {
  padding: 0;
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  margin-bottom: 16px;
}

.form-item {
  flex: 1;
  padding: 0 8px;
}

.form-item-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.input-control {
  width: 100%;
}


/* 成绩确认状态标签样式 */
.score-status-tag {
  border: none;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.score-status-tag.pending {
  background-color: #e6a23c;
  color: #ffffff;
}

.score-status-tag.confirmed {
  background-color: #67c23a;
  color: #ffffff;
}

/* 修复Ant Design表单控件的样式 */
:deep(.ant-select-selection-placeholder),
:deep(.ant-input::placeholder) {
  color: #bfbfbf;
  opacity: 1;
}

:deep(.ant-select-selector) {
  display: flex;
  align-items: center;
}

/* 确保Ant Design Select的占位提示显示 */
:deep(.ant-select-selection-placeholder) {
  position: absolute;
  top: 50%;
  right: 11px;
  left: 11px;
  transform: translateY(-50%);
  transition: all 0.3s;
  flex: 1;
  overflow: hidden;
  color: #bfbfbf;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: none;
}

.review-info,
.confirm-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.review-info p,
.confirm-info p {
  margin-bottom: 8px;
}

.exam-result {
  text-align: center;
  padding: 16px;
}

.exam-result h3 {
  font-size: 18px;
  color: #333333;
  margin-bottom: 16px;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.score-display {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  box-shadow: 0 4px 10px rgba(161, 140, 209, 0.3);
}

.score-value {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
}

.score-unit {
  font-size: 16px;
  margin-top: 4px;
}

.score-status {
  font-size: 16px;
  color: #52c41a;
  font-weight: 500;
}

/* 成绩样式 */
.excellent {
  color: #52c41a;
  font-weight: bold;
}

.good {
  color: #1890ff;
  font-weight: bold;
}

.pass {
  color: #faad14;
  font-weight: bold;
}

.fail {
  color: #f5222d;
  font-weight: bold;
}

.exam-records {
  margin-bottom: 16px;
}

.exam-records h3 {
  font-size: 18px;
  color: #333333;
  margin-bottom: 16px;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.score-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

/* 操作列样式调整 */
.action-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.custom-button {
  margin: 0 4px;
  padding: 0 8px;
  white-space: nowrap;
}
</style>
