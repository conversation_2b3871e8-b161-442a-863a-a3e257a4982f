import request from '@/utils/request';

const api = {
  SystemSetting: '/system/setting',
  SystemSettingByCode: '/system/setting/code'
};

/**
 * 获取系统设置列表
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 关键词
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @returns {Promise} - 返回Promise对象
 */
export function getSystemSettings(params) {
  return request({
    url: api.SystemSetting,
    method: 'get',
    params
  });
}

/**
 * 获取系统设置详情
 * @param {number} id - 系统设置ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSystemSettingById(id) {
  return request({
    url: `${api.SystemSetting}/${id}`,
    method: 'get'
  });
}

/**
 * 通过code获取系统设置
 * @param {string} code - 系统设置代码
 * @returns {Promise} - 返回Promise对象
 */
export function getSystemSettingByCode(code) {
  return request({
    url: `${api.SystemSettingByCode}/${code}`,
    method: 'get'
  });
}

/**
 * 创建系统设置
 * @param {Object} data - 系统设置数据
 * @param {string} data.code - 系统设置代码
 * @param {string} data.name - 系统设置名称
 * @param {string} data.value - 系统设置值
 * @param {string} data.description - 系统设置描述
 * @returns {Promise} - 返回Promise对象
 */
export function createSystemSetting(data) {
  return request({
    url: api.SystemSetting,
    method: 'post',
    data
  });
}

/**
 * 更新系统设置
 * @param {number} id - 系统设置ID
 * @param {Object} data - 系统设置数据
 * @param {string} data.name - 系统设置名称
 * @param {string} data.value - 系统设置值
 * @param {string} data.description - 系统设置描述
 * @returns {Promise} - 返回Promise对象
 */
export function updateSystemSetting(id, data) {
  return request({
    url: `${api.SystemSetting}/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除系统设置
 * @param {number} id - 系统设置ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteSystemSetting(id) {
  return request({
    url: `${api.SystemSetting}/${id}`,
    method: 'delete'
  });
} 