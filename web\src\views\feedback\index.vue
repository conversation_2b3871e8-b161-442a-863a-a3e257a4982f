<template>
    <div class="page-container">
        <page-header
        >
            <template #search>
                <!-- 搜索表单 -->
                <search-form-card
                        :model-value="searchForm"
                        :items="searchItems"
                        @search="handleSearch"
                        @reset="resetSearch"
                />
            </template>
        </page-header>


        <!-- 数据表格 -->
        <base-table
                :columns="columns"
                :data-source="tableData"
                rowKey="id"
                :loading="loading"
                @change="handleTableChange"
                @detail="viewDetail"
                :action-config="actionConfig"
                :pagination="pagination"
                :delete-title="deleteTitle"
        >
            <template #bodyCell="{ column, record }">
                <!-- 图片列 -->
                <template v-if="column.key === 'images'">
                    <div class="image-preview" v-if="record.images && record.images.length">
                        <a-image-preview-group>
                            <a-image
                                    v-for="(image, index) in record.images.slice(0, 3)"
                                    :key="index"
                                    :width="40"
                                    :src="image"
                                    :preview="{ src: image }"
                            />
                        </a-image-preview-group>
                        <span v-if="record.images.length > 3" class="more-images">
                +{{ record.images.length - 3 }}
              </span>
                    </div>
                    <span v-else>无图片</span>
                </template>

                <!-- 回复状态列 -->
                <template v-if="column.key === 'replyStatus'">
                    <a-tag :color="record.replyContent ? 'green' : 'orange'">
                        {{ record.replyContent ? '已回复' : '未回复' }}
                    </a-tag>
                </template>

                <!-- 操作列 -->
                <template v-if="column.key === 'action'">
                    <a-button type="link" @click="viewDetail(record)">
                        {{ record.replyContent ? '详情' : '回复' }}
                    </a-button>
                </template>
            </template>
        </base-table>

        <!-- 详情对话框 -->
        <a-modal
                v-model:visible="detailVisible"
                :title="currentRecord && currentRecord.replyContent ? '反馈详情' : '回复反馈'"
                width="700px"
                :footer="null"
        >
            <a-descriptions bordered :column="1" size="middle" v-if="currentRecord">
                <a-descriptions-item label="联系人">{{ currentRecord.contactPerson }}</a-descriptions-item>
                <a-descriptions-item label="联系方式">{{ currentRecord.contactInfo }}</a-descriptions-item>
                <a-descriptions-item label="反馈时间">{{ currentRecord.feedbackTime }}</a-descriptions-item>
                <a-descriptions-item label="反馈内容">
                    <div class="feedback-content">{{ currentRecord.feedbackContent }}</div>
                </a-descriptions-item>
                <a-descriptions-item label="图片" v-if="currentRecord.images && currentRecord.images.length">
                    <div class="feedback-images">
                        <a-image-preview-group>
                            <a-image
                                    v-for="(image, index) in currentRecord.images"
                                    :key="index"
                                    :width="100"
                                    :src="image"
                                    :preview="{ src: image }"
                            />
                        </a-image-preview-group>
                    </div>
                </a-descriptions-item>
                <a-descriptions-item label="回复状态">
                    <a-tag :color="currentRecord.replyContent ? 'green' : 'orange'">
                        {{ currentRecord.replyContent ? '已回复' : '未回复' }}
                    </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="回复内容" v-if="currentRecord.replyContent">
                    <div class="reply-content">{{ currentRecord.replyContent }}</div>
                </a-descriptions-item>
                <a-descriptions-item label="回复时间" v-if="currentRecord.replyTime">
                    {{ currentRecord.replyTime }}
                </a-descriptions-item>
            </a-descriptions>

            <!-- 回复表单 -->
            <div class="reply-form" v-if="currentRecord && !currentRecord.replyContent">
                <a-divider>回复反馈</a-divider>
                <a-form :model="replyForm" layout="vertical">
                    <a-form-item label="回复内容" name="replyContent" :rules="[{ required: true, message: '请输入回复内容' }]">
                        <a-textarea
                            v-model:value="replyForm.replyContent"
                            :rows="4"
                            placeholder="请输入回复内容"
                        />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="submitReply" :loading="submitting">提交回复</a-button>
                    </a-form-item>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script>
import {defineComponent, ref, reactive, onMounted} from 'vue';
import {SearchOutlined, ReloadOutlined, EyeOutlined} from '@ant-design/icons-vue';
import {SearchFormCard} from '@/components/SearchForm';
import {useTablePagination} from '@/utils/common';
import {getFeedbackList, replyFeedback} from '@/api/feedback';
import { message } from 'ant-design-vue';
const VITE_APP_API_BASE_IMG_URL = import.meta.env.VITE_APP_API_BASE_IMG_URL || '';


export default defineComponent({
    name: 'Feedback',
    components: {
        SearchOutlined,
        ReloadOutlined,
        EyeOutlined,
        SearchFormCard,
    },
    setup() {
        // 搜索表单
        const searchForm = reactive({
            feedbackTimeRange: []
        });

        // 表格配置
        const actionConfig = ref({
            detail: false
        });
        
        // 删除确认提示文字
        const deleteTitle = '确定删除该反馈信息吗？';

        // 搜索表单项配置
        const searchItems = ref([
            {
                label: '反馈时间',
                field: 'feedbackTimeRange',
                type: 'dateRange',
                placeholder: ['开始日期', '结束日期']
            }
        ]);

        // 表格配置
        const columns = [
            {title: '联系人', dataIndex: 'contactPerson', key: 'contactPerson', width: 100},
            {title: '联系方式', dataIndex: 'contactInfo', key: 'contactInfo', width: 150},
            {title: '反馈内容', dataIndex: 'feedbackContent', key: 'feedbackContent', width: 250, ellipsis: true},
            {title: '图片', dataIndex: 'images', key: 'images', width: 150},
            {title: '反馈时间', dataIndex: 'feedbackTime', key: 'feedbackTime', width: 150},
            {title: '回复状态', dataIndex: 'replyStatus', key: 'replyStatus', width: 100},
            {title: '操作', key: 'action', fixed: 'right', width: 180}
        ];

        const loading = ref(false);
        const tableData = ref([]);
        
        // 获取表格数据函数声明
        const fetchTableData = async () => {
            loading.value = true;
            
            // 使用setTimeout让UI先更新loading状态，避免界面阻塞
            setTimeout(async () => {
                try {
                    // 构建请求参数
                    const params = {
                        pageNum: pagination.current,
                        pageSize: pagination.pageSize
                    };

                    // 处理时间范围
                    if (searchForm.feedbackTimeRange && searchForm.feedbackTimeRange.length === 2) {
                        params.startTime = searchForm.feedbackTimeRange[0];
                        params.endTime = searchForm.feedbackTimeRange[1];
                    }

                    // 调用API获取数据
                    const result = await getFeedbackList(params);

                    // 数据比较，避免不必要的更新
                    const newData = processTableData(result);
                    if (JSON.stringify(tableData.value) !== JSON.stringify(newData)) {
                        tableData.value = newData;
                    }

                    // 更新分页信息
                    updatePagination({
                        total: result?.total || 0,
                        current: result?.pageNum || pagination.current,
                        pageSize: result?.pageSize || pagination.pageSize
                    });
                } catch (error) {
                    console.error('获取反馈列表失败:', error);
                    tableData.value = [];
                    updatePagination({ total: 0, current: 1 });
                } finally {
                    loading.value = false;
                }
            }, 0);
        };
        
        // 使用表格分页组合式函数
        const { 
            pagination, 
            handleTableChange, 
            updatePagination, 
            resetPagination 
        } = useTablePagination({
            fetchData: fetchTableData,
            initialPagination: { 
                current: 1, 
                pageSize: 10, 
                total: 0,
                showTotal: (total) => `共 ${total} 条`
            },
            searchForm
        });

        // 详情相关
        const detailVisible = ref(false);
        const currentRecord = ref(null);

        // 解析图片数据
        const parseImages = (imagesStr) => {
            if (!imagesStr) return [];
            let imgs=JSON.parse(imagesStr).split(',')
            imgs=imgs.map(item=>VITE_APP_API_BASE_IMG_URL+item)
            try {
                return imgs;
            } catch (e) {
                console.error('解析图片数据失败:', e);
                return [];
            }
        };

        // 处理表格数据
        const processTableData = (data) => {
            if (!data || !data.list) return [];
            return data.list.map(item => ({
                ...item,
                images: parseImages(item.feedbackImgs)
            }));
        };

        // 回复表单
        const replyForm = reactive({
            replyContent: ''
        });
        
        // 提交状态
        const submitting = ref(false);

        // 搜索处理
        const handleSearch = (values) => {
            if (values) {
                Object.assign(searchForm, values);
            }
            resetPagination();
            fetchTableData();
        };

        // 重置搜索
        const resetSearch = (values) => {
            if (values) {
                Object.assign(searchForm, values);
            } else {
                searchForm.feedbackTimeRange = [];
            }
            resetPagination();
            fetchTableData();
        };

        // 查看详情
        const viewDetail = (record) => {
            currentRecord.value = record;
            detailVisible.value = true;
            // 重置回复表单
            replyForm.replyContent = '';
        };

        // 提交回复
        const submitReply = async () => {
            if (!replyForm.replyContent.trim()) {
                message.warning('请输入回复内容');
                return;
            }
            
            submitting.value = true;
            try {
                await replyFeedback(currentRecord.value.id, {
                    replyContent: replyForm.replyContent
                });
                
                message.success('回复成功');
                
                // 更新当前记录的回复状态
                currentRecord.value.replyContent = replyForm.replyContent;
                currentRecord.value.replyTime = new Date().toISOString();
                
                // 刷新列表数据
                fetchTableData();
            } catch (error) {
                console.error('回复反馈失败:', error);
                message.error('回复失败，请重试');
            } finally {
                submitting.value = false;
            }
        };

        onMounted(() => {
            fetchTableData();
        });

        return {
            searchForm,
            searchItems,
            columns,
            tableData,
            pagination,
            loading,
            handleTableChange,
            handleSearch,
            resetSearch,
            detailVisible,
            currentRecord,
            viewDetail,
            actionConfig,
            deleteTitle,
            resetPagination,
            replyForm,
            submitting,
            submitReply
        };
    }
});
</script>

<style lang="scss" scoped>
.table-card {
  .image-preview {
    display: flex;
    align-items: center;

    :deep(.ant-image) {
      margin-right: 4px;
    }

    .more-images {
      color: #999;
      font-size: 12px;
    }
  }

  :deep(.ant-table-container) {
    overflow-x: auto;
  }

  :deep(.ant-table-fixed-right) {
    background-color: #fff;
  }
}

.feedback-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.reply-content {
  line-height: 1.6;
  white-space: pre-wrap;
  color: #1890ff;
}

.reply-form {
  margin-top: 16px;
}

.feedback-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}
</style>
