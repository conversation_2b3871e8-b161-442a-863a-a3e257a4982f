<template>
  <a-drawer
    :visible="modelValue"
    :title="'文档详情'"
    :width="620"
    @close="$emit('update:modelValue', false)"
    :bodyStyle="{ paddingBottom: '80px' }"
  >
    <a-spin :spinning="loading">
      <div class="knowledge-detail" v-if="detailData">
        <!-- 文件信息卡片 -->
        <a-card class="file-card" :bordered="false">
          <div class="file-header">
            <div class="file-icon">
              <file-pdf-outlined v-if="detailData.fileType === 'pdf'" class="icon pdf" />
              <file-word-outlined v-if="detailData.fileType === 'word'" class="icon word" />
              <file-excel-outlined v-if="detailData.fileType === 'excel'" class="icon excel" />
              <file-ppt-outlined v-if="detailData.fileType === 'ppt'" class="icon ppt" />
              <file-image-outlined v-if="detailData.fileType === 'image'" class="icon image" />
              <file-text-outlined v-if="detailData.fileType === 'text'" class="icon text" />
              <file-outlined v-else class="icon unknown" />
            </div>
            <div class="file-info">
              <h3 class="file-name">{{ detailData.name || detailData.fileName }}</h3>
              <div class="file-meta">
                <a-tag :color="getFileTypeColor(detailData.fileType)">
                  {{ getFileTypeChinese(detailData.fileType) }}
                </a-tag>
                <span class="file-size" v-if="detailData.fileSize">{{ formatFileSize(detailData.fileSize) }}</span>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 文档详情信息 -->
        <a-descriptions title="基本信息" bordered :column="1" size="middle" class="detail-descriptions">
          <a-descriptions-item label="文档名称">{{ detailData.name || detailData.fileName }}</a-descriptions-item>
          <a-descriptions-item label="文件归属">{{ detailData.category }}</a-descriptions-item>
          <a-descriptions-item label="所属岗位">{{ detailData.position }}</a-descriptions-item>
          <a-descriptions-item label="上传时间">{{ formatDateTime(detailData.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="上传人员">{{ detailData.createBy }}</a-descriptions-item>
          <a-descriptions-item label="备注" v-if="detailData.remarks">{{ detailData.remarks }}</a-descriptions-item>
        </a-descriptions>
      </div>
      
      <div class="empty-data" v-else>
        <a-empty description="暂无文档详情" />
      </div>
    </a-spin>

    <!-- 抽屉底部操作按钮 -->
    <div class="drawer-footer">
      <a-button @click="$emit('update:modelValue', false)">关闭</a-button>
      <a-button type="primary" @click="handleDownload" :loading="downloading">
        <download-outlined /> 下载文档
      </a-button>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { 
  DownloadOutlined, 
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileOutlined,
  FileImageOutlined,
  FileTextOutlined 
} from '@ant-design/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'download']);

// 文件下载中状态
const downloading = ref(false);

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let fileSize = size;
  let unitIndex = 0;
  
  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024;
    unitIndex++;
  }
  
  return `${fileSize.toFixed(2)} ${units[unitIndex]}`;
};

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '未知时间';
  
  const date = new Date(datetime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取文件类型颜色
const getFileTypeColor = (type) => {
  const colorMap = {
    'pdf': '#f56c6c',
    'word': '#409eff', 
    'excel': '#67c23a',
    'ppt': '#e6a23c',
    'image': '#a18cd1',
    'text': '#909399',
    'unknown': '#909399'
  };
  
  return colorMap[type] || '#909399';
};

// 获取文件类型中文名称
const getFileTypeChinese = (fileType) => {
  const typeMap = {
    'pdf': 'PDF文档',
    'word': 'Word文档',
    'excel': 'Excel表格',
    'ppt': 'PPT演示',
    'image': '图片',
    'text': '文本',
    'unknown': '未知类型'
  };
  return typeMap[fileType] || '未知类型';
};

// 处理下载
const handleDownload = async () => {
  if (!props.detailData) return;
  
  downloading.value = true;
  try {
    emit('download', props.detailData);
  } finally {
    downloading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.knowledge-detail {
  .file-card {
    margin-bottom: 20px;
    
    .file-header {
      display: flex;
      align-items: center;
      
      .file-icon {
        width: 60px;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        
        .icon {
          font-size: 40px;
          
          &.pdf {
            color: #f56c6c;
          }
          
          &.word {
            color: #409eff;
          }
          
          &.excel {
            color: #67c23a;
          }
          
          &.ppt {
            color: #e6a23c;
          }
          
          &.image {
            color: #a18cd1;
          }
          
          &.text {
            color: #909399;
          }
          
          &.unknown {
            color: #909399;
          }
        }
      }
      
      .file-info {
        flex: 1;
        
        .file-name {
          font-size: 18px;
          margin: 0 0 8px;
          word-break: break-all;
        }
        
        .file-meta {
          display: flex;
          align-items: center;
          
          .file-size {
            margin-left: 10px;
            color: #909399;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .detail-descriptions {
    margin-bottom: 20px;
  }
}

.empty-data {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
  
  button:not(:last-child) {
    margin-right: 8px;
  }
}
</style> 