/**
 * Token存储的key
 */
const TOKEN_KEY = 'token';

/**
 * 用户信息存储的key
 */
const USER_KEY = 'user_info';

/**
 * 保存token到localStorage
 * @param {string} token 
 */
export const setToken = (token) => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 从localStorage获取token
 * @returns {string|null}
 */
export const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 从localStorage删除token
 */
export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

/**
 * 保存用户信息到localStorage
 * @param {Object} user 
 */
export const setUserInfo = (user) => {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * 从localStorage获取用户信息
 * @returns {Object|null}
 */
export const getUserInfo = () => {
  const userInfo = localStorage.getItem(USER_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
};

/**
 * 从localStorage删除用户信息
 */
export const removeUserInfo = () => {
  localStorage.removeItem(USER_KEY);
};

/**
 * 清除所有认证信息
 */
export const clearAuth = () => {
  removeToken();
  removeUserInfo();
};

/**
 * 检查是否已登录
 * @returns {boolean}
 */
export const isLoggedIn = () => {
  return !!getToken();
}; 