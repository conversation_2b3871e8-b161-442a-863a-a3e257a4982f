# 搜索表单组件 (SearchForm)

一个灵活可配置的搜索表单组件，用于系统中各页面的搜索功能。

## 功能特点

- 支持多种常用表单控件：输入框、选择框、日期选择器、日期范围选择器、数字输入框等
- 支持表单项动态配置
- 支持表单数据的双向绑定
- 内置搜索和重置功能
- 支持卡片样式包装

## 组件类型

- `SearchForm`: 基础搜索表单组件
- `SearchFormCard`: 带卡片样式的搜索表单组件

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <search-form
      v-model="searchForm"
      :items="searchItems"
      @search="handleSearch"
      @reset="resetSearch"
    />
    
    <!-- 其他内容... -->
  </div>
</template>

<script>
import { defineComponent, reactive } from 'vue';
import { SearchForm } from '@/components/SearchForm';

export default defineComponent({
  components: {
    SearchForm
  },
  setup() {
    // 搜索表单数据
    const searchForm = reactive({
      name: '',
      status: undefined,
      date: undefined
    });
    
    // 搜索表单配置
    const searchItems = [
      {
        label: '名称',
        field: 'name',
        type: 'input',
        placeholder: '请输入名称'
      },
      {
        label: '状态',
        field: 'status',
        type: 'select',
        width: '120px',
        options: [
          { label: '全部', value: '' },
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' }
        ]
      },
      {
        label: '日期',
        field: 'date',
        type: 'date'
      }
    ];
    
    // 搜索处理
    const handleSearch = (values) => {
      console.log('搜索参数：', values);
      // 执行搜索逻辑...
    };
    
    // 重置搜索
    const resetSearch = () => {
      // 可以在这里添加重置后的额外逻辑
      console.log('表单已重置');
    };
    
    return {
      searchForm,
      searchItems,
      handleSearch,
      resetSearch
    };
  }
});
</script>
```

### 使用卡片样式

```vue
<template>
  <div>
    <search-form-card
      v-model="searchForm"
      :items="searchItems"
      @search="handleSearch"
      @reset="resetSearch"
    />
    
    <!-- 其他内容... -->
  </div>
</template>

<script>
import { defineComponent, reactive } from 'vue';
import { SearchFormCard } from '@/components/SearchForm';

export default defineComponent({
  components: {
    SearchFormCard
  },
  // 其他配置同上...
});
</script>
```

## 属性说明

### SearchForm / SearchFormCard

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| modelValue / v-model | Object | {} | 表单绑定的数据对象 |
| items | Array | [] | 表单项配置数组 |
| searchText | String | '查询' | 搜索按钮文本 |
| resetText | String | '重置' | 重置按钮文本 |
| resetPage | Boolean | true | 搜索时是否重置分页到第一页 |
| formClass | String | '' | 表单额外类名 |

### 表单项配置(items)

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| label | String | - | 表单项标签文本 |
| field | String | - | 表单项绑定的字段名 |
| type | String | - | 表单项类型，支持: 'input', 'select', 'date', 'dateRange', 'number' |
| placeholder | String / Array | - | 占位文本，dateRange类型为数组 |
| width | String | - | 表单控件宽度，如 '120px' |
| options | Array | - | 下拉选项，用于select类型，格式：[{label: '显示值', value: '实际值'}] |
| showSearch | Boolean | false | 是否可搜索，用于select类型 |
| filterOption | Function | - | 自定义筛选函数，用于select类型 |

## 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| search | formValues | 点击搜索按钮时触发，参数为当前表单值 |
| reset | formValues | 点击重置按钮时触发，参数为重置后的表单值 |
| update:modelValue | formValues | 表单值更新时触发，用于v-model双向绑定 | 