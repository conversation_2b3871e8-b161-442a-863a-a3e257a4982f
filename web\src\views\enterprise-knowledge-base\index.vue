<!-- 企业知识库管理模块 -->
<template>
  <div class="page-container">
    <!-- 全屏遮罩加载层 -->
    <div class="global-loading-mask" v-if="fullscreenLoading">
      <div class="loading-spinner">
        <a-spin size="large" />
        <div class="loading-text">段落加载中...</div>
      </div>
    </div>
    <page-header>
      <template #search>
        <!-- 搜索区域 -->
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />

      </template>
      <template #actions>
        <a-space>
          <a-button type="primary" @click="handleUpload">
            <upload-outlined /> 上传文档
          </a-button>
          <a-button danger :disabled="!selectedRowKeys.length" @click="handleBatchDelete">
            <delete-outlined /> 批量删除
          </a-button>
        </a-space>
      </template>
    </page-header>
      
      <!-- 表格区域 -->
       <base-table
        :columns="columns"
        :data-source="documentList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :action-config="actionConfig"
        :delete-title="deleteTitle"
        @edit="handleEditDocument"
        @delete="handleDeleteDocument"
        :row-selection="rowSelection"
      >
        <!-- 证书名称列 -->
        <template #bodyCell="{ column, record }">

          <template v-if="column.key === 'fileName'">
            <div class="document-name">
              <div class="document-icon">
                <file-pdf-outlined v-if="record.fileType === 'pdf'" class="pdf-icon" />
                <file-word-outlined v-else-if="record.fileType === 'word'" class="word-icon" />
                <file-excel-outlined v-else-if="record.fileType === 'excel'" class="excel-icon" />
                <file-ppt-outlined v-else-if="record.fileType === 'ppt'" class="ppt-icon" />
                <file-image-outlined v-else-if="record.fileType === 'image'" class="image-icon" />
                <file-text-outlined v-else-if="record.fileType === 'text'" class="text-icon" />
                <file-outlined v-else class="file-icon" />
              </div>
              <span :title="record.fileName">{{ record.fileName }}</span>
            </div>
          </template>

          <!-- 文件归属列 -->
          <template v-else-if="column.key === 'category' || column.dataIndex === 'fileCategory'">
            <a-tag color="purple">{{ getCategoryById(record.fileCategory) }}</a-tag>
          </template>

          <!-- 所属岗位列 -->
          <template v-else-if="column.key === 'position' || column.dataIndex === 'position'">
            <a-tag color="green">{{ getPositionById(record.position) }}</a-tag>
          </template>

          <!-- 处理状态列 -->
          <template v-else-if="column.key === 'processStatus'">
            <doc-process-status 
              :docId="record.id" 
              :autoRefresh="shouldAutoRefresh(record)"
              :refreshInterval="5000" 
              :initialStatus="getInitialStatus(record)"
              @status-change="handleProcessStatusChange" 
            />
          </template>

          <!-- 文档状态列 -->
          <template v-else-if="column.key === 'status' || column.dataIndex === 'status'">
            <a-switch
              :checked="record.status === 'enable' || record.status === null || record.status === undefined"
              :loading="record.statusLoading"
              @change="(checked) => handleStatusChange(record, checked ? 'enable' : 'disable')"
            />
          </template>

          <!-- 文件大小列 -->
          <template v-else-if="column.key === 'fileSize'">
            <span>{{ formatFileSize(record.fileSize) }}</span>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createdTime'">
            <span>{{ formatDate(record.createdTime) }}</span>
          </template>

          <!-- 更新时间列 -->
          <template v-else-if="column.key === 'updatedTime'">
            <span>{{ formatDate(record.updatedTime) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-button type="link" size="small" class="custom-button" @click="handleManageParagraphs(record)">
              <unordered-list-outlined /> 段落
            </a-button>
          </template>
        </template>
      </base-table>

    <!-- 上传/编辑文档弹窗 -->
    <upload-modal
      v-model:visible="uploadModalVisible"
      :document-data="currentDocument"
      @success="handleUploadSuccess"
    />

    <!-- 段落管理抽屉 -->
    <paragraphs-drawer
      v-model:visible="paragraphsDrawerVisible"
      :document-id="currentDocumentForSegments && currentDocumentForSegments.id"
      :document-name="currentDocumentForSegments && currentDocumentForSegments.fileName"
      :initial-paragraphs="documentSegments"
      :fullscreenLoading="fullscreenLoading"
      @save="handleSaveSegments"
      @refresh="handleRefreshSegments"
      @delete="handleDeleteSegment"
      @setLoading="(val) => fullscreenLoading = val"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useDocumentUtils } from '../knowledge-base/composables/useDocumentUtils';
import { useSegmentsManager } from '../knowledge-base/composables/useSegmentsManager';
import { useTablePagination, getToolTip } from '@/utils/common';
import UploadModal from './components/UploadModal.vue';
import ParagraphsDrawer from '../knowledge-base/components/ParagraphsDrawer.vue';
import { SearchFormCard } from '@/components/SearchForm';
import DocProcessStatus from '@/components/DocProcessStatus.vue';
import {
  getEnterpriseKnowledgeList,
  toggleDocumentStatus,
  getDocumentProcessStatus,
  batchDeleteDocuments
} from '@/api/enterprise-knowledge-base';
import { updateSegment } from '@/api/knowledge-base/segments';
import { getPositionTypeOptions, getPositionNameOptions } from '@/api/organization/position';
import {
  UploadOutlined,
  DeleteOutlined,
  UnorderedListOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileOutlined
} from '@ant-design/icons-vue';

// 使用文档工具函数
const { formatDate, getCategoryLabel, getPositionLabel } = useDocumentUtils();

// 使用段落管理
const {
  documentSegments,
  paragraphsDrawerVisible,
  currentDocumentForSegments,
  openSegmentsDrawer,
  saveSegments,
  removeSegment,
  fullscreenLoading
} = useSegmentsManager();

// 添加key值用于强制重新渲染搜索表单
const formKey = ref(0);

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0 || !bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 分类选项和岗位选项
const categoryOptions = ref([]);
const positionOptions = ref([]);

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除此文档吗?';

// 获取选项数据
const fetchOptions = async () => {
  try {
    const [typeRes, nameRes] = await Promise.all([
      getPositionTypeOptions(),
      getPositionNameOptions()
    ]);

    // 处理岗位类型数据为选项格式
    categoryOptions.value = Array.isArray(typeRes) ? typeRes :
                           (typeRes && typeRes.rows ? typeRes.rows : []);

    // 处理岗位名称数据为选项格式
    positionOptions.value = Array.isArray(nameRes) ? nameRes :
                           (nameRes && nameRes.rows ? nameRes.rows : []);
    
    console.log('文件分类选项:', categoryOptions.value);
    console.log('岗位名称选项:', positionOptions.value);
  } catch (error) {
    console.error('获取选项数据失败', error);
    message.error('获取选项数据失败');
    categoryOptions.value = [];
    positionOptions.value = [];
  }
};

// 表格列定义
const columns = [
  {
    title: '证书名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    sorter: true,
    width: 180,
    minWidth: 150
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true,
    width: 200,
    minWidth: 180
  },
  {
    title: '文件归属',
    dataIndex: 'fileCategory',
    key: 'category',
    width: 120,
    minWidth: 100
  },
  {
    title: '所属岗位',
    dataIndex: 'position',
    key: 'position',
    width: 120,
    minWidth: 100
  },
  {
    title: '处理状态',
    key: 'processStatus',
    width: 250,
    minWidth: 220
  },
  {
    title: '文档状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 120,
    minWidth: 100
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    customRender: (size) => {
      return formatFileSize(size.text)
    },
    sorter: true,
    width: 120,
    minWidth: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
    sorter: true,
    width: 180,
    minWidth: 160
  },
  {
    title: '更新时间',
    dataIndex: 'updatedTime',
    key: 'updatedTime',
    sorter: true,
    width: 180,
    minWidth: 160
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 280,
    minWidth: 250
  }
];

// 复选框
const rowSelection = {
  onChange: (keys, selectedRows) => {
    selectedRowKeys.value = keys;
  },
};

// 查询参数
const searchForm = reactive({
  searchText: '',
  fileCategory: undefined,
  position: undefined,
  sortField: '',
  sortOrder: ''
});

// 状态变量
const documentList = ref([]);
const loading = ref(false);
const uploadModalVisible = ref(false);
const currentDocument = ref(null);
const selectedRowKeys = ref([]);

// 获取文档列表
const fetchDocumentList = async () => {
  loading.value = true;

  try {
    const params = {
      searchText: searchForm.searchText,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      fileCategory: searchForm.fileCategory,
      position: searchForm.position,
      documentType: 'enterprise' // 只查询企业知识库文档
    };
    
    // 添加排序参数
    if (searchForm.sortField) {
      params.sortField = searchForm.sortField;
      params.sortOrder = searchForm.sortOrder;
    }

    const response = await getEnterpriseKnowledgeList(params);

    if (response && response.list) {
      documentList.value = response.list.map(item => {
        if (!item.category && item.fileCategory) {
          item.category = item.fileCategory;
        }

        const mappedItem = { ...item };

        // 处理文件大小字段
        if (!mappedItem.fileSize && (mappedItem.size || mappedItem.filesize)) {
          mappedItem.fileSize = mappedItem.size || mappedItem.filesize;
        }

        // 处理创建时间和更新时间字段
        if (item.createTime && !item.createdTime) {
          mappedItem.createdTime = item.createTime;
        }

        if (item.updateTime && !item.updatedTime) {
          mappedItem.updatedTime = item.updateTime;
        }
        
        // 处理处理状态字段
        if (!mappedItem.processStatus && mappedItem.process_status) {
          mappedItem.processStatus = mappedItem.process_status;
        }
        
        if (!mappedItem.processProgress && mappedItem.process_progress) {
          mappedItem.processProgress = mappedItem.process_progress;
        }
        
        if (!mappedItem.processMessage && mappedItem.process_message) {
          mappedItem.processMessage = mappedItem.process_message;
        }

        return mappedItem;
      });

      updatePagination({
        total: response.total || documentList.value.length,
        pageNum: response.pageNum,
        pageSize: response.pageSize
      });
    } else {
      documentList.value = [];
      updatePagination({ total: 0, pageNum: 1 });
    }
  } catch (error) {
    console.error('获取企业知识库数据失败', error);
    message.error('获取企业知识库数据失败');
    documentList.value = [];
    updatePagination({ total: 0, pageNum: 1 });
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: fetchDocumentList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 搜索
const handleSearch = (values) => {
  if (values) {
    const { sortField, sortOrder } = searchForm;
    Object.assign(searchForm, values, { sortField, sortOrder });
  }
  resetPagination();
  fetchDocumentList();
};

// 重置查询
const resetSearch = () => {
  searchForm.searchText = '';
  searchForm.fileCategory = undefined;
  searchForm.position = undefined;
  searchForm.sortField = '';
  searchForm.sortOrder = '';
  resetPagination();
  fetchDocumentList();
};

// 上传文档
const handleUpload = async () => {
  loading.value = true;
  
  try {
    // 检查所有文档的处理状态
    const processingDocuments = [];
    
    const checkPromises = documentList.value.map(async (doc) => {
      try {
        const status = await getDocumentProcessStatus(doc.id);
        if (status && status.status &&
            status.status !== 'completed' &&
            status.status !== 'failed') {
          processingDocuments.push(doc);
        }
      } catch (error) {
        console.log(`获取文档 ${doc.id} 状态失败，视为已处理完成`);
      }
    });
    
    await Promise.all(checkPromises);
    
    if (processingDocuments.length > 0) {
      message.warning('有文件正在处理中，请等待文件处理完成后再上传，或刷新界面');
      return;
    }
    
    currentDocument.value = null;
    uploadModalVisible.value = true;
  } catch (error) {
    console.error('检查文档处理状态失败', error);
    message.error('检查文档状态失败，请刷新页面后重试');
  } finally {
    loading.value = false;
  }
};

// 编辑文档
const handleEditDocument = (record) => {
  currentDocument.value = record;
  uploadModalVisible.value = true;
};

// 上传成功回调
const handleUploadSuccess = () => {
  uploadModalVisible.value = false;
  fetchDocumentList();
};

// 删除文档
const handleDeleteDocument = async (record) => {
  try {
    await batchDeleteDocuments([record.id]);
    message.success('删除成功');
    fetchDocumentList();
  } catch (error) {
    message.error('删除失败');
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的文档');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个文档吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true;
        await batchDeleteDocuments(selectedRowKeys.value);
        message.success(`已批量删除 ${selectedRowKeys.value.length} 个文档`);
        selectedRowKeys.value = [];
        fetchDocumentList();
      } catch (error) {
        message.error('批量删除失败');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 根据ID获取文件分类名称
const getCategoryById = (id) => {
  if (!id) {
    return '-';
  }
  
  // 处理特殊值
  if (id === 'COMMON_TYPE') {
    return '岗位类别通用';
  }
  
  const numId = parseInt(id);
  if (Array.isArray(categoryOptions.value) && categoryOptions.value.length > 0) {
    let category = categoryOptions.value.find(item => {
      const itemId = item.id || item.value || item.dictValue;
      return itemId == id;
    });
    
    if (!category && typeof id === 'string') {
      category = categoryOptions.value.find(item => {
        const itemLabel = item.dictLabel || item.name || item.label;
        return itemLabel && itemLabel.includes(id);
      });
    }
    
    if (category) {
      const label = category.dictLabel || category.name || category.label;
      return label;
    } else {
      console.warn('未找到匹配的文件归属:', id);
    }
  } else {
    console.warn('文件归属选项为空或不是数组');
  }
  
  return id;
};

// 根据ID获取岗位名称
const getPositionById = (id) => {
  if (!id) {
    return '-';
  }
  
  // 处理特殊值
  if (id === 'COMMON') {
    return '通用';
  }
  
  const numId = parseInt(id);
  if (Array.isArray(positionOptions.value) && positionOptions.value.length > 0) {
    let position = positionOptions.value.find(item => {
      const itemId = item.id || item.value || item.dictValue;
      return itemId == id;
    });
    
    if (!position && typeof id === 'string') {
      position = positionOptions.value.find(item => {
        const itemLabel = item.dictLabel || item.name || item.label;
        return itemLabel && itemLabel.includes(id);
      });
    }
    
    if (position) {
      const label = position.dictLabel || position.name || position.label;
      return label;
    } else {
      console.warn('未找到匹配的所属岗位:', id);
    }
  } else {
    console.warn('所属岗位选项为空或不是数组');
  }
  
  return id;
};

// 段落管理
const handleManageParagraphs = (record) => {
  openSegmentsDrawer(record);
};

// 删除段落
const handleDeleteSegment = async (segmentId) => {
  if (!currentDocumentForSegments.value?.id) {
    message.error('删除段落失败：文档ID缺失');
    return;
  }
  
  try {
    const documentId = currentDocumentForSegments.value.id;
    await removeSegment(documentId, segmentId);
    message.success('段落已删除');
    
    await openSegmentsDrawer(currentDocumentForSegments.value);
  } catch (error) {
    message.error('删除段落失败：' + (error.message || '未知错误'));
  }
};

// 刷新段落数据
const handleRefreshSegments = async () => {
  if (!currentDocumentForSegments.value?.id) {
    message.error('刷新段落数据失败：文档ID缺失');
    return;
  }
  
  try {
    await openSegmentsDrawer(currentDocumentForSegments.value);
  } catch (error) {
    message.error('刷新段落数据失败');
  }
};

// 保存段落
const handleSaveSegments = async (segments, isSingleSegment = false, isNewSegment = false) => {
  if (!currentDocumentForSegments.value?.id) {
    console.error('当前文档ID缺失');
    message.error('保存段落失败：文档ID缺失');
    return;
  }
  
  const documentId = currentDocumentForSegments.value.id;
  
  fullscreenLoading.value = true;
  try {
    if (isSingleSegment && segments.length === 1 && segments[0].id && !segments[0].id.startsWith('temp_')) {
      const segment = segments[0];
      const { id, ...updateData } = segment;
      await updateSegment(documentId, id, updateData);
      message.success('段落已更新');
    } else if (isNewSegment) {
      await saveSegments(documentId, segments);
      await openSegmentsDrawer(currentDocumentForSegments.value);
    } else {
      await saveSegments(documentId, segments);
      await openSegmentsDrawer(currentDocumentForSegments.value);
    }
    
    if (!isSingleSegment && !isNewSegment) {
      await openSegmentsDrawer(currentDocumentForSegments.value);
      console.log('段落数据已刷新');
    }
  } catch (error) {
    console.error('保存段落数据失败', error);
    message.error('保存段落失败：' + (error.message || '未知错误'));
  } finally {
    fullscreenLoading.value = false;
  }
};

// 搜索表单配置
const searchFormItems = computed(() => {
  console.log('生成搜索表单时的分类选项:', categoryOptions.value);
  console.log('生成搜索表单时的岗位选项:', positionOptions.value);
  
  const enhancedCategoryOptions = Array.isArray(categoryOptions.value) 
    ? categoryOptions.value.map(item => ({
        ...item,
        id: item.id || item.value || item.dictValue,
        dictLabel: item.dictLabel || item.name || item.label || '未命名类型',
        label: item.dictLabel || item.name || item.label || '未命名类型',
        value: item.id || item.value || item.dictValue
      }))
    : [];
  
  const enhancedPositionOptions = Array.isArray(positionOptions.value)
    ? positionOptions.value.map(item => ({
        ...item,
        id: item.id || item.value || item.dictValue,
        dictLabel: item.dictLabel || item.name || item.label || '未命名岗位',
        label: item.dictLabel || item.name || item.label || '未命名岗位',
        value: item.id || item.value || item.dictValue
      }))
    : [];
  
  return [
    {
      label: '证书/文件名',
      field: 'searchText',
      type: 'input',
      placeholder: '搜索证书名称/文件名称',
      width: '220px'
    },
    {
      label: '文件归属',
      field: 'fileCategory',
      type: 'select',
      placeholder: '请选择归属',
      width: '200px',
      options: enhancedCategoryOptions,
      selectLabel: 'label',
      selectValue: 'value'
    },
    {
      label: '所属岗位',
      field: 'position',
      type: 'select',
      placeholder: '请选择岗位',
      width: '200px',
      options: enhancedPositionOptions,
      selectLabel: 'label',
      selectValue: 'value'
    }
  ]
})

// 处理文档状态切换
const handleStatusChange = async (record, status) => {
  const originalStatus = record.status === null || record.status === undefined ? 'enable' : record.status;
  
  try {
    record.statusLoading = true;
    
    await toggleDocumentStatus(record.id, status);
    
    record.status = status;
    
    message.success(status === 'enable' ? '文档已启用' : '文档已禁用');
  } catch (error) {
    console.error('切换文档状态失败', error);
    message.error('切换状态失败: ' + (error.message || '未知错误'));
    
    record.status = originalStatus;
  } finally {
    record.statusLoading = false;
  }
};

// 判断是否需要自动刷新状态
const shouldAutoRefresh = (record) => {
  return !(record.processStatus === 'completed' || record.processStatus === 'failed');
};

// 获取初始状态对象
const getInitialStatus = (record) => {
  if (record.processStatus) {
    return {
      id: record.id,
      status: record.processStatus,
      progress: record.processProgress || 0,
      message: record.processMessage || '',
      updatedAt: record.updatedTime
    };
  }
  
  return null;
};

// 处理状态变化
const handleProcessStatusChange = (status) => {
  if (status.status === 'completed' || status.status === 'failed') {
    const doc = documentList.value.find(item => item.id === status.id);
    if (doc) {
      doc.processStatus = status.status;
      doc.processProgress = status.progress;
      doc.processMessage = status.message;
    }
  }
};

// 初始化
onMounted(() => {
  Promise.all([
    fetchOptions(),
    fetchDocumentList()
  ]).then(() => {
    formKey.value += 1;
  }).catch(error => {
    console.error('初始化失败', error);
  });
});
</script>

<style lang="scss" scoped>
.document-name {
  display: flex;
  align-items: center;

  .document-icon {
    margin-right: 8px;
    font-size: 20px;

    .pdf-icon {
      color: #f56c6c;
    }

    .word-icon {
      color: #409eff;
    }

    .excel-icon {
      color: #67c23a;
    }

    .ppt-icon {
      color: #e6a23c;
    }

    .image-icon {
      color: #9c27b0;
    }

    .text-icon {
      color: #909399;
    }
  }
}

// 添加全屏遮罩层样式
.global-loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: wait;
  user-select: none;
  pointer-events: auto;

  .loading-spinner {
    text-align: center;
  }

  .loading-text {
    color: #fff;
    margin-top: 12px;
    font-size: 16px;
  }

  :deep(.ant-spin-dot-item) {
    background-color: #fff;
  }
}
</style> 