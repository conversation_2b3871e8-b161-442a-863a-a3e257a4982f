import request from '@/utils/request'

// 获取菜单列表
export function getMenuList() {
  return request({
    url: '/system/menu/list',
    method: 'get'
  })
}

// 创建菜单
export function createMenu(data) {
  // 在请求前进行数据检查和处理
  const processedData = processMenuData(data);
  
  return request({
    url: '/system/menu',
    method: 'post',
    data: processedData
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('创建菜单失败:', error);
    return Promise.reject(error);
  });
}

// 更新菜单
export function updateMenu(data) {
  // 在请求前进行数据检查和处理
  const processedData = processMenuData(data);
  
  return request({
    url: '/system/menu',
    method: 'put',
    data: processedData
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('更新菜单失败:', error);
    return Promise.reject(error);
  });
}

// 删除菜单
export function deleteMenu(id) {
  return request({
    url: `/system/menu/${id}`,
    method: 'delete'
  })
}

// 处理菜单数据，确保格式正确
function processMenuData(data) {
  const result = { ...data };
  
  // 处理组件路径
  if (result.type === 1) { // 菜单类型为菜单
    if (result.component) {
      // 直接使用用户输入的组件路径，不做任何处理
    }
  } else {
    // 目录或按钮类型的组件路径设为null
    result.component = null;
  }
  
  // 确保perms字段不会被清空，用于所有类型的菜单
  if (result.perms) {
    // 保留权限标识字段
  }
  
  return result;
} 