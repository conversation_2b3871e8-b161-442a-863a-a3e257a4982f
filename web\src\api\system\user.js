import request from '@/utils/request'

const api = {
  User: '/system/user',
  UserInfo: '/system/user/info',
  UserList: '/system/user/list',
  UserCurrent: '/system/user/current',
  Login: '/system/user/login',
  UserMenuPermissions: '/system/user/menu-permissions',
  UserPassword: '/system/user/password',
  UserProfile: '/system/user/profile'
}

// 登录
export function login(data) {
  return request({
    url: api.Login,
    method: 'post',
    data
  })
}

// 获取当前登录用户信息
export function getCurrentUser() {
  return request({
    url: api.UserCurrent,
    method: 'get'
  })
}

// 获取用户信息
export function getUserInfo(params) {
  return request({
    url: api.UserInfo,
    method: 'get',
    params
  })
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: api.UserList,
    method: 'get',
    params
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: api.User,
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(data) {
  return request({
    url: api.User,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `${api.User}/${id}`,
    method: 'delete'
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/system/role/list',
    method: 'get'
  })
}

// 获取用户菜单权限
export function getUserMenuPermissions() {
  return request({
    url: api.UserMenuPermissions,
    method: 'get'
  })
}

// 更新用户个人资料
export function updateUserProfile(data) {
  return request({
    url: api.UserProfile,
    method: 'put',
    data
  })
}

// 修改用户密码
export function updateUserPassword(data) {
  return request({
    url: api.UserPassword,
    method: 'put',
    data
  })
} 