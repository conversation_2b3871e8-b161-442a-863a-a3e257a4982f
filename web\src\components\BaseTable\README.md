# BaseTable 基础表格组件

BaseTable 是一个封装了 Ant Design Vue 的表格组件，提供了常用的表格功能和界面，便于在系统中统一维护和使用。

## 功能特点

- 统一的表格样式和交互
- 内置默认操作列（详情、编辑、删除）
- 灵活的自定义插槽机制
- 完整的事件支持

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| columns | Array | - | 表格列配置，必填 |
| dataSource | Array | [] | 表格数据源 |
| rowKey | String/Function | 'id' | 行数据的唯一标识 |
| pagination | Object/Boolean | { ... } | 分页配置，设为 false 时不显示分页 |
| loading | Boolean | false | 数据加载状态 |
| scroll | Object | { x: 1500 } | 表格滚动配置 |
| showDefaultAction | Boolean | true | 是否显示默认操作按钮 |
| actionConfig | Object | { detail: true, edit: true, delete: true } | 操作按钮配置 |
| actionButtonStyle | Object | { color: '#a18cd1' } | 操作按钮样式 |
| cardProps | Object | {} | 外层卡片属性 |

## 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| change | { pagination, filters, sorter, extra } | 表格参数变化事件 |
| detail | record | 点击详情按钮时触发 |
| edit | record | 点击编辑按钮时触发 |
| delete | record | 点击删除按钮时触发 |

## 插槽

| 插槽名 | 说明 | 
| --- | --- |
| header | 表格顶部区域 |
| footer | 表格底部区域 |
| bodyCell | 自定义单元格内容 |
| actionBefore | 操作列按钮前的内容 |
| actionAfter | 操作列按钮后的内容 |

## 使用示例

```vue
<template>
  <base-table
    :columns="columns"
    :data-source="tableData"
    :loading="loading"
    @detail="handleDetail"
    @edit="handleEdit"
    @delete="handleDelete"
  >
    <!-- 自定义列 -->
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'status'">
        <a-tag :color="record.status === 'active' ? 'green' : 'red'">
          {{ record.statusText }}
        </a-tag>
      </template>
    </template>
    
    <!-- 表格顶部内容 -->
    <template #header>
      <div class="table-header">
        <h3>数据列表</h3>
        <a-button type="primary">导出</a-button>
      </div>
    </template>
  </base-table>
</template>

<script>
import { defineComponent, ref } from 'vue';

export default defineComponent({
  setup() {
    const loading = ref(false);
    const tableData = ref([]);
    
    const columns = [
      { title: '名称', dataIndex: 'name', key: 'name' },
      { title: '状态', dataIndex: 'status', key: 'status' },
      { title: '操作', key: 'action', fixed: 'right', width: 180 }
    ];
    
    const handleDetail = (record) => {
      console.log('查看详情', record);
    };
    
    const handleEdit = (record) => {
      console.log('编辑', record);
    };
    
    const handleDelete = (record) => {
      console.log('删除', record);
    };
    
    return {
      loading,
      tableData,
      columns,
      handleDetail,
      handleEdit,
      handleDelete
    };
  }
});
</script>
```

## 进阶用法

### 禁用默认操作按钮，完全自定义操作列

```vue
<base-table
  :columns="columns"
  :data-source="tableData"
  :show-default-action="false"
>
  <template #bodyCell="{ column, record }">
    <template v-if="column.key === 'action'">
      <div class="custom-actions">
        <a-button type="link" @click="viewDetails(record)">查看</a-button>
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="editItem(record)">编辑</a-menu-item>
              <a-menu-item @click="deleteItem(record)">删除</a-menu-item>
              <a-menu-item @click="exportItem(record)">导出</a-menu-item>
            </a-menu>
          </template>
          <a-button type="link">更多 <down-outlined /></a-button>
        </a-dropdown>
      </div>
    </template>
  </template>
</base-table>
```

### 自定义分页配置

```vue
<base-table
  :columns="columns"
  :data-source="tableData"
  :pagination="{
    current: current,
    pageSize: pageSize,
    total: total,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total) => `共 ${total} 条数据`
  }"
  @change="handleTableChange"
>
</base-table>
``` 