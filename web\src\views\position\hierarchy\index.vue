<template>
  <div class="page-container position-hierarchy-container">
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <template #actions>
        <a-button type="primary" v-if="isAdmin" @click="handleTypeManage">
          岗位类型管理
        </a-button>
        <a-button type="primary" :style="{ marginLeft: isAdmin ? '16px' : '0' }" @click="handleAddName">
          新增岗位名称
        </a-button>
      </template>
    </page-header>
    
    <div class="table-container">
      <base-table
        :columns="nameColumns"
        :data-source="tableData"
        :loading="nameLoading"
        :pagination="tablePagination"
        @change="handleTableChange"
        rowKey="id"
        :scroll="{ x: 1000 }"
        :action-config="nameActionConfig"
        :delete-title="deleteTitle"
        @edit="handleEdit"
        @delete="handleDeleteName"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'typeName'">
            {{ getTypeName(record.typeId) }}
          </template>
          <template v-if="column.key === 'positionLevels'">
            <div class="level-tags-container">
              <a-tag v-for="level in getSortedLevels(record.positionLevels)" :key="level.id" color="blue" class="level-tag">
                {{ level.name }}
              </a-tag>
              <span v-if="!record.positionLevels || record.positionLevels.length === 0" class="no-levels">
                暂无关联等级
              </span>
            </div>
          </template>
        </template>
      </base-table>
    </div>
    
    <!-- 岗位类型弹窗 -->
    <a-modal
      :title="typeModalTitle"
      :visible="typeModalVisible"
      :confirm-loading="typeModalLoading"
      @ok="handleTypeModalOk"
      @cancel="handleTypeModalCancel"
      width="800px"
    >
      <a-form
        ref="typeFormRef"
        :model="typeFormData"
        :rules="typeFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        v-if="typeModalMode === 'form'"
      >
        <a-form-item label="类型名称" name="name">
          <a-input v-model:value="typeFormData.name" placeholder="请输入类型名称" />
        </a-form-item>
        <!-- <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="typeFormData.sort" :min="0" />
        </a-form-item> -->
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="typeFormData.remark" placeholder="请输入备注" />
        </a-form-item>
      </a-form>
      
      <div v-else>
        <div style="margin-bottom: 16px; text-align: right;">
          <a-button type="primary" @click="handleAddTypeinModal">
            新增类型
          </a-button>
        </div>
        
        <!-- <a-input-search
          v-model:value="typeSearchValue"
          placeholder="搜索类型名称"
          style="margin-bottom: 12px"
          @search="onTypeSearch"
        /> -->
        
        <base-table
          :columns="typeColumns"
          :data-source="filteredTypeList"
          :loading="typeLoading"
          rowKey="id"
          :pagination="false"
          :action-config="typeActionConfig"
          :delete-title="typeDeleteTitle"
          @edit="handleEditTypeinModal"
          @delete="handleDeleteType"
        >
        </base-table>
      </div>
    </a-modal>
    
    <!-- 岗位名称弹窗 -->
    <a-modal
      :title="nameModalTitle"
      :visible="nameModalVisible"
      :confirm-loading="nameModalLoading"
      @ok="handleNameModalOk"
      @cancel="handleNameModalCancel"
    >
      <a-form
        ref="nameFormRef"
        :model="nameFormData"
        :rules="nameFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="岗位类型" name="typeId">
          <a-select
            v-model:value="nameFormData.typeId"
            placeholder="请选择岗位类型"
            style="width: 100%"
          >
            <a-select-option v-for="type in typeList" :key="type.id" :value="type.id">
              {{ type.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="岗位名称" name="name">
          <a-input v-model:value="nameFormData.name" placeholder="请输入岗位名称" />
        </a-form-item>
        <a-form-item label="岗位等级" name="levelIds">
          <a-select
            v-model:value="nameFormData.levelIds"
            mode="multiple"
            placeholder="请选择岗位等级"
            style="width: 100%"
            :max-tag-count="3"
          >
            <a-select-option v-for="level in levelList" :key="level.id" :value="level.id">
              {{ level.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="nameFormData.sort" :min="0" />
        </a-form-item> -->
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="nameFormData.remark" placeholder="请输入备注" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { defineComponent, ref, reactive, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { 
  getPositionTypeList, 
  getPositionTypeOptions,
  addPositionType, 
  updatePositionType, 
  deletePositionType,
  getPositionNameList,
  addPositionName,
  updatePositionName,
  deletePositionName
} from '@/api/organization/position';
import { getLevelOptions } from '@/api/organization/level';
import { useTablePagination } from '@/utils/common';
import { SearchFormCard } from '@/components/SearchForm';

// export default defineComponent({
//   name: 'PositionHierarchyManagement',
//   components: {
//     PageHeader,
//     SearchFormCard
//   },
const formKey = ref(0);
    // 判断是否为超级管理员
    const isAdmin = computed(() => {
      try {
        // 从localStorage获取用户信息
        const userInfoStr = localStorage.getItem('user_info');
        if (!userInfoStr) return false;
        
        // 解析用户信息
        const userInfo = JSON.parse(userInfoStr);
        
        // 根据截图，user_info包含id、enterpriseId、username等字段
        // 并且包含roles数组，其中roleCode为ROLE_ADMIN的是超级管理员
        if (userInfo && userInfo.roles && Array.isArray(userInfo.roles)) {
          // 检查是否有超级管理员角色
          return userInfo.roles.some(role => 
            role && role.roleCode === 'ROLE_ADMIN'
          );
        }
        
        return false;
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return false;
      }
    });

    // 岗位类型
    const typeLoading = ref(false);
    const typeList = ref([]);
    const typeSearchValue = ref('');
    const selectedTypeId = ref(undefined);
    
    // 岗位等级
    const levelList = ref([]);
    
    // 岗位名称
    const nameLoading = ref(false);
    const tableData = ref([]);
    const nameSearchValue = ref('');
    
    // 类型弹窗
    const typeModalVisible = ref(false);
    const typeModalLoading = ref(false);
    const typeModalTitle = ref('岗位类型管理');
    const typeModalMode = ref('list'); // 'list' 或 'form'
    const typeFormRef = ref(null);
    const typeFormData = reactive({
      id: null,
      name: '',
      sort: 0,
      remark: ''
    });
    const typeFormRules = {
      name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
    };
    
    // 岗位类型表格列
    const typeColumns = [
      { title: '类型名称', dataIndex: 'name', key: 'name' },
      // { title: '排序', dataIndex: 'sort', key: 'sort' },
      { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
      { title: '操作', key: 'action', width: 150 }
    ];
    
    // 名称弹窗
    const nameModalVisible = ref(false);
    const nameModalLoading = ref(false);
    const nameModalTitle = ref('新增岗位名称');
    const nameFormRef = ref(null);
    const nameFormData = reactive({
      id: null,
      typeId: undefined,
      name: '',
      levelIds: [],
      sort: 0,
      remark: ''
    });
    const nameFormRules = {
      typeId: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
      name: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
      levelIds: [{ required: true, type: 'array', min: 1, message: '请至少选择一个岗位等级', trigger: 'change' }]
    };
    
    // 表格设置
    const nameColumns = [
      { title: '岗位名称', dataIndex: 'name', key: 'name' },
      { title: '岗位类型',
       key: 'typeName'
       },
      { title: '关联等级', key: 'positionLevels', width: 200 },
      // { title: '排序', dataIndex: 'sort', key: 'sort' },
      { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
      { 
        title: '操作', 
        key: 'action', 
        fixed: 'right',
        width: 180
      }
    ];
    
    // 添加操作配置
    const nameActionConfig = ref({
      edit: true,
      delete: true
    });
    
    const deleteTitle = '确定要删除该岗位名称吗？';
    
    // 类型表格相关配置
    const typeActionConfig = ref({
      edit: true,
      delete: true
    });
    
    const typeDeleteTitle = '确定要删除该岗位类型吗？';

    // 使用useTablePagination替代直接声明pagination

    // 过滤后的岗位类型列表
    const filteredTypeList = computed(() => {
      if (!typeSearchValue.value) return typeList.value;
      
      return typeList.value.filter(item => 
        item.name.toLowerCase().includes(typeSearchValue.value.toLowerCase())
      );
    });
    
    // 过滤后的岗位名称列表
    // const filteredNameList = computed(() => {
    //   return tableData.value;
    // });
    
    // 添加搜索表单配置
    const searchForm = reactive({
      name: '',
      typeId: undefined
    });

    // 表单项配置
    const formItems = computed(() => [
      {
        label: '岗位名称',
        field: 'name',
        type: 'input',
        placeholder: '请输入岗位名称'
      },
      {
        label: '岗位类型',
        field: 'typeId',
        type: 'select',
        placeholder: '请选择岗位类型',
        // options: typeList.value.map(item => ({
        //   label: item.name,
        //   value: item.id
        // })),
        // ,
       options: typeList.value,
       selectLabel:'name',
       selectValue:'id',
        allowClear: true
      }
    ]);

    // 搜索处理函数
    const handleSearch = (values) => {
      if (values) {
        Object.assign(searchForm, values);
      }
      nameSearchValue.value = searchForm.name;
      selectedTypeId.value = searchForm.typeId;
      resetPagination();
      loadData();
    };

    // 重置搜索
    const resetSearch = () => {
      searchForm.name = '';
      searchForm.typeId = undefined;
      nameSearchValue.value = '';
      selectedTypeId.value = undefined;
      resetPagination();
      loadData();
    };

    // 声明loadData函数
    const loadData = async () => {
      nameLoading.value = true;
      
      // 使用setTimeout优化加载体验
      setTimeout(async () => {
        try {
          const params = {
            pageNum: tablePagination.current,
            pageSize: tablePagination.pageSize
          };
          
          if (selectedTypeId.value) {
            params.typeId = selectedTypeId.value;
          }
          
          if (nameSearchValue.value) {
            params.name = nameSearchValue.value;
          }
          
          const res = await getPositionNameList(params);
          
          // 数据比较，避免不必要的更新
          const newData = res.rows || [];
          if (JSON.stringify(tableData.value) !== JSON.stringify(newData)) {
            tableData.value = newData;
          }
          
          // 更新分页信息
          updatePagination({
            total: res.total || 0,
            current: res.pageNum || tablePagination.current,
            pageSize: res.pageSize || tablePagination.pageSize
          });
        } catch (error) {
          console.error('获取岗位名称列表失败：', error);
          message.error(error.message || '获取岗位名称列表失败');
          tableData.value = [];
          updatePagination({ total: 0, current: 1 });
        } finally {
          nameLoading.value = false;
        }
      }, 0);
    };
    
    // 使用useTablePagination
    const { 
      pagination: tablePagination, 
      handleTableChange, 
      updatePagination, 
      resetPagination 
    } = useTablePagination({
      fetchData: loadData,
      initialPagination: { 
        current: 1, 
        pageSize: 10, 
        total: 0,
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条`
      }
    });
    
    // 原有的获取岗位名称列表函数修改为
    const getNameList = () => {
      resetPagination();
      loadData();
    };
    
    // 选择岗位类型变更修改为
    const onTypeChange = () => {
      resetPagination();
      loadData();
    };
    
    // 搜索岗位名称修改为
    const onNameSearch = () => {
      resetPagination();
      loadData();
    };

    // 删除岗位名称修改为
    const handleDeleteName = async (record) => {
      try {
        await deletePositionName(record.id);
        message.success('删除成功');
        loadData();
      } catch (error) {
        message.error(error.message || '删除岗位名称失败');
      }
    };

    // 应用 BaseTable 的编辑事件处理
    const handleEdit = (record) => {
      nameModalTitle.value = '编辑岗位名称';
      Object.assign(nameFormData, record);
      // 处理等级ID的回填，如果record中有positionLevels数组则使用，否则初始化为空数组
      nameFormData.levelIds = record.positionLevels ? record.positionLevels.map(level => level.id) : [];
      nameModalVisible.value = true;
    };
    
    // 获取岗位类型名称
    const getTypeName = (typeId) => {
      if (!typeId) return '';
      const type = typeList.value.find(item => item.id === typeId);
      return type ? type.name : '';
    };
    
    // 获取排序后的等级列表
    const getSortedLevels = (levels) => {
      if (!levels || !Array.isArray(levels)) return [];
      return [...levels].sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0));
    };
    
    // 获取岗位类型列表
    const getTypeList = async () => {
      typeLoading.value = true;
      try {
        const res = await getPositionTypeList(); // 获取更多数据
        typeList.value = res.rows;
      } catch (error) {
        console.error('获取岗位类型列表失败：', error);
        message.error(error.message || '获取岗位类型列表失败');
      } finally {
        typeLoading.value = false;
      }
    };
    
    // 获取岗位等级列表
    const getLevelList = async () => {
      try {
        const res = await getLevelOptions();
        levelList.value = res || [];
      } catch (error) {
        console.error('获取岗位等级列表失败：', error);
        message.error(error.message || '获取岗位等级列表失败');
      }
    };
    
    // 搜索岗位类型
    const onTypeSearch = () => {
      // 仅过滤显示，无需调用API
    };
    
    // 打开岗位类型管理弹窗
    const handleTypeManage = () => {
      typeModalMode.value = 'list';
      typeModalTitle.value = '岗位类型管理';
      typeModalVisible.value = true;
    };
    
    // 弹窗中新增岗位类型
    const handleAddTypeinModal = () => {
      typeModalMode.value = 'form';
      typeModalTitle.value = '新增岗位类型';
      typeFormData.id = null;
      typeFormData.name = '';
      typeFormData.sort = 0;
      typeFormData.remark = '';
    };
    
    // 弹窗中编辑岗位类型
    const handleEditTypeinModal = (record) => {
      typeModalMode.value = 'form';
      typeModalTitle.value = '编辑岗位类型';
      Object.assign(typeFormData, record);
    };
    
    // 删除岗位类型
    const handleDeleteType = async (record) => {
      try {
        await deletePositionType(record.id);
        message.success('删除成功');
        getTypeList();
      } catch (error) {
        message.error(error.message || '删除岗位类型失败');
      }
    };
    
    // 新增岗位名称
    const handleAddName = () => {
      if (typeList.value.length === 0) {
        message.warning('请先新增岗位类型');
        return;
      }
      
      nameModalTitle.value = '新增岗位名称';
      nameFormData.id = null;
      nameFormData.typeId = undefined;
      nameFormData.name = '';
      nameFormData.levelIds = []; // 清空等级选择
      nameFormData.sort = 0;
      nameFormData.remark = '';
      nameModalVisible.value = true;
    };
    
    // 岗位类型弹窗确认
    const handleTypeModalOk = async () => {
      if (typeModalMode.value === 'list') {
        // 列表模式下，点击确定只是关闭弹窗
        typeModalVisible.value = false;
        return;
      }
      
      // 表单模式下，保存类型数据
      typeFormRef.value.validate().then(async () => {
        typeModalLoading.value = true;
        try {
          // 自动生成编码
          const submitData = { ...typeFormData };
          if (!submitData.id) {
            // 新增时自动生成编码，使用时间戳
            submitData.code = `T${Date.now()}`;
          }
          // 设置默认状态为true
          submitData.status = true;
          
          if (submitData.id) {
            await updatePositionType(submitData);
            message.success('更新成功');
          } else {
            await addPositionType(submitData);
            message.success('新增成功');
          }
          // 返回列表模式
          typeModalMode.value = 'list';
          getTypeList();
        } catch (error) {
          message.error(error.message || '保存岗位类型失败');
        } finally {
          typeModalLoading.value = false;
        }
      });
    };
    
    // 岗位类型弹窗取消
    const handleTypeModalCancel = () => {
      if (typeModalMode.value === 'form') {
        // 表单模式下，返回列表
        typeModalMode.value = 'list';
      } else {
        // 列表模式下，关闭弹窗
        typeModalVisible.value = false;
      }
    };
    
    // 岗位名称弹窗确认
    const handleNameModalOk = () => {
      nameFormRef.value.validate().then(async () => {
        nameModalLoading.value = true;
        try {
          // 自动生成编码
          const submitData = { ...nameFormData };
          if (!submitData.id) {
            // 新增时自动生成编码，格式：类型编码+时间戳
            const selectedType = typeList.value.find(type => type.id === submitData.typeId);
            const typePrefix = selectedType ? selectedType.code || 'P' : 'P';
            submitData.code = `${typePrefix}_${Date.now()}`;
          }
          // 设置默认状态为true
          submitData.status = true;
          
          if (submitData.id) {
            await updatePositionName(submitData);
            message.success('更新成功');
          } else {
            await addPositionName(submitData);
            message.success('新增成功');
          }
          nameModalVisible.value = false;
          loadData();
        } catch (error) {
          message.error(error.message || '保存岗位名称失败');
        } finally {
          nameModalLoading.value = false;
        }
      });
    };
    
    // 岗位名称弹窗取消
    const handleNameModalCancel = () => {
      nameModalVisible.value = false;
    };
    
    onMounted(async () => {
      await getTypeList();
      await getLevelList();
      formKey.value += 1;
      await loadData(); // 使用新的loadData替代getNameList
    });
    
// });
</script>

<style lang="less" scoped>
.position-hierarchy-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .danger {
    color: #ff4d4f;
  }
}

.table-container {
  flex: 1;
  height: calc(100% - 64px);
}

.level-tags-container {
  display: flex;
  flex-wrap: wrap;
}

.level-tag {
  margin: 2px;
}

.no-levels {
  color: #999;
  margin-left: 8px;
}
</style> 