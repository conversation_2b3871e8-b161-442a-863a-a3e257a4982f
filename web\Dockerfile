# 使用官方Node.js镜像
FROM node:18-alpine

# 设置Alpine镜像加速源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 设置npm加速源并安装依赖
RUN npm config set registry https://registry.npmmirror.com && \
    npm ci

# 复制源代码
COPY . .


# 暴露开发服务器端口
EXPOSE 5173

# 使用CMD运行开发服务器，支持环境变量注入
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--cors"]
