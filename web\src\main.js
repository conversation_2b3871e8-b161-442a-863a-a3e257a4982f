import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import '@ant-design/icons-vue'
import 'ant-design-vue/dist/reset.css'

// 导入 Ant Design Vue 中文语言包
// import zhCN from 'ant-design-vue/es/locale/zh_CN'

// 导入自定义全局样式
import '@/assets/styles/global.scss'

// 导入自定义全局组件
import Components from './components'

// 导入权限指令
import permission from '@/directives/permission'

// 直接导入常用组件，避免每个页面重复导入
// import PageHeader from '@/components/PageHeader'
// import { SearchForm, SearchFormCard } from '@/components/SearchForm'
// 导入常用图标
import { 
  PlusOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  EyeOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  UnorderedListOutlined, 
  QuestionCircleOutlined,
  UploadOutlined,
  UserOutlined,
  KeyOutlined,
  LockOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  SettingOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 导入dayjs及其插件
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import localeData from 'dayjs/plugin/localeData'

// 配置dayjs
dayjs.locale('zh-cn') // 设置语言为中文
dayjs.extend(customParseFormat) // 启用自定义解析格式
dayjs.extend(localeData) // 启用本地化数据

// 创建应用实例
const app = createApp(App)

// 使用路由
app.use(router)

// 使用状态管理
app.use(createPinia())

// 使用 Ant Design Vue
app.use(Antd)

// 使用全局组件
app.use(Components)

// 注册权限指令
app.directive('permission', permission)

// 手动注册常用组件
// app.component('PageHeader', PageHeader)
// app.component('SearchForm', SearchForm)
// app.component('SearchFormCard', SearchFormCard)

// 手动注册常用图标组件
const icons = [
  PlusOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  EyeOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  UnorderedListOutlined, 
  QuestionCircleOutlined,
  UploadOutlined,
  UserOutlined,
  KeyOutlined,
  LockOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  SettingOutlined,
  ExclamationCircleOutlined
]

icons.forEach(icon => {
  app.component(icon.displayName || icon.name, icon)
})

// 挂载应用
app.mount('#app')
