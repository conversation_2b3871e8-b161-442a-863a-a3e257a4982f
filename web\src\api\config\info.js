import request from '@/utils/request'

/**
 * 获取信息配置
 * @returns {Promise}
 */
export function getInfoConfig() {
  return request({
    url: '/info-config',
    method: 'get'
  })
}

/**
 * 更新信息配置
 * @param {Object} data 
 * @returns {Promise}
 */
export function updateInfoConfig(data) {
  return request({
    url: '/info-config',
    method: 'put',
    data
  })
}

/**
 * 上传图片
 * @param {File} file 图片文件
 * @returns {Promise}
 */
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/info-config/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 