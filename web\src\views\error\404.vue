<template>
  <div class="not-found">
    <div class="content">
      <div class="image">404</div>
      <h1>页面不存在</h1>
      <p>抱歉，您访问的页面不存在或已被删除</p>
      <a-button type="primary" @click="goHome" size="large" class="back-btn">
        返回首页
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  text-align: center;
}

.content {
  max-width: 500px;
  padding: 32px;
}

.image {
  font-size: 144px;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 24px;
  background-image: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 16px;
}

p {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

.back-btn {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border: none;
  height: 48px;
  font-size: 16px;
  transition: all 0.3s;
}

.back-btn:hover {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(161, 140, 209, 0.4);
}
</style> 