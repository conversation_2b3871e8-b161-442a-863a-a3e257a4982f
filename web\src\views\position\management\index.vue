<template>
  <div class="page-container">

    <page-header
    >
      <template #search>
        <search-form-card
        :model-value="searchForm" 
        :items="formItems"
        :key="formKey"
        @search="handleSearch"
        @reset="resetSearch"
      />
      </template>
      <template #actions>
        <a-button type="primary" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          新增
        </a-button>
      </template>
    </page-header>

    
    <!-- 列表区域 -->
    <base-table
      :columns="columns"
      :data-source="positionList"
      :loading="loading"
      @change="handleTableChange"
      row-key="id"
      :delete-title="deleteTitle"
      @edit="handleEdit"
      @delete="handleDelete"
      :action-config="actionConfig"
      :pagination="pagination"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          <span>{{ record.positionType && record.positionType.name || '-' }}</span>
        </template>
        <template v-if="column.key === 'level'">
          <span>{{ record.level && record.level.name || '-' }}</span>
        </template>
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'green' : 'red'">
            {{ record.status ? '启用' : '停用' }}
          </a-tag>
        </template>
      </template>
    </base-table>

    <!-- 添加/编辑岗位弹窗 -->
    <a-modal
      :title="modalType === 'add' ? '新增岗位' : '编辑岗位'"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="岗位类型及名称" name="positionCascader">
              <a-cascader
                v-model:value="formData.positionCascader"
                :options="cascaderOptions"
                placeholder="请选择岗位类型及名称"
                @change="handlePositionCascaderChange"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="岗位等级" name="levelId">
              <a-select
                v-model:value="formData.levelId"
                placeholder="请选择岗位等级"
                style="width: 100%"
              >
                <a-select-option
                  v-for="item in levelOptions"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name || item.level }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-switch v-model:checked="formData.status" />
            </a-form-item>
          </a-col>
        </a-row>
        
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  getPositionList,
  getPositionDetail,
  addPosition,
  updatePosition,
  deletePosition,
  getPositionNameOptions,
  getPositionTypeOptions,
  getLevelOptions
} from '@/api/organization/position';
import { SearchFormCard } from '@/components/SearchForm';

import BaseTable from '@/components/BaseTable';
import { useTablePagination } from '@/utils/common';

// 查询表单
const searchForm = reactive({
  name: '',
  typeId: undefined,
  levelId: undefined,
  status: undefined,
  pageNum: 1,
  pageSize: 10
});

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};  

// 删除确认标题
const deleteTitle = '确定删除吗?';


// 添加key值用于强制重新渲染搜索表单
const formKey = ref(0);

// 搜索表单配置
const formItems = computed(() => {
  return [
    {
      label: '岗位名称',
      field: 'name',
      type: 'select',
      placeholder: '请选择岗位名称',
      options: positionNames.value,
      selectLabel: 'name',
      selectValue: 'name'
    },
    {
      label: '岗位类型',
      field: 'typeId',
      type: 'select',
      placeholder: '请选择岗位类型',
      options: positionTypes.value,
      selectLabel: 'name',
      selectValue: 'id'
    },
    {
      label: '岗位等级',
      field: 'levelId',
      type: 'select',
      placeholder: '请选择岗位等级',
      options: levelOptions.value,
      selectLabel: 'name',
      selectValue: 'id'
    }
  ];
});


// 表格列定义
const columns = [
  {
    title: '岗位名称',
    dataIndex: 'positionName',
    key: 'name',
    customRender: ({ record }) => record.positionName && record.positionName.name || record.name || '-'
  },
  {
    title: '岗位类别',
    key: 'type',
    customRender: ({ record }) => record.positionType && record.positionType.name || '-'
  },
  {
    title: '岗位等级',
    key: 'level',
  },
  {
    title: '状态',
    key: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 180
  },
];

// 数据与状态
const loading = ref(false);
const positionList = ref([]);
const positionNames = ref([]);
const positionTypes = ref([]);
const levelOptions = ref([]);

// 模态框相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formData = reactive({
  id: null,
  positionCascader: [], // 级联选择器的值数组 [typeId, nameId]
  typeId: undefined,    // 岗位类型ID
  nameId: undefined,    // 岗位名称ID
  code: '',
  levelId: undefined,
  status: true,
  remark: '',
});

const formRules = {
  positionCascader: [
    { required: true, message: '请选择岗位类型及名称', trigger: 'change' }
  ],
  levelId: [
    { required: true, message: '请选择岗位等级', trigger: 'change' }
  ]
};

// 添加级联选择器选项计算属性
const cascaderOptions = computed(() => {
  return positionTypes.value.map(type => ({
    value: type.id,
    label: type.name,
    children: positionNames.value
      .filter(name => name.typeId === type.id)
      .map(name => ({
        value: name.id,
        label: name.name
      }))
  }));
});

// 添加级联选择器变更处理函数
const handlePositionCascaderChange = (value) => {
  if (value && value.length === 2) {
    formData.typeId = value[0];
    formData.nameId = value[1];
    
    // 生成岗位编码逻辑
    if (modalType.value === 'add') {
      const selectedType = positionTypes.value.find(type => type.id === value[0]);
      const selectedName = positionNames.value.find(name => name.id === value[1]);
      let prefix = '';
      
      if (selectedType && selectedName) {
        // 使用类型编码和名称首字母作为前缀
        prefix = selectedType.code || 'T';
        const firstChar = selectedName.name.charAt(0);
        prefix += firstChar;
      } else {
        prefix = 'P';
      }
      
      // 生成随机字符串作为后缀，确保唯一性
      const randomString = Math.random().toString(36).substring(2, 7).toUpperCase();
      formData.code = `${prefix}${randomString}`;
    }
  }
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: function() {
    fetchPositionList();
  },
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
  }
});

// 获取岗位列表
const fetchPositionList = async () => {
  loading.value = true;
  try {
    const { name, typeId, levelId, status } = searchForm;
    const params = {
      name,
      typeId,
      levelId,
      status,
      pageNum: pagination.current || 1,
      pageSize: pagination.pageSize || 10
    };

    const res = await getPositionList(params);
    if (res && res.rows) {
      positionList.value = res.rows || [];
      // 使用组合式函数的updatePagination方法更新分页信息
      updatePagination({
        total: res.total || 0,
        current: params.pageNum,
        pageSize: params.pageSize
      });
    } else {
      positionList.value = [];
      // 重置分页信息
      updatePagination({ total: 0, current: 1 });
      console.error('获取岗位列表数据异常：', res);
    }
  } catch (error) {
    console.error('获取岗位列表失败', error);
    message.error('获取岗位列表失败');
    positionList.value = [];
    // 发生错误时重置分页
    updatePagination({ total: 0, current: 1 });
  } finally {
    loading.value = false;
  }
};

// 获取岗位名称选项
const fetchPositionNameOptions = async () => {
  try {
    const res = await getPositionNameOptions();
    // 从PositionName表获取数据，不再使用字典
    positionNames.value = res.rows || [];
  } catch (error) {
    console.error('获取岗位名称选项失败', error);
    message.error('获取岗位名称选项失败');
    positionNames.value = [];
  }
};

// 获取岗位类别选项
const fetchPositionTypeOptions = async () => {
  try {
    const res = await getPositionTypeOptions();
    // 从PositionType表获取数据，不再使用字典
    positionTypes.value = res || [];
  } catch (error) {
    console.error('获取岗位类型选项失败', error);
    message.error('获取岗位类型选项失败');
    positionTypes.value = [];
  }
};

// 获取岗位等级选项
const fetchLevelOptions = async () => {
  try {
    const res = await getLevelOptions();
    levelOptions.value = res || [];
  } catch (error) {
    console.error('获取岗位等级选项失败', error);
    message.error('获取岗位等级选项失败');
    levelOptions.value = [];
  }
};

// 查询
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  // 重置到第一页
  resetPagination();
  fetchPositionList();
};

// 重置查询
const resetSearch = () => {
  searchForm.name = '';
  searchForm.typeId = undefined;
  searchForm.levelId = undefined;
  searchForm.status = undefined;
  // 重置到第一页
  resetPagination();
  fetchPositionList();
};

// 显示添加模态框
const showAddModal = () => {
  modalType.value = 'add';
  formData.id = null;
  formData.positionCascader = []; // 清空级联选择器
  formData.typeId = undefined;
  formData.nameId = undefined;
  formData.code = '';
  formData.levelId = undefined;
  formData.status = true;
  formData.remark = '';
  modalVisible.value = true;
};

// 编辑
const handleEdit = async (record) => {
  modalType.value = 'edit';
  modalVisible.value = true;
  modalLoading.value = true;
  
  try {
    const res = await getPositionDetail(record.id);
    const detail = res;
    
    // 填充表单数据
    formData.id = detail.id;
    formData.typeId = detail.typeId;
    formData.nameId = detail.nameId;
    formData.positionCascader = [detail.typeId, detail.nameId]; // 设置级联选择器的值
    formData.code = detail.code;
    formData.levelId = detail.levelId;
    formData.status = detail.status;
    formData.remark = detail.remark;
  } catch (error) {
    console.error('获取岗位详情失败', error);
    message.error('获取岗位详情失败');
  } finally {
    modalLoading.value = false;
  }
};

// 删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除岗位"${record.positionName && record.positionName.name || record.name}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true;
        const res = await deletePosition(record.id);
        message.success('删除成功');
        fetchPositionList();
      } catch (error) {
        console.error('删除失败', error);
        message.error('删除失败');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 确认添加/编辑
const handleOk = () => {
  formRef.value.validate().then(async () => {
    // 确保级联选择器的值已正确转换为typeId和nameId
    if (!formData.typeId || !formData.nameId) {
      message.error('请完整选择岗位类型及名称');
      return;
    }
    
    modalLoading.value = true;
    
    try {
      const submitData = {
        id: formData.id,
        typeId: formData.typeId,
        nameId: formData.nameId,
        code: formData.code,
        levelId: formData.levelId,
        status: formData.status,
        remark: formData.remark
      };
      
      if (modalType.value === 'add') {
        await addPosition(submitData);
        message.success('新增岗位成功');
      } else {
        await updatePosition(submitData);
        message.success('修改岗位成功');
      }
      
      modalVisible.value = false;
      fetchPositionList();
    } catch (error) {
      console.error(`${modalType.value === 'add' ? '新增' : '修改'}岗位失败`, error);
      message.error(`该岗位已存在，请检查后重新输入`);
    } finally {
      modalLoading.value = false;
    }
  });
};

// 取消添加/编辑
const handleCancel = () => {
  modalVisible.value = false;
};

onMounted(() => {
  Promise.all([
    fetchPositionNameOptions(),
    fetchPositionTypeOptions(),
    fetchLevelOptions()
  ]).then(() => {
    formKey.value += 1;
    // 加载完选项后，再加载表格数据
    fetchPositionList();
  }).catch(error => {
    console.error('数据加载出错:', error);
    message.error('初始化数据加载失败，请刷新页面重试');
    // 即使选项加载失败，也尝试加载表格数据
    fetchPositionList();
  });
});
</script>

<style scoped>
.page-container {
  width: 100%;
  height: 100%;
}

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}
</style>
