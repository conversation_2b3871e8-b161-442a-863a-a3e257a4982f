import { defineStore } from 'pinia'
import { 
  getUserList, 
  createUser, 
  updateUser, 
  deleteUser, 
  getRoleList,
  login,
  getCurrentUser,
  getUserMenuPermissions
} from '@/api/system/user'
import { setToken, setUserInfo, clearAuth, getUserInfo } from '@/utils/auth'
import { useMenuStore } from './menu'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: getUserInfo(), // 从localStorage初始化
    permissions: [],
    userMenus: [] // 保存用户可访问的菜单
  }),

  actions: {
    // 登录
    async login(loginData) {
      try {
        const response = await login(loginData)
        if (!response) {
          console.error('登录响应为空')
          return { success: false, error: '登录失败，响应为空' }
        }
        
        const { token, user } = response
        
        if (!token || !user) {
          console.error('登录响应数据不完整:', response)
          return { success: false, error: '登录失败，响应数据不完整' }
        }
        
        // 保存token和用户信息
        setToken(token)
        setUserInfo(user)
        
        // 更新状态
        this.userInfo = user
        
        // 从用户角色中提取权限
        this.permissions = []
        if (user.Roles && Array.isArray(user.Roles) && user.Roles.length > 0) {
          const allPermissions = []
          for (const role of user.Roles) {
            if (role.permissions && Array.isArray(role.permissions)) {
              allPermissions.push(...role.permissions)
            }
          }
          this.permissions = allPermissions
        }
        
        // 获取用户菜单
        try {
          await this.getUserMenus()
        } catch (menuError) {
          console.error('获取用户菜单失败:', menuError)
          // 不中断登录流程
        }
        
        return { success: true }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, error: error.message || '登录失败' }
      }
    },
    
    // 退出登录
    logout() {
      this.userInfo = null
      this.permissions = []
      this.userMenus = []
      clearAuth()
    },
    
    // 获取当前用户信息
    async getCurrentUserInfo() {
      try {
        const user = await getCurrentUser()
        if (!user) {
          throw new Error('用户信息为空')
        }
        
        setUserInfo(user)
        this.userInfo = user
        
        // 从用户角色中提取权限
        this.permissions = []
        if (user.Roles && Array.isArray(user.Roles) && user.Roles.length > 0) {
          const allPermissions = []
          for (const role of user.Roles) {
            if (role.permissions && Array.isArray(role.permissions)) {
              allPermissions.push(...role.permissions)
            }
          }
          this.permissions = allPermissions
        }
        
        // 获取用户菜单
        try {
          await this.getUserMenus()
        } catch (menuError) {
          console.error('获取用户菜单失败:', menuError)
          // 不中断获取用户信息流程
        }
        
        return { success: true }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { success: false, error: error.message || '获取用户信息失败' }
      }
    },

    // 获取用户列表
    async getUserList(params) {
      try {
        const response = await getUserList(params)
        return response.data || { list: [], total: 0 }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        return { list: [], total: 0 }
      }
    },

    // 创建用户
    async createUser(data) {
      try {
        const response = await createUser(data)
        return response.data
      } catch (error) {
        console.error('创建用户失败:', error)
        throw error
      }
    },

    // 更新用户
    async updateUser(data) {
      try {
        const response = await updateUser(data)
        return response.data
      } catch (error) {
        console.error('更新用户失败:', error)
        throw error
      }
    },

    // 删除用户
    async deleteUser(id) {
      try {
        const response = await deleteUser(id)
        return response.data
      } catch (error) {
        console.error('删除用户失败:', error)
        throw error
      }
    },

    // 获取角色选项列表
    async getRoleOptions() {
      try {
        const response = await getRoleList();
        
        if (!response || !response.data) {
          console.error('获取角色列表返回数据为空');
          return [];
        }
        
        const roles = Array.isArray(response.data) ? response.data : [];
        
        return roles;
      } catch (error) {
        console.error('获取角色列表失败:', error);
        return [];
      }
    },

    // 获取用户可访问的菜单
    async getUserMenus() {
      try {
        // 使用新的后端接口获取用户菜单和权限
        const response = await getUserMenuPermissions();
        
        // 检查响应是否有效
        if (!response) {
          console.error('获取用户菜单返回为空');
          this.userMenus = [];
          return [];
        }
        
        // 后端可能直接返回data，或者把data包在response里
        const responseData = response.data || response;
        
        if (!responseData) {
          console.error('无法解析菜单数据');
          this.userMenus = [];
          return [];
        }
        
        // 获取菜单数据，兼容多种数据结构
        const menus = responseData.menus || responseData.data?.menus || [];
        const permissions = responseData.permissions || responseData.data?.permissions || [];
        
        // 设置用户菜单
        if (Array.isArray(menus)) {
          this.userMenus = menus;
        } else {
          console.error('提取的菜单数据无效:', menus);
          this.userMenus = [];
        }
        
        // 设置用户权限
        if (Array.isArray(permissions)) {
          this.permissions = permissions;
        } else {
          this.permissions = [];
        }
        
        return this.userMenus;
      } catch (error) {
        console.error('获取用户菜单失败:', error);
        this.userMenus = [];
        return [];
      }
    }
  }
}) 