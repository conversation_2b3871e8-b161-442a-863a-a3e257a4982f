export const practiceStats = {
  daily: {
    title: '日练习统计',
    data: [
      { rank: 1, name: '李明', department: '前厅', duration: 120, count: 45 },
      { rank: 2, name: '王芳', department: '前厅', duration: 115, count: 43 },
      { rank: 3, name: '张伟', department: '后厨', duration: 110, count: 40 },
      { rank: 4, name: '赵丽', department: '前厅', duration: 105, count: 38 },
      { rank: 5, name: '刘强', department: '后厨', duration: 100, count: 36 },
      { rank: 6, name: '陈晓', department: '前厅', duration: 95, count: 34 },
      { rank: 7, name: '杨光', department: '后厨', duration: 90, count: 32 },
      { rank: 8, name: '周娜', department: '前厅', duration: 85, count: 30 },
      { rank: 9, name: '吴涛', department: '后厨', duration: 80, count: 28 },
      { rank: 10, name: '郑华', department: '前厅', duration: 75, count: 26 }
    ]
  },
  weekly: {
    title: '周练习统计',
    data: [
      { rank: 1, name: '张伟', department: '后厨', duration: 840, count: 280 },
      { rank: 2, name: '李明', department: '前厅', duration: 780, count: 260 },
      { rank: 3, name: '王芳', department: '前厅', duration: 720, count: 240 },
      { rank: 4, name: '赵丽', department: '前厅', duration: 660, count: 220 },
      { rank: 5, name: '刘强', department: '后厨', duration: 600, count: 200 },
      { rank: 6, name: '陈晓', department: '前厅', duration: 540, count: 180 },
      { rank: 7, name: '杨光', department: '后厨', duration: 480, count: 160 },
      { rank: 8, name: '周娜', department: '前厅', duration: 420, count: 140 },
      { rank: 9, name: '吴涛', department: '后厨', duration: 360, count: 120 },
      { rank: 10, name: '郑华', department: '前厅', duration: 300, count: 100 }
    ]
  },
  monthly: {
    title: '月练习统计',
    data: [
      { rank: 1, name: '王芳', department: '前厅', duration: 3200, count: 1100 },
      { rank: 2, name: '张伟', department: '后厨', duration: 3000, count: 1000 },
      { rank: 3, name: '李明', department: '前厅', duration: 2800, count: 950 },
      { rank: 4, name: '赵丽', department: '前厅', duration: 2600, count: 900 },
      { rank: 5, name: '刘强', department: '后厨', duration: 2400, count: 850 },
      { rank: 6, name: '陈晓', department: '前厅', duration: 2200, count: 800 },
      { rank: 7, name: '杨光', department: '后厨', duration: 2000, count: 750 },
      { rank: 8, name: '周娜', department: '前厅', duration: 1800, count: 700 },
      { rank: 9, name: '吴涛', department: '后厨', duration: 1600, count: 650 },
      { rank: 10, name: '郑华', department: '前厅', duration: 1400, count: 600 }
    ]
  },
  quarterly: {
    title: '季度练习统计',
    data: [
      { rank: 1, name: '赵丽', department: '前厅', duration: 9600, count: 3200 },
      { rank: 2, name: '王芳', department: '前厅', duration: 9000, count: 3000 },
      { rank: 3, name: '张伟', department: '后厨', duration: 8400, count: 2800 },
      { rank: 4, name: '李明', department: '前厅', duration: 7800, count: 2600 },
      { rank: 5, name: '刘强', department: '后厨', duration: 7200, count: 2400 },
      { rank: 6, name: '陈晓', department: '前厅', duration: 6600, count: 2200 },
      { rank: 7, name: '杨光', department: '后厨', duration: 6000, count: 2000 },
      { rank: 8, name: '周娜', department: '前厅', duration: 5400, count: 1800 },
      { rank: 9, name: '吴涛', department: '后厨', duration: 4800, count: 1600 },
      { rank: 10, name: '郑华', department: '前厅', duration: 4200, count: 1400 }
    ]
  },
  yearly: {
    title: '年度练习统计',
    data: [
      { rank: 1, name: '张伟', department: '后厨', duration: 38000, count: 12500 },
      { rank: 2, name: '赵丽', department: '前厅', duration: 35000, count: 11500 },
      { rank: 3, name: '王芳', department: '前厅', duration: 32000, count: 10500 },
      { rank: 4, name: '李明', department: '前厅', duration: 29000, count: 9500 },
      { rank: 5, name: '刘强', department: '后厨', duration: 26000, count: 8500 },
      { rank: 6, name: '陈晓', department: '前厅', duration: 23000, count: 7500 },
      { rank: 7, name: '杨光', department: '后厨', duration: 20000, count: 6500 },
      { rank: 8, name: '周娜', department: '前厅', duration: 17000, count: 5500 },
      { rank: 9, name: '吴涛', department: '后厨', duration: 14000, count: 4500 },
      { rank: 10, name: '郑华', department: '前厅', duration: 11000, count: 3500 }
    ]
  }
};

export const subjectStats = {
  title: '科目统计',
  categories: [
    { name: '服务礼仪', level: 'P1', avgDuration: 120, avgCount: 45, topUsers: [
      { name: '李明', duration: 180, count: 60 },
      { name: '王芳', duration: 160, count: 55 },
      { name: '张伟', duration: 140, count: 50 }
    ]},
    { name: '菜品知识', level: 'P2', avgDuration: 140, avgCount: 50, topUsers: [
      { name: '赵丽', duration: 200, count: 65 },
      { name: '刘强', duration: 180, count: 60 },
      { name: '陈晓', duration: 160, count: 55 }
    ]},
    { name: '烹饪技巧', level: 'P3', avgDuration: 160, avgCount: 55, topUsers: [
      { name: '杨光', duration: 220, count: 70 },
      { name: '周娜', duration: 200, count: 65 },
      { name: '吴涛', duration: 180, count: 60 }
    ]},
    { name: '酒水知识', level: 'P1', avgDuration: 100, avgCount: 40, topUsers: [
      { name: '郑华', duration: 150, count: 50 },
      { name: '李明', duration: 130, count: 45 },
      { name: '王芳', duration: 110, count: 40 }
    ]},
    { name: '卫生安全', level: 'P2', avgDuration: 130, avgCount: 45, topUsers: [
      { name: '张伟', duration: 190, count: 60 },
      { name: '赵丽', duration: 170, count: 55 },
      { name: '刘强', duration: 150, count: 50 }
    ]}
  ]
};

export const examStats = {
  monthly: {
    title: '月度考试统计',
    passCount: 245,
    totalCount: 300,
    passRate: 81.67,
    positionRates: [
      { position: '前厅经理', passRate: 95.0 },
      { position: '厨师长', passRate: 92.5 },
      { position: '服务员', passRate: 85.0 },
      { position: '厨师', passRate: 82.5 },
      { position: '收银员', passRate: 78.3 },
      { position: '迎宾员', passRate: 76.7 },
      { position: '后厨助理', passRate: 70.0 }
    ]
  },
  quarterly: {
    title: '季度考试统计',
    passCount: 720,
    totalCount: 900,
    passRate: 80.0,
    positionRates: [
      { position: '前厅经理', passRate: 93.3 },
      { position: '厨师长', passRate: 90.0 },
      { position: '服务员', passRate: 83.3 },
      { position: '厨师', passRate: 80.0 },
      { position: '收银员', passRate: 76.7 },
      { position: '迎宾员', passRate: 73.3 },
      { position: '后厨助理', passRate: 66.7 }
    ]
  },
  yearly: {
    title: '年度考试统计',
    passCount: 2800,
    totalCount: 3600,
    passRate: 77.8,
    positionRates: [
      { position: '前厅经理', passRate: 91.7 },
      { position: '厨师长', passRate: 88.9 },
      { position: '服务员', passRate: 80.6 },
      { position: '厨师', passRate: 77.8 },
      { position: '收银员', passRate: 75.0 },
      { position: '迎宾员', passRate: 72.2 },
      { position: '后厨助理', passRate: 63.9 }
    ]
  }
};

export const certificateStats = {
  title: '证书统计',
  typesDistribution: [
    { type: '前厅证书', count: 320, percentage: 40 },
    { type: '后厨证书', count: 280, percentage: 35 },
    { type: '服务类证书', count: 200, percentage: 25 }
  ],
  requiredCompletionRate: 85.5,
  pendingReviewCount: 48,
  expiringCount: 32,
  topHolders: [
    { position: '前厅经理', top5: [
      { name: '李明', count: 12 },
      { name: '王芳', count: 11 },
      { name: '赵丽', count: 10 },
      { name: '陈晓', count: 9 },
      { name: '周娜', count: 8 }
    ]},
    { position: '厨师长', top5: [
      { name: '张伟', count: 14 },
      { name: '刘强', count: 12 },
      { name: '杨光', count: 11 },
      { name: '吴涛', count: 10 },
      { name: '马超', count: 9 }
    ]},
    { position: '服务员', top5: [
      { name: '郑华', count: 8 },
      { name: '孙雪', count: 7 },
      { name: '林晴', count: 6 },
      { name: '黄梅', count: 5 },
      { name: '朱亮', count: 4 }
    ]},
    { position: '厨师', top5: [
      { name: '韩立', count: 9 },
      { name: '冯强', count: 8 },
      { name: '董明', count: 7 },
      { name: '谢雨', count: 6 },
      { name: '姜山', count: 5 }
    ]}
  ]
};

// 新增：证书排行榜数据
export const certificateRanking = [
  { name: '张伟', position: '厨师长', level: 'P4', count: 14, distanceToHighest: 0, progress: 100 },
  { name: '李明', position: '前厅经理', level: 'P4', count: 12, distanceToHighest: 0, progress: 92 },
  { name: '刘强', position: '厨师长', level: 'P3', count: 12, distanceToHighest: 1, progress: 85 },
  { name: '王芳', position: '前厅经理', level: 'P3', count: 11, distanceToHighest: 1, progress: 82 },
  { name: '杨光', position: '厨师长', level: 'P3', count: 11, distanceToHighest: 1, progress: 78 },
  { name: '赵丽', position: '前厅经理', level: 'P3', count: 10, distanceToHighest: 1, progress: 75 },
  { name: '吴涛', position: '厨师长', level: 'P2', count: 10, distanceToHighest: 2, progress: 72 },
  { name: '韩立', position: '厨师', level: 'P3', count: 9, distanceToHighest: 1, progress: 68 },
  { name: '陈晓', position: '前厅经理', level: 'P2', count: 9, distanceToHighest: 2, progress: 65 },
  { name: '马超', position: '厨师长', level: 'P2', count: 9, distanceToHighest: 2, progress: 62 }
];

// 新增：科目练习和考试情况统计数据
export const subjectExamStats = [
  {
    name: '服务礼仪',
    level: 'P1',
    avgDuration: 120,
    avgCount: 45,
    passRate: 88,
    topStudents: [
      { name: '李明', duration: 180, count: 60 },
      { name: '王芳', duration: 160, count: 55 },
      { name: '张伟', duration: 140, count: 50 }
    ]
  },
  {
    name: '菜品知识',
    level: 'P2',
    avgDuration: 140,
    avgCount: 50,
    passRate: 82,
    topStudents: [
      { name: '赵丽', duration: 200, count: 65 },
      { name: '刘强', duration: 180, count: 60 },
      { name: '陈晓', duration: 160, count: 55 }
    ]
  },
  {
    name: '烹饪技巧',
    level: 'P3',
    avgDuration: 160,
    avgCount: 55,
    passRate: 75,
    topStudents: [
      { name: '杨光', duration: 220, count: 70 },
      { name: '周娜', duration: 200, count: 65 },
      { name: '吴涛', duration: 180, count: 60 }
    ]
  },
  {
    name: '酒水知识',
    level: 'P1',
    avgDuration: 100,
    avgCount: 40,
    passRate: 90,
    topStudents: [
      { name: '郑华', duration: 150, count: 50 },
      { name: '李明', duration: 130, count: 45 },
      { name: '王芳', duration: 110, count: 40 }
    ]
  },
  {
    name: '卫生安全',
    level: 'P2',
    avgDuration: 130,
    avgCount: 45,
    passRate: 95,
    topStudents: [
      { name: '张伟', duration: 190, count: 60 },
      { name: '赵丽', duration: 170, count: 55 },
      { name: '刘强', duration: 150, count: 50 }
    ]
  },
  {
    name: '客户服务',
    level: 'P2',
    avgDuration: 125,
    avgCount: 42,
    passRate: 85,
    topStudents: [
      { name: '周娜', duration: 175, count: 58 },
      { name: '陈晓', duration: 165, count: 53 },
      { name: '王芳', duration: 155, count: 48 }
    ]
  },
  {
    name: '团队协作',
    level: 'P3',
    avgDuration: 150,
    avgCount: 48,
    passRate: 78,
    topStudents: [
      { name: '杨光', duration: 210, count: 67 },
      { name: '刘强', duration: 190, count: 62 },
      { name: '赵丽', duration: 180, count: 57 }
    ]
  }
]; 
