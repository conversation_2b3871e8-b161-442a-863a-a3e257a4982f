import { getSystemSettingByCode } from '@/api/system/setting';

// 缓存设置值
const settingCache = new Map();

/**
 * 获取系统设置值
 * @param {string} code - 系统设置代码
 * @param {*} defaultValue - 默认值，如果设置不存在则返回此值
 * @param {boolean} useCache - 是否使用缓存，默认为true
 * @returns {Promise<*>} - 返回设置值或默认值
 */
export async function getSettingValue(code, defaultValue = null, useCache = true) {
  try {
    if (!code) {
      return defaultValue;
    }

    // 如果启用缓存并且缓存中有值，则直接返回
    if (useCache && settingCache.has(code)) {
      return settingCache.get(code);
    }

    // 从服务器获取设置
    try {
      const response = await getSystemSettingByCode(code);
      // request.js已经处理了响应格式，直接使用返回的数据
      const value = response && response.value;
      
      // 缓存设置值
      if (useCache && value !== undefined) {
        settingCache.set(code, value);
      }
      
      return value !== undefined ? value : defaultValue;
    } catch (error) {
      return defaultValue;
    }
  } catch (error) {
    console.error(`获取系统设置 [${code}] 失败:`, error);
    return defaultValue;
  }
}

/**
 * 清除设置缓存
 * @param {string} code - 系统设置代码，不传则清除所有缓存
 */
export function clearSettingCache(code) {
  if (code) {
    settingCache.delete(code);
  } else {
    settingCache.clear();
  }
} 