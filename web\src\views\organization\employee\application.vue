<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
          v-model="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetQuery"
          @update:model-value="handleFormUpdate"
        />
      </template>
      <template #actions>
        <a-space :size="16">
          <a-button 
            type="primary" 
            class="primary-button" 
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchAudit('通过')"
            :loading="batchAuditLoading"
          >
            批量通过
          </a-button>
          <a-button 
            class="primary-button" 
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchAudit('驳回')"
            :loading="batchAuditLoading"
          >
            批量驳回
          </a-button>
        </a-space>
      </template>
    </page-header>

    <base-table
      :columns="columns"
      :data-source="applicationList"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      @change="handleTableChange"
      rowKey="id"
      :action-config="actionConfig"
      :show-default-action="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'auditStatus'">
          <a-tag :color="getStatusColor(record.auditStatus)">
            {{ record.auditStatus }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <div class="action-buttons">
            <a-button 
              type="link" 
              class="action-button" 
              @click="handleViewDetail(record)"
            >
              详情
            </a-button>
            <a-button 
              v-if="record.auditStatus === '审核中'"
              type="link" 
              class="action-button" 
              @click="handleAudit(record, '通过')"
            >
              通过
            </a-button>
            <a-button 
              v-if="record.auditStatus === '审核中'"
              type="link" 
              class="action-button" 
              @click="handleAudit(record, '驳回')"
            >
              驳回
            </a-button>
          </div>
        </template>
      </template>
    </base-table>

    <!-- 申请详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="申请详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentRecord" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="申请人姓名">
            {{ currentRecord.realName }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号">
            {{ currentRecord.phone }}
          </a-descriptions-item>
          <a-descriptions-item label="身份证号">
            {{ currentRecord.idCard }}
          </a-descriptions-item>
          <a-descriptions-item label="企业邀请码">
            {{ currentRecord.enterpriseInviteCode }}
          </a-descriptions-item>
          <a-descriptions-item label="申请部门">
            {{ currentRecord.departmentName }}
          </a-descriptions-item>
          <a-descriptions-item label="申请岗位">
            {{ currentRecord.positionName }}
          </a-descriptions-item>
          <a-descriptions-item label="岗位等级">
            {{ currentRecord.levelName }}
          </a-descriptions-item>
          <a-descriptions-item label="岗位类型">
            {{ currentRecord.positionTypeName }}
          </a-descriptions-item>
          <a-descriptions-item label="性别">
            {{ currentRecord.gender === 'male' ? '男' : currentRecord.gender === 'female' ? '女' : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="入职时间">
            {{ currentRecord.entryTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ formatTime(currentRecord.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="审核状态">
            <a-tag :color="getStatusColor(currentRecord.auditStatus)">
              {{ currentRecord.auditStatus }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="审核人" v-if="currentRecord.auditBy">
            {{ currentRecord.auditBy }}
          </a-descriptions-item>
          <a-descriptions-item label="审核时间" v-if="currentRecord.auditTime">
            {{ formatTime(currentRecord.auditTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="审核备注" v-if="currentRecord.auditRemark" :span="2">
            {{ currentRecord.auditRemark }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 单个审核弹窗 -->
    <a-modal
      v-model:visible="auditModalVisible"
      :title="`${auditType}申请`"
      @ok="handleAuditConfirm"
      @cancel="handleAuditCancel"
      :confirm-loading="auditLoading"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="申请人">
          {{ currentRecord?.realName }}
        </a-form-item>
        <a-form-item label="审核结果">
          <a-tag :color="auditType === '通过' ? 'success' : 'error'">
            {{ auditType }}
          </a-tag>
        </a-form-item>
        <a-form-item label="备注" :required="auditType === '驳回'">
          <a-textarea 
            v-model:value="auditRemark" 
            :placeholder="auditType === '驳回' ? '请输入驳回原因' : '请输入备注（可选）'"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量审核弹窗 -->
    <a-modal
      v-model:visible="batchAuditModalVisible"
      :title="`批量${batchAuditType}申请`"
      @ok="handleBatchAuditConfirm"
      @cancel="handleBatchAuditCancel"
      :confirm-loading="batchAuditLoading"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="选中申请">
          {{ selectedRowKeys.length }} 个申请
        </a-form-item>
        <a-form-item label="审核结果">
          <a-tag :color="batchAuditType === '通过' ? 'success' : 'error'">
            {{ batchAuditType }}
          </a-tag>
        </a-form-item>
        <a-form-item label="备注" :required="batchAuditType === '驳回'">
          <a-textarea 
            v-model:value="batchAuditRemark" 
            :placeholder="batchAuditType === '驳回' ? '请输入驳回原因' : '请输入备注（可选）'"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import SearchFormCard from '@/components/SearchForm/SearchFormCard.vue'
import PageHeader from '@/components/PageHeader/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import { 
  getEmployeeApplicationList, 
  getEmployeeApplicationDetail,
  auditEmployeeApplication,
  batchAuditEmployeeApplication
} from '@/api/organization/employee'
import { getUserInfo } from '@/utils/auth'

// 响应式数据
const loading = ref(false)
const applicationList = ref([])
const selectedRowKeys = ref([])
const detailModalVisible = ref(false)
const auditModalVisible = ref(false)
const batchAuditModalVisible = ref(false)
const auditLoading = ref(false)
const batchAuditLoading = ref(false)
const currentRecord = ref(null)
const auditType = ref('')
const batchAuditType = ref('')
const auditRemark = ref('')
const batchAuditRemark = ref('')

// 搜索表单
const searchForm = reactive({
  realName: '',
  phone: '',
  auditStatus: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条数据`
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record) => ({
    disabled: record.auditStatus !== '审核中'
  })
}))

// 操作按钮配置
const actionConfig = {
  detail: false,
  edit: false,
  delete: false
}

// 搜索表单配置
const searchFormItems = computed(() => [
  {
    type: 'input',
    label: '申请人姓名',
    field: 'realName',
    placeholder: '请输入申请人姓名'
  },
  {
    type: 'input',
    label: '手机号',
    field: 'phone',
    placeholder: '请输入手机号'
  },
  {
    type: 'select',
    label: '审核状态',
    field: 'auditStatus',
    placeholder: '请选择审核状态',
    options: [
      { label: '审核中', value: '审核中' },
      { label: '通过', value: '通过' },
      { label: '驳回', value: '驳回' }
    ]
  }
])

// 表格列配置
const columns = [
  {
    title: '申请人姓名',
    dataIndex: 'realName',
    key: 'realName',
    width: 120
  },
  {
    title: '手机号',
    dataIndex: 'phoneMasked',
    key: 'phoneMasked',
    width: 140
  },
  {
    title: '身份证号',
    dataIndex: 'idCardMasked',
    key: 'idCardMasked',
    width: 180
  },
  {
    title: '企业邀请码',
    dataIndex: 'enterpriseInviteCode',
    key: 'enterpriseInviteCode',
    width: 120
  },
  {
    title: '申请部门',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 120
  },
  {
    title: '申请岗位',
    dataIndex: 'positionName',
    key: 'positionName',
    width: 120
  },
  {
    title: '岗位等级',
    dataIndex: 'levelName',
    key: 'levelName',
    width: 100
  },
  {
    title: '岗位类型',
    dataIndex: 'positionTypeName',
    key: 'positionTypeName',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
    customRender: ({ text }) => formatTime(text)
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    key: 'auditStatus',
    width: 100
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    key: 'auditTime',
    width: 160,
    customRender: ({ text }) => text ? formatTime(text) : '-'
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 200
  }
]

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case '审核中':
      return 'processing'
    case '通过':
      return 'success'
    case '驳回':
      return 'error'
    default:
      return 'default'
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

// 获取申请列表
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    console.log('查询参数:', params)
    const response = await getEmployeeApplicationList(params)
    if (response) {
      applicationList.value = response.rows
      pagination.total = response.total
    }
  } catch (error) {
    message.error('获取申请列表失败')
  } finally {
    loading.value = false
  }
}

// 处理表单数据更新
const handleFormUpdate = (values) => {
  Object.assign(searchForm, values)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  getList()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getList()
}

// 查看详情
const handleViewDetail = async (record) => {
  try {
    const response = await getEmployeeApplicationDetail(record.id)
    if (response) {
      currentRecord.value = response
      detailModalVisible.value = true
    }
  } catch (error) {
    message.error('获取申请详情失败')
  }
}

// 单个审核
const handleAudit = (record, type) => {
  currentRecord.value = record
  auditType.value = type
  auditRemark.value = ''
  auditModalVisible.value = true
}

// 确认单个审核
const handleAuditConfirm = async () => {
  if (auditType.value === '驳回' && !auditRemark.value.trim()) {
    message.error('驳回时必须填写原因')
    return
  }
  
  if (!currentRecord.value || !currentRecord.value.id) {
    message.error('申请记录ID不存在')
    return
  }
  
  // 获取当前登录用户信息
  const currentUser = getUserInfo()
  if (!currentUser || !currentUser.id) {
    message.error('获取当前用户信息失败，请重新登录')
    return
  }
  
  auditLoading.value = true
  try {
    const data = {
      id: currentRecord.value.id,
      auditStatus: auditType.value,
      auditRemark: auditRemark.value,
      currentUser: {
        id: currentUser.id,
        username: currentUser.username || currentUser.nickname || 'admin'
      }
    }
    console.log('审核参数:', { id: currentRecord.value.id, data })
    const response = await auditEmployeeApplication(currentRecord.value.id, data)
    if (response) {
      message.success(`${auditType.value}成功`)
      auditModalVisible.value = false
      getList()
    }
  } catch (error) {
    message.error(`${auditType.value}失败`)
  } finally {
    auditLoading.value = false
  }
}

// 取消单个审核
const handleAuditCancel = () => {
  auditModalVisible.value = false
  auditRemark.value = ''
}

// 批量审核
const handleBatchAudit = (type) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审核的申请')
    return
  }
  batchAuditType.value = type
  batchAuditRemark.value = ''
  batchAuditModalVisible.value = true
}

// 确认批量审核
const handleBatchAuditConfirm = async () => {
  if (batchAuditType.value === '驳回' && !batchAuditRemark.value.trim()) {
    message.error('驳回时必须填写原因')
    return
  }
  
  if (selectedRowKeys.value.length === 0) {
    message.error('请选择要审核的申请')
    return
  }
  
  // 获取当前登录用户信息
  const currentUser = getUserInfo()
  if (!currentUser || !currentUser.id) {
    message.error('获取当前用户信息失败，请重新登录')
    return
  }
  
  batchAuditLoading.value = true
  try {
    const data = {
      ids: selectedRowKeys.value,
      auditStatus: batchAuditType.value,
      auditRemark: batchAuditRemark.value,
      currentUser: {
        id: currentUser.id,
        username: currentUser.username || currentUser.nickname || 'admin'
      }
    }
    const response = await batchAuditEmployeeApplication(data)
    if (response) {
      message.success(`批量${batchAuditType.value}成功`)
      batchAuditModalVisible.value = false
      selectedRowKeys.value = []
      getList()
    }
  } catch (error) {
    message.error(`批量${batchAuditType.value}失败`)
  } finally {
    batchAuditLoading.value = false
  }
}

// 取消批量审核
const handleBatchAuditCancel = () => {
  batchAuditModalVisible.value = false
  batchAuditRemark.value = ''
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-button {
  padding: 0;
  height: auto;
  color: #a18cd1;
  
  &:hover {
    color: #8a70c8;
  }
}

.detail-content {
  .ant-descriptions-item-label {
    font-weight: 500;
  }
}

.primary-button {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border: none;
  color: white;
  font-weight: 500;
  
  &:hover,
  &:focus {
    background: linear-gradient(135deg, #8a70c8 0%, #f8a5d8 100%);
    border: none;
    color: white;
  }
  
  &:disabled {
    background: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
  }
}
</style> 