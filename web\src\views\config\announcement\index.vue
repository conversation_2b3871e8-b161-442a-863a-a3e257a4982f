<template>
  <div class="page-config-container">
    <page-header >
    <template #search>
        <!-- 查询表单（优化后只保留SearchFormCard） -->
      <search-form-card
        :model-value="searchForm"
        :items="searchItems"
        @search="handleSearch"
        @reset="handleReset"
      />
    </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增公告
        </a-button>
      </template>
    </page-header>



    <!-- 列表区域 -->
      <base-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
        @change="handleTableChange"
        @edit="handleEdit"
        @delete="handleDelete"
        :action-config="actionConfig"
        :delete-title="deleteTitle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'default'">
              {{ record.status ? '已发布' : '未发布' }}
            </a-tag>
          </template>
          <!-- 内容 -->
          <template v-if="column.key === 'content'">
            <!-- <a-tooltip>
            <template #title>
            <div v-html="record.content" style="max-height: 60px; overflow: hidden; text-overflow: ellipsis;"></div>
          </template> -->
            <div v-html="record.content" style="max-height: 60px; overflow: hidden; text-overflow: ellipsis;"></div>
          <!-- </a-tooltip> -->
          </template>
        </template>
      </base-table>

    <!-- 新增/编辑公告弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="公告标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入公告标题" />
        </a-form-item>

        <a-form-item label="公告内容" name="content">
          <div class="rich-editor-wrapper">
            <QuillEditor
              :key="editorKey"
              v-model:content="formData.content"
              content-type="html"
              theme="snow"
              placeholder="请输入公告内容"
              :options="editorOptions"
            />
          </div>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="发布状态" name="status">
              <a-radio-group v-model:value="formData.status">
                <a-radio :value="true">已发布</a-radio>
                <a-radio :value="false">未发布</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" name="sort">
              <a-input-number
                v-model:value="formData.sort"
                :min="0"
                :max="999"
                placeholder="数字越小越靠前"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

import {
  getAnnouncementList,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
} from '@/api/announcement';
import dayjs from 'dayjs';
import { useTablePagination } from '@/utils/common';
import { SearchFormCard } from '@/components/SearchForm';

const deleteTitle="确定删除当前公告？"
const actionConfig={
  edit:true,
  delete:true
}

// 富文本编辑器配置
const editorOptions = {
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image']
    ]
  },
  placeholder: '请输入公告内容...',
  theme: 'snow'
}

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '内容',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
  },
  {
    title: '发布状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 180
  }
];

// 数据与状态
const loading = ref(false);
const dataSource = ref([]);

// 查询表单
const searchForm = reactive({
  title: '',
  status: undefined
});

// 获取公告列表函数
const fetchTableData = async () => {
  loading.value = true;

  // 使用setTimeout让UI先更新loading状态，避免界面阻塞
  setTimeout(async () => {
    try {
      const params = {
        ...searchForm,
        pageNum: pagination.current,
        pageSize: pagination.pageSize
      };
      const response = await getAnnouncementList(params);

      // 数据比较，避免不必要的更新
      const newData = response.list || [];
      if (JSON.stringify(dataSource.value) !== JSON.stringify(newData)) {
        dataSource.value = newData;
      }

      // 更新分页信息
      updatePagination({
        total: response.total || 0,
        current: response.page,
        pageSize: response.pageSize
      });
    } catch (error) {
      console.error('获取公告列表失败:', error);
      message.error('获取公告列表失败');
      dataSource.value = [];
      updatePagination({ total: 0, current: 1 });
    } finally {
      loading.value = false;
    }
  }, 0);
};

// 使用表格分页组合式函数
const {
  pagination,
  handleTableChange,
  updatePagination,
  resetPagination
} = useTablePagination({
  fetchData: fetchTableData,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 弹窗相关
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalTitle = computed(() => formData.id ? '编辑公告' : '新增公告');
const formRef = ref(null);
const editorKey = ref(0); // 用于强制重新渲染富文本编辑器
const formData = reactive({
  id: null,
  title: '',
  content: '',
  status: true,
  sort: 0,
  createTime: null,
  updateTime: null
});

// 表单校验规则
const formRules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { max: 5000, message: '内容长度不能超过5000个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择发布状态', trigger: 'change' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' }
  ]
};

// 搜索表单项配置
const searchItems = [
  {
    label: '公告标题',
    field: 'title',
    type: 'input',
    placeholder: '请输入公告标题'
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    options: [
      { label: '已发布', value: 1 },
      { label: '未发布', value: 0 }
    ],
    placeholder: '请选择状态'
  }
];

// 重置表单数据
const resetFormData = () => {
  formData.id = null;
  formData.title = '';
  formData.content = '';
  formData.status = true;
  formData.sort = 0;
  // 更新编辑器key，强制重新渲染富文本编辑器
  editorKey.value += 1;
};

// 新增公告
const handleAdd = () => {
  resetFormData();
  // 使用 nextTick 确保 DOM 更新后再显示弹窗
  nextTick(() => {
    modalVisible.value = true;
  });
};

// 编辑公告
const handleEdit = (record) => {
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = Boolean(record[key]);
    } else {
      formData[key] = record[key];
    }
  });
  modalVisible.value = true;
};

// 删除公告
const handleDelete = async (record) => {
  try {
    await deleteAnnouncement(record.id);
    message.success('删除成功');
    fetchTableData();
  } catch (error) {
    console.error('删除公告失败:', error);
    message.error('删除公告失败');
  }
};

// 弹窗确认
const handleOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;

    const data = {
      title: formData.title,
      content: formData.content,
      status: formData.status,
      sort: formData.sort
    };

    if (formData.id) {
      await updateAnnouncement(formData.id, data);
      message.success('编辑成功');
    } else {
      await createAnnouncement(data);
      message.success('新增成功');
    }

    modalVisible.value = false;
    resetFormData(); // 保存成功后重置表单
    fetchTableData();
  } catch (error) {
    console.error('保存公告失败:', error);
    message.error('保存公告失败');
  } finally {
    modalLoading.value = false;
  }
};

// 弹窗取消
const handleCancel = () => {
  modalVisible.value = false;
  // 取消时也重置表单，避免下次打开时显示之前的内容
  resetFormData();
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 搜索处理
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  resetPagination();
  fetchTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.title = '';
  searchForm.status = undefined;
  resetPagination();
  fetchTableData();
};

// 初始化
onMounted(() => {
  fetchTableData();
});
</script>

<style scoped>

.page-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  h1 {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.45);
  }
}

.rich-editor-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.rich-editor-wrapper :deep(.ql-editor) {
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
}

.rich-editor-wrapper :deep(.ql-toolbar) {
  border-bottom: 1px solid #d9d9d9;
}

.rich-editor-wrapper :deep(.ql-container) {
  border: none;
}

</style>