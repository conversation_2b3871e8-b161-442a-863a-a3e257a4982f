<template>
  <div class="profile-container">
    <a-card title="个人资料" :bordered="false">
      <a-tabs default-active-key="basic">
        <a-tab-pane key="basic" tab="基本资料">
          <a-form 
            :model="userForm" 
            :label-col="{ span: 4 }" 
            :wrapper-col="{ span: 16 }"
            class="profile-form"
          >
            <a-form-item label="用户名">
              <a-input v-model:value="userForm.username" disabled />
            </a-form-item>
            <a-form-item label="昵称">
              <a-input v-model:value="userForm.nickname" placeholder="请输入昵称" />
            </a-form-item>
            <a-form-item label="邮箱">
              <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
            </a-form-item>
            <a-form-item label="手机号">
              <a-input v-model:value="userForm.phone" placeholder="请输入手机号" />
            </a-form-item>
            <a-form-item label="真实姓名">
              <a-input v-model:value="userForm.realName" placeholder="请输入真实姓名" />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
              <a-button type="primary" @click="saveUserInfo">保存</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        
        <a-tab-pane key="password" tab="修改密码">
          <a-form 
            :model="passwordForm" 
            :label-col="{ span: 4 }" 
            :wrapper-col="{ span: 16 }"
            class="profile-form"
          >
            <a-form-item label="旧密码" name="oldPassword" :rules="[{ required: true, message: '请输入旧密码' }]">
              <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入旧密码" />
            </a-form-item>
            <a-form-item label="新密码" name="newPassword" :rules="[{ required: true, message: '请输入新密码' }]">
              <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
            </a-form-item>
            <a-form-item label="确认密码" name="confirmPassword" :rules="[
              { required: true, message: '请确认新密码' },
              { validator: validateConfirmPassword, trigger: 'change' }
            ]">
              <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请确认新密码" />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
              <a-button type="primary" @click="updatePassword">更新密码</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { getUserInfo, setUserInfo } from '@/utils/auth';
import { updateUserPassword, updateUserProfile } from '@/api/system/user';

// 用户基本信息表单数据
const userForm = reactive({
  id: '',
  username: '',
  nickname: '',
  email: '',
  phone: '',
  realName: ''
});

// 修改密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 校验确认密码
const validateConfirmPassword = async (rule, value) => {
  if (value !== passwordForm.newPassword) {
    return Promise.reject('两次输入的密码不一致');
  }
  return Promise.resolve();
};

// 初始化加载用户信息
onMounted(() => {
  const userInfo = getUserInfo();
  if (userInfo) {
    userForm.id = userInfo.id;
    userForm.username = userInfo.username;
    userForm.nickname = userInfo.nickname || '';
    userForm.email = userInfo.email || '';
    userForm.phone = userInfo.phone || '';
    userForm.realName = userInfo.realName || '';
  }
});

// 保存用户信息
const saveUserInfo = async () => {
  try {
    const result = await updateUserProfile(userForm);

      message.success('个人信息更新成功');
      
      // 更新本地存储的用户信息
      const userInfo = getUserInfo();
      const updatedUserInfo = {
        ...userInfo,
        nickname: userForm.nickname,
        email: userForm.email,
        phone: userForm.phone,
        realName: userForm.realName
      };
      setUserInfo(updatedUserInfo);

  } catch (error) {
    console.error('保存个人信息失败:', error);
    const errorMsg = String(error).replace('AxiosError: ', '');
    message.error(errorMsg || '保存失败，请稍后重试');
  }
};

// 修改密码
const updatePassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    message.error('两次输入的密码不一致');
    return;
  }
  
  try {
    const result = await updateUserPassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    });
    

      message.success('密码修改成功');
      // 清空表单
      passwordForm.oldPassword = '';
      passwordForm.newPassword = '';
      passwordForm.confirmPassword = '';
  } catch (error) {
    console.error('密码修改失败:', error);
    const errorMsg = String(error).replace('AxiosError: ', '');
    message.error(errorMsg || '密码修改失败，请稍后重试');
  }
};
</script>

<style scoped>
.profile-container {
  padding: 24px;
}

.profile-form {
  max-width: 650px;
  margin: 0 auto;
  padding-top: 20px;
}
</style> 