import request from '@/utils/request'

/**
 * 获取员工统计数据
 * @param {Object} params 查询参数
 * @param {string} params.name 员工姓名
 * @param {number} params.position 岗位ID
 * @param {number} params.level 岗位等级ID
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页大小
 * @returns {Promise} 返回统计数据
 */
export function getEmployeeStatistics(params) {
  return request({
    url: '/organization/employee/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取员工职位统计数据
 * @param {number} employeeId 员工ID
 * @returns {Promise} 返回职位统计数据
 */
export function getEmployeePositionStatistics(employeeId) {
  return request({
    url: `/organization/employee/statistics/${employeeId}/positions`,
    method: 'get'
  })
}

/**
 * 导出员工统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回导出数据
 */
export function exportEmployeeStatistics(params) {
  return request({
    url: '/organization/employee/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 