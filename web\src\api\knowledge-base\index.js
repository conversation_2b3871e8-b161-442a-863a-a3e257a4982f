import request from '@/utils/request';

/**
 * 获取知识库列表
 * @param {Object} params 请求参数
 * @param {string} params.searchText 搜索文本
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页条数
 * @param {string} params.fileCategory 文件归属筛选
 * @param {string} params.position 所属岗位名称筛选
 * @returns {Promise<Object>} 返回一个包含list、total、page、pageSize的分页数据对象
 */
export function getKnowledgeBaseList(params) {
  return request({
    url: '/knowledge-base/list',
    method: 'get',
    params
  });
}

// 上传单个文件
export function uploadFile(data) {
  return request({
    url: '/knowledge-base/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 批量上传文件
export function batchUploadFiles(data) {
  return request({
    url: '/knowledge-base/batch-upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 保存知识库文档
export function saveKnowledgeDocument(data) {
  return request({
    url: '/knowledge-base/save',
    method: 'post',
    data
  });
}

// 批量保存知识库文档
export function batchSaveDocuments(data) {
  return request({
    url: '/knowledge-base/batch-save',
    method: 'post',
    data
  });
}

// 获取知识库文档详情
export function getKnowledgeDetail(id) {
  return request({
    url: `/knowledge-base/detail/${id}`,
    method: 'get'
  });
}

// 更新知识库文档
export function updateKnowledgeDocument(id, data) {
  return request({
    url: `/knowledge-base/${id}`,
    method: 'put',
    data
  });
}

// 删除知识库文档
export function deleteKnowledgeDocument(id) {
  return request({
    url: `/knowledge-base/${id}`,
    method: 'delete'
  });
}

// 下载知识库文档
export function downloadKnowledgeFile(id) {
  return request({
    url: `/knowledge-base/download/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}

// 切换文档状态(启用/禁用)
export function toggleDocumentStatus(id, status) {
  return request({
    url: `/knowledge-base/${id}/status`,
    method: 'patch',
    data: { status }
  });
}

// 获取文档处理状态
export function getDocumentProcessStatus(id) {
  return request({
    url: `/knowledge-base/${id}/process-status`,
    method: 'get',
    silent: true
  });
}

// 获取文档的出题策略提示词
export function getPromptStrategy(id) {
  return request({
    url: `/knowledge-base/${id}/prompt-strategy`,
    method: 'get'
  });
}

// 保存文档的出题策略提示词并生成新题目
export function savePromptStrategy(id, data) {
  return request({
    url: `/knowledge-base/${id}/prompt-strategy`,
    method: 'post',
    data
  });
} 