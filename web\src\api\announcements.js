import { announcements } from '@/mock/announcements';
import { cloneDeep } from 'lodash-es';

// 获取公告列表
export function getAnnouncementList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: cloneDeep(announcements),
        message: '获取成功'
      });
    }, 300);
  });
}

// 添加公告
export function addAnnouncement(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = Math.max(...announcements.map(item => item.id), 0) + 1;
      const now = new Date().toISOString();
      const newAnnouncement = {
        ...data,
        id: newId,
        createTime: now,
        updateTime: now
      };
      
      announcements.push(newAnnouncement);
      
      resolve({
        code: 200,
        data: newAnnouncement,
        message: '添加成功'
      });
    }, 300);
  });
}

// 更新公告
export function updateAnnouncement(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = announcements.findIndex(item => item.id === data.id);
      
      if (index !== -1) {
        const now = new Date().toISOString();
        const updatedAnnouncement = {
          ...announcements[index],
          ...data,
          updateTime: now
        };
        
        announcements[index] = updatedAnnouncement;
        
        resolve({
          code: 200,
          data: updatedAnnouncement,
          message: '更新成功'
        });
      } else {
        resolve({
          code: 404,
          message: '公告不存在'
        });
      }
    }, 300);
  });
}

// 删除公告
export function deleteAnnouncement(id) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = announcements.findIndex(item => item.id === id);
      
      if (index !== -1) {
        announcements.splice(index, 1);
        
        resolve({
          code: 200,
          message: '删除成功'
        });
      } else {
        resolve({
          code: 404,
          message: '公告不存在'
        });
      }
    }, 300);
  });
}

// 获取单个公告详情
export function getAnnouncementDetail(id) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const announcement = announcements.find(item => item.id === id);
      
      if (announcement) {
        resolve({
          code: 200,
          data: cloneDeep(announcement),
          message: '获取成功'
        });
      } else {
        resolve({
          code: 404,
          message: '公告不存在'
        });
      }
    }, 300);
  });
}

// 更新公告状态
export function updateAnnouncementStatus(id, status) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = announcements.findIndex(item => item.id === id);
      
      if (index !== -1) {
        const now = new Date().toISOString();
        announcements[index].status = status;
        announcements[index].updateTime = now;
        
        resolve({
          code: 200,
          data: cloneDeep(announcements[index]),
          message: '状态更新成功'
        });
      } else {
        resolve({
          code: 404,
          message: '公告不存在'
        });
      }
    }, 300);
  });
} 