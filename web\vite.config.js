import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  // 将逗号分隔的字符串转换为数组
  const allowedHostsFromEnv = env.VITE_ALLOWED_HOSTS
    ? env.VITE_ALLOWED_HOSTS.split(',').map(host => host.trim())
    : []

  // 默认允许的主机列表
  const defaultAllowedHosts = [
    'localhost',
    '127.0.0.1',
    '************'
  ]

  return {
    plugins: [
      vue(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      allowedHosts: [
        ...allowedHostsFromEnv,
        ...defaultAllowedHosts
      ],
      // hmr: {
      //   host: '0.0.0.0',
      //   port: 443,
      //   clientPort: 443
      // },
      watch: {
        usePolling: true
      },
      fs: {
        strict: false
      },
      https: false,
      strictPort: false,
      cors: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
      },
      proxy: {
        '/api': {
          target: env.VITE_APP_API_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia']
    },
    build: {
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          }
        }
      }
    },
    // css: {
    //   loaderOptions: {
    //     less: {
    //       lessOptions: {
    //         modifyVars: {
    //           'primary-color': '#a18cd1',
    //           'primary-hover-color': '#b6a6d8',
    //           'secondary-color': '#fbc2eb'
    //         },
    //         javascriptEnabled: true
    //       }
    //     }
    //   }
    // }
  }
})
