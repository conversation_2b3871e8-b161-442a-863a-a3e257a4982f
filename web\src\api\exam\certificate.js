import request from '@/utils/request';

// 获取证书列表
export function getCertificateList(params) {
  return request({
    url: '/certificate/list',
    method: 'get',
    params
  });
}

// 获取证书详情
export function getCertificateDetail(id) {
  return request({
    url: `/certificate/detail/${id}`,
    method: 'get'
  });
}

// 下载证书
export function downloadCertificate(id) {
  return request({
    url: `/certificate/download/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}

// 创建证书记录
export function createCertificate(data) {
  return request({
    url: '/certificate/create',
    method: 'post',
    data
  });
}

// 更新证书记录
export function updateCertificate(id, data) {
  return request({
    url: `/certificate/update/${id}`,
    method: 'put',
    data
  });
}

// 删除证书记录
export function deleteCertificate(id) {
  return request({
    url: `/certificate/delete/${id}`,
    method: 'delete'
  });
}