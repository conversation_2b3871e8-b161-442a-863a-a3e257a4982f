<template>
<!-- <div class="certificate-preview">
	<div class="certificate-card" ref="certificateRef">
	  <div class="certificate-header">
		<div class="certificate-logo">
		  <img src="@/assets/logo.svg" alt="Logo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2ExOGNkMSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyNCIgZmlsbD0iI2ZmZiI+TE9HTzwvdGV4dD48L3N2Zz4='" />
		</div>
		<div class="certificate-title">
		  <h2>餐烤餐考</h2>
		  <h3>{{ certificate?.certificateName }}</h3>
		</div>
	  </div>

	  <div class="certificate-content">
		<div class="certificate-text">
		  兹证明 <span class="highlight">{{ certificate?.employeeName }}</span>
		  在岗位 <span class="highlight">{{ certificate?.positionNameInfo?.name }}（{{ certificate?.positionLevelInfo?.name }}）</span>
		  中表现优秀，通过所有相关考核，特颁发此证书。
		</div>

		<div class="certificate-info">
		  <div class="info-item">
			<div class="info-label">证书编号：</div>
			<div class="info-value">{{ certificate?.certificateNo }}</div>
		  </div>
		  <div class="info-item">
			<div class="info-label">颁发日期：</div>
			<div class="info-value">{{ formatDate(certificate?.obtainTime) }}</div>
		  </div>
		  <div class="info-item">
			<div class="info-label">有效期至：</div>
			<div class="info-value">{{ certificate?.validUntil ? formatDate(certificate.validUntil) : getExpiryDate(certificate?.obtainTime) }}</div>
		  </div>
		</div>
	  </div>

	  <div class="certificate-footer">
		<div class="certificate-seal">
		  <div class="seal-image"></div>
		</div>
		<div class="certificate-signature">
		  <div class="signature-image"></div>
		  <div class="signature-name">餐烤餐考企业负责人</div>
		</div>
	  </div>
	</div>

  </div> -->
  <div class="cert-detail">
  <div class=" theme-gold">
    <div class="border-frame"></div>
    <div class="corner-decoration top-left"></div>
    <div class="corner-decoration top-right"></div>
    <div class="corner-decoration bottom-left"></div>
    <div class="corner-decoration bottom-right"></div>
    
    <div class="header">
      <!-- <image src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%239C6FE4'/><text x='50' y='65' fill='white' text-anchor='middle' font-size='40'>认证</text></svg>" class="logo"></image> -->
      <div class="title">餐考认证</div>
      <div class="subtitle">CERTIFICATION</div>
      <div class="course-name">《{{certificate?.certificateName}}》</div>
      <div class="level">{{certificate?.positionNameInfo?.name}}（{{certificate?.positionLevelInfo?.name}}级）</div>
    </div>

    <div class="decorative-line"></div>

    <div class="content">
      <div class="recipient-name">{{certificate?.employeeName}}</div>
      <div class="certificate-text">
        <text>已完成《{{certificate?.certificateName}}》课程学习，<br>考核成绩合格，特发此证。</text>
      </div>
        </div>
        
    <div class="decorative-line"></div>

    <div class="footer">
      <div class="issuer">
        <div class="issuer-info">
          <div class="issuer-name">{{certificate?.organization || '餐烤餐考培训中心'}}</div>
          <div class="date-range">
            <div class="date-item">
              <text class="date-label">颁发日期：</text>
              <text>{{formatDate(certificate?.obtainTime)}}</text>
          </div>
            <div class="date-item">
              <text class="date-label">有效期至：</text>
              <text>{{formatDate(certificate?.validUntil)}}</text>
          </div>
          </div>
        </div>
        <div class="seal"></div>
        <div class="certificate-number">证书编号：{{certificate?.certificateNo }}</div>
      </div>
    </div>

    <div class="watermark">认证证书</div>
  </div>
  
</div> 

</template>

<script setup >
import dayjs from 'dayjs';
const props = defineProps({
  certificate: {
    type: Object,
    required: true
  }
})
// 格式化日期
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-';
};
// 获取证书有效期（默认一年）
const getExpiryDate = (obtainTime) => {
  return obtainTime ? dayjs(obtainTime).add(1, 'year').format('YYYY-MM-DD') : '-';
};
</script>

<style lang="scss" scoped>

.cert-detail {
  width: 100%;
}

.theme-gold{
  width:100%;
  padding:50px 50px;
  box-sizing: border-box;
  position: relative;
}

.border-frame {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  border: 2px solid #D4AF37;
  pointer-events: none;
  border-radius: 12px;
}

.border-frame::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 1px solid #C0A062;
  border-radius: 14px;
}

.corner-decoration {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #D4AF37;
}

.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.title {
  font-size: 28px;
  color: #1B3F8F;
  font-weight:bold;
  margin-bottom: 15px;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 24px;
  color: #D4AF37;
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.level {
  font-size: 18px;
  color: #1B3F8F;
  margin: 5px 0;
  font-weight: 500;
  padding: 4px 15px;
  border-radius: 20px;
  display: inline-block;
  line-height: 1.4;
}

.course-name {
  font-size: 20px;
  color: #1B3F8F;
  margin-top: 15px;
  font-weight:bold;
  padding: 0 20px;
  line-height: 1.4;
}

.content {
  text-align: center;
  margin: 30px 0;
  line-height: 2;
  color:#1B3F8F;
}

.recipient-name {
  font-size: 20px;
  margin: 20px 0;
  font-weight: bold;
}

.certificate-text {
  margin: 20px auto;
  padding: 0 10px;
  text-align: center;
}

.certificate-text text {
  text-align: center;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-top: 40px;
  position: relative;
  padding: 0 15px;
}

.issuer {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  width: 100%;
  position: relative;
  padding-bottom: 30px;
}

.issuer-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.issuer-name {
  font-size: 22px;
  color: #1B3F8F;
  font-weight:bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #C0A062;
  margin-bottom: 5px;
}

.date-range {
  font-size: 14px;
  color: #D4AF37;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-label {
  color: #D4AF37;
  min-width: 75px;
}

.seal {
  width: 90px;
  height: 90px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="%239C6FE4" stroke-width="2"/><circle cx="50" cy="50" r="44" fill="none" stroke="%239C6FE4" stroke-width="2"/><path d="M50 20l4 12h12l-10 8 4 12-10-8-10 8 4-12-10-8h12z" fill="%239C6FE4"/></svg>') no-repeat center;
  opacity: 0.85;
  align-self: flex-end;
}

.certificate-number {
  position: absolute;
  bottom: 0;
  left: 0;
  font-size: 12px;
  color: #D4AF37;
  padding: 4px 0;
  border-top: 1px dashed #C0A062;
  width: 100%;
}

.watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 80px;
  color: rgba(156, 111, 228, 0.03);
  white-space: nowrap;
  pointer-events: none;
  z-index: 0;
}

.decorative-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
  margin: 10px 0;
  opacity: 0.5;
}


.theme-gold .seal {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="%23D4AF37" stroke-width="2"/><circle cx="50" cy="50" r="44" fill="none" stroke="%23D4AF37" stroke-width="2"/><path d="M50 20l4 12h12l-10 8 4 12-10-8-10 8 4-12-10-8h12z" fill="%23D4AF37"/></svg>') no-repeat center;
}

.theme-gold .watermark {
  color: rgba(27, 63, 143, 0.03);
}


</style>
