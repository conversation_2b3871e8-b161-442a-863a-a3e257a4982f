<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
        :model-value="searchForm"
        :items="searchFormItems"
        @search="handleSearch"
        @reset="handleReset"
      />
      </template>
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增角色
        </a-button>
      </template>
    </page-header>

    <!-- 表格区域 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      @edit="handleEdit"
      @delete="handleDelete"
      :action-config="actionConfig"
      :delete-title="deleteTitle"
      
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '正常' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-button 
              type="link" 
              size="small" 
              @click="handlePermission(record)"
              class="custom-button"
            >
            <key-outlined />权限
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="handleUsers(record)"
              class="custom-button"
            >
            <user-outlined />用户
            </a-button>
        </template>
      </template>
    </base-table>

    <!-- 角色表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="formData.roleName" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" style="width: 100%" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formData.status" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限分配弹窗 -->
    <a-modal
      v-model:visible="permissionModalVisible"
      title="分配权限"
      @ok="handlePermissionOk"
      @cancel="handlePermissionCancel"
      width="600px"
    >
      <a-spin :spinning="menuLoading">
        <div class="permission-tree-container">
          <div class="permission-tree-header">
            <a-checkbox 
              :checked="isAllChecked"
              :indeterminate="isIndeterminate"
              @change="handleCheckAll"
            >
              全选/取消全选
            </a-checkbox>
          </div>
          <a-divider style="margin: 8px 0" />
          <div v-if="menuTreeData.length > 0" class="permission-tree-content">
            <a-directory-tree
              v-model:expandedKeys="expandedKeys"
              v-model:selectedKeys="selectedKeys"
              :tree-data="menuTreeData"
              :selectable="false"
              :blockNode="true"
              :autoExpandParent="true"
              :defaultExpandAll="true"
            />
          </div>
          <a-empty v-else description="暂无菜单数据" />
        </div>
      </a-spin>
    </a-modal>

    <!-- 角色用户列表弹窗 -->
    <a-modal
      v-model:visible="usersModalVisible"
      :title="`角色用户列表 - ${currentRole?.roleName || ''}`"
      width="700px"
      @cancel="handleUsersCancel"
      :footer="null"
    >
      <a-table
        :columns="userColumns"
        :data-source="roleUsers"
        :loading="usersLoading"
        :pagination="{ 
          showSizeChanger: true, 
          showTotal: total => `共 ${total} 条`,
          defaultPageSize: 5
        }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '正常' : '禁用' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { message, Checkbox as CheckboxComp } from 'ant-design-vue'
import { 
  getRoleList, 
  createRole, 
  updateRole, 
  deleteRole, 
  getRolePermissions, 
  updateRolePermissions,
  getRoleUsers
} from '@/api/system/role'
import { getMenuList } from '@/api/system/menu'
import { useMenuStore } from '@/store/modules/menu'
import { SearchFormCard } from '@/components/SearchForm'
import { useTablePagination } from '@/utils/common'

// 加载状态
const loading = ref(false)

// 获取菜单store
const menuStore = useMenuStore()

// 搜索表单
const searchForm = reactive({
  roleName: ''
})

// 表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除该角色吗?';


// 搜索表单配置
const searchFormItems = [
  {
    label: '角色名称',
    field: 'roleName',
    type: 'input',
    placeholder: '请输入角色名称'
  }
]

// 表格列定义
const columns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'roleName',
    width: 200  // 添加固定宽度
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
]

// 表格数据
const tableData = ref([])



// 弹窗控制
const modalVisible = ref(false)
const modalTitle = ref('新增角色')
const formRef = ref()

// 表单数据
const formData = reactive({
  id: undefined,
  roleName: '',
  sort: 0,
  status: true,
  remark: ''
})

// 表单校验规则
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称' },
    { max: 50, message: '角色名称最多50个字符' }
  ],
  sort: [{ required: true, message: '请输入排序号' }]
}

// 权限弹窗控制
const permissionModalVisible = ref(false)
const checkedKeys = ref([]) // checkStrictly模式下为对象 {checked: [], halfChecked: []}
const currentRole = ref(null)
const menuTreeData = ref([]) // 存储菜单树数据
const menuLoading = ref(false) // 添加菜单加载状态

// 用户列表控制
const usersModalVisible = ref(false)
const roleUsers = ref([])
const usersLoading = ref(false)
const userColumns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  }
]

// 树形结构的展开节点控制
const expandedKeys = ref([])
const selectedKeys = ref([])
const expandedAll = ref(true) // 默认设置为展开状态

// 计算属性：全选状态
const isAllChecked = computed(() => {
  if (!menuTreeData.value || menuTreeData.value.length === 0) return false
  const allIds = getAllTreeIds(menuTreeData.value)
  return allIds.length > 0 && checkedKeys.value.length === allIds.length
})

// 计算属性：半选状态
const isIndeterminate = computed(() => {
  if (!menuTreeData.value || menuTreeData.value.length === 0) return false
  const allIds = getAllTreeIds(menuTreeData.value)
  return checkedKeys.value.length > 0 && checkedKeys.value.length < allIds.length
})

// 处理节点选中
const handleCheck = (key, checked) => {
  let newCheckedKeys = [...checkedKeys.value];
  
  // 查找当前节点及其所有子节点的key
  const findChildKeys = (treeData, targetKey) => {
    const childKeys = [];
    
    const findChildren = (nodes, parentKey) => {
      if (!nodes) return;
      
      for (const node of nodes) {
        if (node.key === targetKey) {
          // 找到目标节点，收集它所有子节点的key
          const collectChildrenKeys = (items) => {
            if (!items) return;
            for (const item of items) {
              childKeys.push(item.key);
              if (item.children && item.children.length > 0) {
                collectChildrenKeys(item.children);
              }
            }
          };
          
          if (node.children && node.children.length > 0) {
            collectChildrenKeys(node.children);
          }
          return true;
        }
        
        if (node.children && node.children.length > 0) {
          if (findChildren(node.children, targetKey)) {
            return true;
          }
        }
      }
      
      return false;
    };
    
    findChildren(treeData, targetKey);
    return childKeys;
  };
  
  // 查找节点的所有父节点的key
  const findParentKey = (treeData, targetKey, path = []) => {
    if (!treeData || treeData.length === 0) return null;
    
    for (const node of treeData) {
      // 当前路径
      const currentPath = [...path];
      
      // 如果找到目标节点，返回路径
      if (node.key === targetKey) {
        return currentPath;
      }
      
      // 如果有子节点，继续搜索
      if (node.children && node.children.length > 0) {
        // 将当前节点加入路径
        currentPath.push(node.key);
        const result = findParentKey(node.children, targetKey, currentPath);
        if (result) {
          return result;
        }
      }
    }
    
    return null;
  };
  
  // 检查节点是否应该选中（所有子节点都被选中）
  const shouldCheckParent = (treeData, parentKey, checkedKeysList) => {
    const allChildKeys = [];
    
    const findAllChildren = (nodes) => {
      if (!nodes) return;
      
      for (const node of nodes) {
        if (node.key !== parentKey) { // 排除父节点自身
          allChildKeys.push(node.key);
        }
        if (node.children && node.children.length > 0) {
          findAllChildren(node.children);
        }
      }
    };
    
    // 找到父节点
    const findParent = (nodes) => {
      if (!nodes) return null;
      
      for (const node of nodes) {
        if (node.key === parentKey) {
          return node;
        }
        
        if (node.children && node.children.length > 0) {
          const foundParent = findParent(node.children);
          if (foundParent) {
            return foundParent;
          }
        }
      }
      
      return null;
    };
    
    const parentNode = findParent(treeData);
    if (parentNode && parentNode.children) {
      // 只检查直接子节点
      const directChildrenKeys = parentNode.children.map(child => child.key);
      return directChildrenKeys.every(childKey => checkedKeysList.includes(childKey));
    }
    
    return false;
  };
  
  if (checked) {
    // 选中当前节点
    if (!newCheckedKeys.includes(key)) {
      newCheckedKeys.push(key);
    }
    
    // 选中所有子节点
    const childKeys = findChildKeys(menuTreeData.value, key);
    childKeys.forEach(childKey => {
      if (!newCheckedKeys.includes(childKey)) {
        newCheckedKeys.push(childKey);
      }
    });
    
    // 检查父节点是否需要被选中（所有子节点都被选中）
    const parentKeys = findParentKey(menuTreeData.value, key);
    if (parentKeys && parentKeys.length > 0) {
      for (const parentKey of parentKeys) {
        if (shouldCheckParent(menuTreeData.value, parentKey, [...newCheckedKeys, key])) {
          if (!newCheckedKeys.includes(parentKey)) {
            newCheckedKeys.push(parentKey);
          }
        }
      }
    }
  } else {
    // 取消选中当前节点
    newCheckedKeys = newCheckedKeys.filter(k => k !== key);
    
    // 取消选中所有子节点
    const childKeys = findChildKeys(menuTreeData.value, key);
    newCheckedKeys = newCheckedKeys.filter(k => !childKeys.includes(k));
    
    // 取消父节点的选中状态
    const parentKeys = findParentKey(menuTreeData.value, key);
    if (parentKeys && parentKeys.length > 0) {
      // 父节点不再保持选中状态
      parentKeys.forEach(parentKey => {
        newCheckedKeys = newCheckedKeys.filter(k => k !== parentKey);
      });
    }
  }
  
  checkedKeys.value = newCheckedKeys;
  
  // 保持展开状态，确保选择操作后仍然全部展开
  expandedKeys.value = getAllTreeIds(menuTreeData.value);
}

// 全选/取消全选
const handleCheckAll = (e) => {
  if (e.target.checked) {
    // 全选
    checkedKeys.value = getAllTreeIds(menuTreeData.value);
  } else {
    // 取消全选
    checkedKeys.value = [];
  }
  
  // 保持展开状态
  expandedKeys.value = getAllTreeIds(menuTreeData.value);
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    loading.value = true
    const params = {
      roleName: searchForm.roleName,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const res = await getRoleList(params)
    tableData.value = res.list || []
    // 更新分页信息
    updatePagination({
      total: res.total || 0,
      pageNum: res.pageNum || pagination.current,
      pageSize: res.pageSize || pagination.pageSize
    })
  } catch (error) {
    message.error('获取角色列表失败: ' + error.message)
    tableData.value = []
    // 发生错误时重置分页
    updatePagination({ total: 0, pageNum: 1 })
  } finally {
    loading.value = false
  }
}

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchRoleList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
})

// 方法定义
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values)
  }
  // 重置到第一页
  resetPagination()
  fetchRoleList()
}

const handleReset = () => {
  searchForm.roleName = ''
  // 重置到第一页
  resetPagination()
  fetchRoleList()
}

const handleAdd = () => {
  modalTitle.value = '新增角色'
  formData.id = undefined
  formData.roleName = ''
  formData.sort = 0
  formData.status = true
  formData.remark = ''
  modalVisible.value = true
}

const handleEdit = (record) => {
  modalTitle.value = '编辑角色'
  Object.assign(formData, record)
  modalVisible.value = true
}

const handleDelete = async (record) => {
  try {
    loading.value = true
    await deleteRole(record.id)
    message.success('删除成功')
    fetchRoleList()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (formData.id) {
      // 编辑
      await updateRole(formData)
      message.success('更新成功')
    } else {
      // 新增
      await createRole(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    fetchRoleList()
  } catch (error) {
    message.error('操作失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handlePermission = async (record) => {
  try {
    currentRole.value = record
    permissionModalVisible.value = true
    menuLoading.value = true
    // 获取菜单树
    await fetchMenuTree()
    // 获取角色的权限
    const rolePermResponse = await getRolePermissions(record.id)
    // 解析角色权限数据
    let roleMenuIds = []
    if (rolePermResponse && rolePermResponse.data && rolePermResponse.data.menuIds) {
      roleMenuIds = rolePermResponse.data.menuIds
    } else if (rolePermResponse && rolePermResponse.menuIds) {
      roleMenuIds = rolePermResponse.menuIds
    }
    // 只用接口返回的权限赋值checkedKeys
    checkedKeys.value = Array.isArray(roleMenuIds) ? roleMenuIds : []
    
    // 确保默认展开所有节点
    expandedKeys.value = getAllTreeIds(menuTreeData.value)
    expandedAll.value = true
  } catch (error) {
    console.error('获取权限数据失败:', error)
    message.error('获取权限数据失败: ' + error.message)
  } finally {
    menuLoading.value = false
  }
}

const handlePermissionOk = async () => {
  try {
    loading.value = true;
    
    // 获取菜单树的所有节点ID（用于确保父节点不受影响）
    const allMenuIds = getAllTreeIds(menuTreeData.value);
    
    // 获取当前选中的菜单ID数组
    const checked = [...checkedKeys.value];
    
    // 确保所有父菜单都被保留
    // 即使其子菜单被取消勾选，父菜单也应该保留
    const ensureParentMenus = (treeData) => {
      const parentMenuIds = new Set();
      
      const findParents = (nodes) => {
        if (!Array.isArray(nodes)) return;
        
        for (const node of nodes) {
          if (node.children && node.children.length > 0) {
            // 如果该节点是父节点，且至少有一个子节点被选中，则保留父节点
            const hasCheckedChild = node.children.some(child => 
              checked.includes(child.key) || 
              (child.children && child.children.some(grandChild => checked.includes(grandChild.key)))
            );
            
            if (hasCheckedChild && !checked.includes(node.key)) {
              parentMenuIds.add(node.key);
            }
            
            // 递归处理子节点
            findParents(node.children);
          }
        }
      };
      
      findParents(treeData);
      return Array.from(parentMenuIds);
    };
    
    // 获取需要保留的父菜单ID
    const parentMenuIds = ensureParentMenus(menuTreeData.value);
    
    // 合并选中的菜单和需要保留的父菜单
    const finalMenuIds = [...new Set([...checked, ...parentMenuIds])];
    
    // 如果没有选中任何菜单，确认是否继续
    if (!finalMenuIds || finalMenuIds.length === 0) {
      const confirm = window.confirm('您没有选择任何菜单，确定要清空该角色的所有权限吗？');
      if (!confirm) {
        loading.value = false;
        return;
      }
    }
    
    // 提交选中的菜单ID到后端
    await updateRolePermissions({
      roleId: currentRole.value.id,
      permissions: finalMenuIds
    });
    
    message.success('权限更新成功');
    
    // 保存后重新拉取权限数据，确保回显
    await handlePermission(currentRole.value);
    permissionModalVisible.value = false;
  } catch (error) {
    console.error('权限更新失败:', error);
    message.error('权限更新失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

const handlePermissionCancel = () => {
  permissionModalVisible.value = false
  checkedKeys.value = []
  currentRole.value = null
}

const handleUsers = async (record) => {
  try {
    loading.value = true
    currentRole.value = record
    
    // 获取角色用户列表
    await fetchRoleUsers()
    
    usersModalVisible.value = true
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleUsersCancel = () => {
  usersModalVisible.value = false
}

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    menuLoading.value = true
    
    // 直接调用接口获取菜单列表，不依赖store中的数据
    const response = await getMenuList()
    
    // 检查响应结构，兼容多种可能的返回格式
    let menuList = []
    
    if (response) {
      if (response.data) {
        // 标准返回结构 { data: [...] }
        menuList = response.data
      } else if (response.code === 200 && response.data) {
        // 业务封装返回结构 { code: 200, data: [...] }
        menuList = response.data
      } else if (Array.isArray(response)) {
        // 直接返回数组
        menuList = response
      }
    }
    
    // 检查菜单列表是否为数组
    if (!Array.isArray(menuList)) {
      console.error('菜单列表不是数组:', menuList)
      menuList = []
    }
    
    // 将菜单转换为树形结构
    menuTreeData.value = formatMenuTree(menuList)
    
    // 如果菜单树还是空的，尝试使用硬编码的基础菜单数据
    if (menuTreeData.value.length === 0) {
      menuTreeData.value = getBasicMenuTree();
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error)
    message.error('获取菜单数据失败: ' + error.message)
    menuTreeData.value = getBasicMenuTree(); // 发生错误时使用基础菜单
  } finally {
    menuLoading.value = false
  }
}

// 获取基础菜单树（当API失败时使用）
const getBasicMenuTree = () => {
  return [
    {
      title: '控制台',
      key: 1,
      perms: 'dashboard'
    },
    {
      title: '系统管理',
      key: 2,
      children: [
        {
          title: '用户管理',
          key: 21,
          perms: 'system:user:list'
        },
        {
          title: '角色管理',
          key: 22,
          perms: 'system:role:list'
        },
        {
          title: '菜单管理',
          key: 23,
          perms: 'system:menu:list'
        }
      ]
    }
  ]
}

// 格式化菜单数据为树形结构
const formatMenuTree = (menus) => {
  return menus.map(menu => {
    const node = {
      title: () => {
        return h('div', { class: 'tree-node-content' }, [
          h(CheckboxComp, {
            checked: checkedKeys.value.includes(menu.id),
            onChange: (e) => handleCheck(menu.id, e.target.checked)
          }, () => [
            h('span', { class: 'node-title' }, menu.name)
          ])
        ]);
      },
      key: menu.id,
      perms: menu.perms
    }
    
    if (menu.children && menu.children.length > 0) {
      node.children = formatMenuTree(menu.children)
    }
    
    return node
  })
}

// 递归获取树的所有节点ID
const getAllTreeIds = (treeData) => {
  const ids = []
  
  const traverse = (nodes) => {
    if (!nodes) return
    
    nodes.forEach(node => {
      ids.push(node.key)
      
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
  return ids
}

// 获取角色用户列表
const fetchRoleUsers = async () => {
  try {
    loading.value = true
    const res = await getRoleUsers(currentRole.value.id)
    roleUsers.value = res.list
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
    roleUsers.value = []
  } finally {
    loading.value = false
  }
}

// 初始化获取角色数据
onMounted(() => {
  fetchRoleList()
  // 预加载菜单数据
  fetchMenuTree()
})
</script>

<style scoped>
.table-toolbar {
  margin-bottom: 16px;
}

/* 权限树相关样式 */
.permission-tree-container {
  max-height: 450px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.permission-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
  margin-bottom: 8px;
}

.permission-tree-content {
  padding: 8px;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 28px;
}

.node-title {
  margin-right: 5px;
  font-weight: 500;
}

.node-perms {
  font-size: 12px;
  color: #8c8c8c;
}

/* 兼容不同版本Vue的深度选择器写法 */
:deep(.ant-tree-directory .ant-tree-treenode),
::v-deep .ant-tree-directory .ant-tree-treenode,
/deep/ .ant-tree-directory .ant-tree-treenode {
  padding: 4px 0 !important;
}

:deep(.ant-directory-tree .ant-tree-treenode-selected:before),
::v-deep .ant-directory-tree .ant-tree-treenode-selected:before,
/deep/ .ant-directory-tree .ant-tree-treenode-selected:before {
  background: transparent !important;
}

:deep(.ant-checkbox-wrapper),
::v-deep .ant-checkbox-wrapper,
/deep/ .ant-checkbox-wrapper {
  width: 100%;
}

/* 增加一些额外的样式修复 */
:deep(.ant-tree-list-holder-inner),
::v-deep .ant-tree-list-holder-inner,
/deep/ .ant-tree-list-holder-inner {
  padding-top: 4px;
}

:deep(.ant-tree-node-content-wrapper),
::v-deep .ant-tree-node-content-wrapper,
/deep/ .ant-tree-node-content-wrapper {
  display: flex !important;
  flex: 1;
  align-items: center;
}

:deep(.ant-tree-switcher),
::v-deep .ant-tree-switcher,
/deep/ .ant-tree-switcher {
  align-self: center;
}
</style>