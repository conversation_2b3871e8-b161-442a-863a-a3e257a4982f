{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 5173 --cors --strictPort false", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueup/vue-quill": "^1.2.0", "ant-design-vue": "^4.0.0-rc.6", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "dom-to-image": "^2.6.0", "less": "^4.3.0", "sass": "^1.86.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}