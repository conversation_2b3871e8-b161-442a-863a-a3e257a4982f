<!-- 岗位分类树组件 -->
<template>
  <div class="category-tree-container">
    <h3 class="tree-title">{{ title }}</h3>
    <a-tree
      :tree-data="treeData"
      :default-expanded-keys="defaultExpandedKeys"
      @select="handleTreeSelect"
      :fieldNames="{ title: 'title', key: 'key', children: 'children' }"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import { positions } from '@/mock/position-management';
import { positionLevels } from '@/mock/position-level';

const props = defineProps({
  // 树组件标题
  title: {
    type: String,
    default: '岗位结构'
  },
  // 默认展开的节点
  defaultExpandedKeys: {
    type: Array,
    default: () => ['前厅', '后厨']
  },
  // 是否显示所有配置节点
  showAllNode: {
    type: Boolean,
    default: true
  },
  // 所有配置节点的标题
  allNodeTitle: {
    type: String,
    default: '全部'
  },
  // 过滤显示的岗位分类（前厅、后厨）
  categoryFilter: {
    type: Array,
    default: () => []
  },
  // 自定义转换节点数据的函数
  customNodeTransformer: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['select']);

// 树数据
const treeData = ref([]);

// 处理树节点选择
const handleTreeSelect = (selectedKeys, info) => {
  if (selectedKeys.length === 0) return;
  
  const key = selectedKeys[0];
  let nodeData;
  
  // 根据key的格式判断节点类型
  if (key === 'all') {
    nodeData = {
      type: 'all',
      key: 'all',
      title: props.allNodeTitle,
      category: '',
      position: '',
      level: ''
    };
  } else if (key === '前厅' || key === '后厨') {
    nodeData = {
      type: 'category',
      key,
      title: key,
      category: key,
      position: '',
      level: ''
    };
  } else if (key.includes('-') && !key.includes(':')) {
    // 岗位级别: '前厅-服务员'
    const [category, position] = key.split('-');
    nodeData = {
      type: 'position',
      key,
      title: position,
      category,
      position,
      level: ''
    };
  } else if (key.includes(':')) {
    // 等级级别: '前厅-服务员:P1'
    const [posKey, level] = key.split(':');
    const [category, position] = posKey.split('-');
    nodeData = {
      type: 'level',
      key,
      title: level,
      category,
      position,
      level
    };
  }
  
  // 如果有自定义转换函数，应用它
  if (props.customNodeTransformer && nodeData) {
    nodeData = props.customNodeTransformer(nodeData, info);
  }
  
  // 向父组件发送选中的节点数据
  emit('select', nodeData);
};

// 初始化树数据
onMounted(() => {
  const initialTreeData = [];
  
  // 添加"所有配置"节点
  if (props.showAllNode) {
    initialTreeData.push({
      title: props.allNodeTitle,
      key: 'all',
    });
  }
  
  // 从positions和positionLevels构建树结构
  const categoryMap = {
    '前厅': {
      title: '前厅',
      key: '前厅',
      children: []
    },
    '后厨': {
      title: '后厨',
      key: '后厨',
      children: []
    }
  };

  // 按岗位类型分组添加岗位
  positions.forEach(position => {
    const category = position.category;
    
    // 如果设置了分类过滤且当前分类不在过滤列表中，则跳过
    if (props.categoryFilter.length > 0 && !props.categoryFilter.includes(category)) {
      return;
    }
    
    if (categoryMap[category]) {
      const positionNode = {
        title: position.name,
        key: `${category}-${position.name}`,
        children: []
      };

      // 为每个岗位添加等级子节点
      positionLevels.forEach(level => {
        positionNode.children.push({
          title: `${level.level} (${level.englishAbbr})`,
          key: `${category}-${position.name}:${level.englishAbbr}`,
          isLeaf: true
        });
      });

      categoryMap[category].children.push(positionNode);
    }
  });

  // 添加分类到树数据
  Object.keys(categoryMap).forEach(key => {
    // 如果设置了分类过滤且当前分类不在过滤列表中，则跳过
    if (props.categoryFilter.length > 0 && !props.categoryFilter.includes(key)) {
      return;
    }
    initialTreeData.push(categoryMap[key]);
  });
  
  treeData.value = initialTreeData;
});
</script>

<style scoped>
.category-tree-container {
  width: 100%;
  height: 100%;
}

.tree-title {
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

/* 树节点高亮颜色 */
:deep(.ant-tree-node-selected) {
  background-color: rgba(161, 140, 209, 0.2) !important;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(161, 140, 209, 0.1) !important;
}
</style> 