<template>
  <!-- <a-card class="search-form-card" :bordered="false"> -->
    <search-form
      :items="items"
      :model-value="formModel"
      @update:model-value="handleModelValueUpdate"
      :search-text="searchText"
      :reset-text="resetText"
      :reset-page="resetPage"
      :form-class="formClass"
      @search="handleSearch"
      @reset="handleReset"
    />
  <!-- </a-card> -->
</template>

<script>
import { defineComponent, reactive, watch, toRefs } from 'vue';
import SearchForm from './index.vue';

export default defineComponent({
  name: 'SearchFormCard',
  components: {
    SearchForm,
  },
  props: {
    // 表单项配置
    items: {
      type: Array,
      default: () => [],
    },
    // 表单模型绑定值
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    // 搜索按钮文本
    searchText: {
      type: String,
      default: '查询',
    },
    // 重置按钮文本
    resetText: {
      type: String,
      default: '重置',
    },
    // 搜索时是否重置分页到第一页
    resetPage: {
      type: Boolean,
      default: true,
    },
    // 搜索表单额外类名
    formClass: {
      type: String,
      default: '',
    },
  },
  emits: ['search', 'reset', 'update:modelValue'],
  setup(props, { emit }) {
    const { modelValue } = toRefs(props);
    // 内部表单数据模型
    const formModel = reactive({...modelValue.value});
    
    // 同步父组件传入的值和内部表单值
    watch(() => modelValue.value, (newVal) => {
      console.log('[SearchFormCard] 父组件数据变化:', newVal);
      Object.assign(formModel, newVal);
    }, { deep: true });
    
    // 处理来自SearchForm的数据更新
    const handleModelValueUpdate = (newVal) => {
      console.log('[SearchFormCard] 接收到SearchForm更新:', newVal);
      Object.assign(formModel, newVal);
      // 向上传递更新事件
      emit('update:modelValue', {...formModel});
    };
    
    // 搜索处理
    const handleSearch = (values) => {
      console.log('[SearchFormCard] 搜索触发，表单值:', values);
      emit('search', values);
    };
    
    // 重置处理
    const handleReset = (values) => {
      console.log('[SearchFormCard] 重置触发，表单值:', values);
      emit('reset', values);
    };
    
    return {
      formModel,
      handleSearch,
      handleReset,
      handleModelValueUpdate
    };
  },
});
</script>

<style lang="scss" scoped>
</style> 