import request from '@/utils/request'

// 获取智能体列表
export function getAgentList(params) {
  return request({
    url: '/system/agent/list',
    method: 'get',
    params
  })
}

// 获取智能体详情
export function getAgentDetail(id) {
  return request({
    url: `/system/agent/detail/${id}`,
    method: 'get'
  })
}

// 创建智能体
export function createAgent(data) {
  return request({
    url: '/system/agent',
    method: 'post',
    data
  })
}

// 更新智能体
export function updateAgent(data) {
  return request({
    url: '/system/agent',
    method: 'put',
    data
  })
}

// 删除智能体
export function deleteAgent(id) {
  return request({
    url: `/system/agent/${id}`,
    method: 'delete'
  })
} 