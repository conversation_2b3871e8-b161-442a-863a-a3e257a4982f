<template>
  <div class="exam-statistics">
    <a-row :gutter="24">
      <a-col :span="8">
        <div class="statistic-card">
          <div class="statistic-title">考试通过数量</div>
          <div class="statistic-value gradient-text">{{ data.passCount }}</div>
          <div class="statistic-desc">总考试数: {{ data.totalCount }}</div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="statistic-card">
          <div class="statistic-title">考试通过率</div>
          <div class="statistic-value gradient-text">{{ data.passRate }}%</div>
          <div class="statistic-desc">
            <a-progress
              :percent="data.passRate"
              :stroke-color="{
                '0%': '#a18cd1',
                '100%': '#fbc2eb'
              }"
            />
          </div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="statistic-card chart-card">
          <div class="statistic-title">通过率走势</div>
          <div class="mini-chart" ref="trendChartRef"></div>
        </div>
      </a-col>
    </a-row>

    <div class="position-rates">
      <a-row :gutter="16">
        <a-col :span="12">
          <div class="certificate-ranking">
            <a-spin :spinning="loadingCertificateData">
              <a-table
                :columns="certificateColumns"
                :data-source="certificateRankingData"
                :pagination="false"
                size="small"
                :row-class-name="(_record, index) => index < 3 ? 'top-ranked-row' : ''"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'rank'">
                    <a-avatar :size="28" :style="{ backgroundColor: getAvatarColor(index) }">
                      {{ index + 1 }}
                    </a-avatar>
                  </template>
                  <template v-if="column.key === 'name'">
                    {{ record.name }}
                  </template>
                  <template v-if="column.key === 'position'">
                    <a-tag :color="getPositionColor(record.position)">{{ record.position }}</a-tag>
                  </template>
                  <template v-if="column.key === 'level'">
                    <a-tag color="green">{{ record.level }}</a-tag>
                  </template>
                  <template v-if="column.key === 'progress'">
                    <div class="cert-count-text">
                      {{ record.count }}证书
                    </div>
                  </template>
                </template>
              </a-table>
            </a-spin>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="position-list">
            <div
              v-for="(item, index) in data.positionRates"
              :key="index"
              class="position-item"
            >
              <div class="position-rank">{{ index + 1 }}</div>
              <div class="position-name">{{ item.position }}</div>
              <div class="position-rate-bar">
                <div
                  class="position-rate-progress"
                  :style="{
                    width: `${item.passRate}%`,
                    background: getGradientByIndex(index)
                  }"
                ></div>
                <span class="position-rate-text">{{ item.passRate }}%</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  certificateRankingData: {
    type: Array,
    default: () => []
  },
  loadingCertificateData: {
    type: Boolean,
    default: false
  }
});

const trendChartRef = ref(null);
let trendChart = null;

const certificateColumns = [
  {title: '排名', key: 'rank', width: 70, align: 'center'},
  {title: '姓名', dataIndex: 'name', key: 'name', width: 80, align: 'center'},
  {title: '岗位', key: 'position', width: 110, align: 'center'},
  {title: '等级', key: 'level', width: 80, align: 'center'},
  {title: '证书数量', key: 'progress', width: 180, align: 'center'}
];

const getGradientByIndex = (index) => {
  const gradients = [
    'linear-gradient(to right, #a18cd1, #fbc2eb)',
    'linear-gradient(to right, #96e6a1, #d4fc79)',
    'linear-gradient(to right, #84fab0, #8fd3f4)',
    'linear-gradient(to right, #fa709a, #fee140)',
    'linear-gradient(to right, #667eea, #764ba2)',
    'linear-gradient(to right, #ff9a9e, #fad0c4)',
    'linear-gradient(to right, #a1c4fd, #c2e9fb)'
  ];

  return gradients[index % gradients.length];
};

const getAvatarColor = (index) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#8470FF'];
  return colors[index % colors.length];
};

const getProgressColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#52c41a', '#1890ff', '#722ed1'];
  return colors[index % colors.length];
};

const getPositionColor = (position) => {
  const positionColors = {
    '前厅经理': '#1890ff',
    '厨师长': '#13c2c2',
    '服务员': '#52c41a',
    '厨师': '#fa8c16',
    '收银员': '#eb2f96',
    '迎宾员': '#722ed1',
    '后厨助理': '#faad14',
    '烧烤师': '#fa8c16'
  };
  return positionColors[position] || '#1890ff';
};

// 格式化趋势数据
const formatTrendData = (trendData) => {
  if (!trendData || !Array.isArray(trendData) || trendData.length === 0) {
    return {
      categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      data: [75, 80, 78, 83, 85, props.data.passRate || 0]
    };
  }

  const categories = trendData.map(item => {
    // 格式化期间显示
    const period = item.period;
    if (period.includes('-')) {
      const [year, month] = period.split('-');
      return `${parseInt(month)}月`;
    }
    return period;
  });

  const data = trendData.map(item => item.passRate || 0);

  return { categories, data };
};

// 初始化或更新图表
const initOrUpdateChart = () => {
  if (!trendChartRef.value) return;

  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value);
  }

  const { categories, data } = formatTrendData(props.data.trend);

  const option = {
    grid: {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    series: [{
      data: data,
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: '#a18cd1',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(161, 140, 209, 0.5)'
          }, {
            offset: 1, color: 'rgba(251, 194, 235, 0.1)'
          }]
        }
      }
    }]
  };

  trendChart.setOption(option);
};

// 监听数据变化，更新图表
watch(() => props.data, () => {
  nextTick(() => {
    initOrUpdateChart();
  });
}, { deep: true });

onMounted(() => {
  nextTick(() => {
    initOrUpdateChart();

    // 添加窗口大小变化监听
    window.addEventListener('resize', () => {
      if (trendChart) {
        trendChart.resize();
      }
    });
  });
});
</script>

<style scoped>
.exam-statistics {
  padding: 16px 0;
}

.statistic-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  height: 160px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statistic-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 8px;
}

.gradient-text {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.statistic-desc {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: auto;
}

.chart-card {
  position: relative;
}

.mini-chart {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
}

.position-rates {
  margin-top: 24px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.position-rates-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.certificate-ranking {
  height: 300px;
  overflow-y: auto;
}

.position-list {
  height: 300px;
  overflow-y: auto;
}

.position-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.position-rank {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background-color: #a18cd1;
  color: white;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.position-name {
  width: 80px;
  font-size: 14px;
  margin-right: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.position-rate-bar {
  flex: 1;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.position-rate-progress {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 10px;
  transition: width 0.5s ease;
}

.position-rate-text {
  position: absolute;
  right: 8px;
  top: 0;
  line-height: 20px;
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.top-ranked-row {
  background-color: rgba(24, 144, 255, 0.05);
}

.cert-count-text {
  width: 100%;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
  padding: 4px 0;
}

:deep(.ant-table-tbody > tr > td) {
  text-align: center;
}

:deep(.ant-table-small) {
  font-size: 13px;
}

:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  text-align: center;
}

:deep(.ant-table-small .ant-table-thead > tr > th .ant-table-header-column) {
  display: flex;
  justify-content: center;
}

:deep(.ant-table-row:hover) {
  cursor: pointer;
}
</style>
