import request from '@/utils/request';

/**
 * 练习记录API
 */

// 获取练习记录列表
export function getPracticeRecords(params) {
  return request({
    url: '/practice/records',
    method: 'get',
    params
  });
}

// 获取练习记录详情
export function getPracticeRecordDetail(id) {
  return request({
    url: `/practice/records/${id}`,
    method: 'get'
  });
}

// 导出用户练习词汇列表
export function getUserPracticeWordList(data) {
  return request({
    url: '/practice/words',
    method: 'post',
    data
  });
} 