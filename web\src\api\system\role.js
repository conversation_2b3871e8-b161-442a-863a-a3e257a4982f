import request from '@/utils/request'

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(data) {
  return request({
    url: '/system/role',
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/system/role/${id}`,
    method: 'delete'
  })
}

// 获取角色权限
export function getRolePermissions(roleId) {
  return request({
    url: `/system/role/permissions/${roleId}`,
    method: 'get'
  })
}

// 更新角色权限
export function updateRolePermissions(data) {
  return request({
    url: '/system/role/permissions',
    method: 'put',
    data
  })
}

// 获取角色下的用户列表
export function getRoleUsers(roleId) {
  return request({
    url: `/system/role/users/${roleId}`,
    method: 'get'
  })
} 