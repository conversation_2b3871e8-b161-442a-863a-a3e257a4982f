<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    v-model:openKeys="openKeys"
    mode="inline"
    theme="light"
    class="menu"
  >
    <!-- 菜单项 -->
    <template v-for="item in menuItems" :key="item.key">
      <!-- 普通菜单项 -->
      <a-menu-item v-if="!item.children || item.children.length === 0" :key="item.key">
        <template #icon v-if="item.icon">
          <component :is="item.icon" />
        </template>
        <router-link :to="item.path">{{ item.title }}</router-link>
      </a-menu-item>

      <!-- 子菜单 -->
      <template v-else>
        <a-sub-menu :key="item.key">
          <template #icon v-if="item.icon">
            <component :is="item.icon" />
          </template>
          <template #title>{{ item.title }}</template>

          <!-- 递归渲染子菜单的子项 -->
          <template v-for="child in item.children" :key="child.key">
            <!-- 无孙级菜单的子菜单项 -->
            <a-menu-item v-if="!child.children || child.children.length === 0" :key="child.key">
              <router-link :to="child.path">{{ child.title }}</router-link>
            </a-menu-item>

            <!-- 有孙级菜单的子菜单项 -->
            <a-sub-menu v-else :key="child.key">
              <template #title>{{ child.title }}</template>
              <a-menu-item v-for="grandChild in child.children" :key="grandChild.key">
                <router-link :to="grandChild.path">{{ grandChild.title }}</router-link>
              </a-menu-item>
            </a-sub-menu>
          </template>
        </a-sub-menu>
      </template>
    </template>
  </a-menu>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import {
  DashboardOutlined,
  TeamOutlined,
  UserOutlined,
  SettingOutlined,
  FileOutlined,
  ApartmentOutlined,
  BookOutlined,
  FormOutlined,
  CustomerServiceOutlined,
  MessageOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  menus: {
    type: Array,
    default: () => []
  }
});

const route = useRoute();

// 菜单选中状态
const selectedKeys = ref([route.path]);
const openKeys = ref([]);

// 获取图标组件
const getIconComponent = (iconName) => {
  if (!iconName) return null;

  // 图标映射表，增加更多图标支持
  const iconMap = {
    // 常规图标
    'dashboard': DashboardOutlined,
    'team': TeamOutlined,
    'user': UserOutlined,
    'setting': SettingOutlined,
    'file': FileOutlined,
    'apartment': ApartmentOutlined,
    'book': BookOutlined,
    'form': FormOutlined,
    'customer-service': CustomerServiceOutlined,
    'message': MessageOutlined,

    // 添加更多图标支持
    'SettingOutlined': SettingOutlined,
    'UserOutlined': UserOutlined,
    'TeamOutlined': TeamOutlined,
    'DashboardOutlined': DashboardOutlined,
    'FileOutlined': FileOutlined,
    'ApartmentOutlined': ApartmentOutlined,
    'BookOutlined': BookOutlined,
    'FormOutlined': FormOutlined,
    'CustomerServiceOutlined': CustomerServiceOutlined,
    'MessageOutlined': MessageOutlined
  };

  const iconComponent = iconMap[iconName];

  return iconComponent || null;
};

// 递归处理菜单数据
const processMenuItems = (menuList) => {
  if (!menuList || !Array.isArray(menuList)) return [];

  return menuList.map(menu => {
    if (!menu) return null;

    // 生成key，使用完整路径
    const path = menu.path || '';
    // 确保key的唯一性
    const key = path || `menu-${menu.id || Math.random().toString(36).substr(2, 9)}`;

    const result = {
      key,
      title: menu.name || '未命名',
      path: path,
      icon: getIconComponent(menu.icon),
      parentId: menu.parentId,
      id:menu.id
    };

    // 递归处理子菜单
    if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
      result.children = processMenuItems(menu.children);
    }

    return result;
  }).filter(Boolean);
};

// 处理菜单数据
const menuItems = computed(() => {
  if (!props.menus || !Array.isArray(props.menus) || props.menus.length === 0) {
    console.warn('MenuTree组件收到的菜单数据为空或无效');
    return [];
  }

  return processMenuItems(props.menus);
});


// 查找菜单项的所有父级菜单
const findParentKeys = (menuItems, path) => {
  const parentKeys = [];

  const findParent = (items, targetPath, parentPath = null) => {
    for (const item of items) {
      if (item.path === targetPath) {
        if (parentPath) parentKeys.push(parentPath);
        return true;
      }
      
      if (item.children && item.children.length > 0) {
        if (findParent(item.children, targetPath, item.path)) {
          if (parentPath) parentKeys.push(parentPath);
          return true;
        }
      }
    }
    return false;
  };

  findParent(menuItems, path);
  return parentKeys;
};

// 设置初始打开的菜单项
const updateMenuOpenKeys = (path) => {
  // 更新当前选中的菜单
  selectedKeys.value = [path];
  
  // 查找所有父级菜单
  const parentKeys = findParentKeys(menuItems.value, path);
  
  // 合并新的父级菜单和已经打开的菜单，避免关闭已打开的菜单
  const existingOpenKeys = [...openKeys.value];
  const newOpenKeys = [...new Set([...existingOpenKeys, ...parentKeys])];
  
  // 设置打开的菜单项
  openKeys.value = newOpenKeys;
};

// 监听路由变化，更新菜单选中状态
watch(
  () => route.path,
  (path) => {
    updateMenuOpenKeys(path);
  },
  { immediate: true }
);

// 初始化菜单
onMounted(() => {
  updateMenuOpenKeys(route.path);
});

// 暴露属性和方法
defineExpose({
  selectedKeys,
  openKeys
});
</script>

<style scoped>
.menu {
  border-right: 0;
  height:calc( 100vh - 64px);
  /* overflow-y: auto; */
}
</style>
