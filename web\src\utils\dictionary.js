import { getDictionaryDataByTypeCode } from '@/api/organization/dictionary';

// 缓存已加载的字典数据
const dictionaryCache = {};

/**
 * 获取字典数据
 * @param {String} type - 字典类型编码
 * @returns {Promise<Array>} - 字典数据数组
 */
export async function getDictionary(type) {
  // 如果缓存中已有数据，则直接返回
  if (dictionaryCache[type]) {
    return dictionaryCache[type];
  }
  
  try {
    // 从后端获取字典数据
    const response = await getDictionaryDataByTypeCode(type);
    if (response && response.code === 200 && response.data) {
      // 缓存数据
      dictionaryCache[type] = response.data;
      return response.data;
    }
    return [];
  } catch (error) {
    console.error('获取字典数据失败:', error);
    return [];
  }
}

/**
 * 根据字典值获取字典标签
 * @param {String} dictType - 字典类型编码
 * @param {String|Number} value - 字典值
 * @returns {Promise<String>} - 字典标签
 */
export async function getDictLabel(dictType, value) {
  if (!value && value !== 0) return '';
  
  const dictData = await getDictionary(dictType);
  const dictItem = dictData.find(item => item.value === value || item.value === String(value));
  return dictItem ? dictItem.label : '';
}

/**
 * 根据字典标签获取字典值
 * @param {String} dictType - 字典类型编码
 * @param {String} label - 字典标签
 * @returns {Promise<String|Number>} - 字典值
 */
export async function getDictValue(dictType, label) {
  if (!label) return '';
  
  const dictData = await getDictionary(dictType);
  const dictItem = dictData.find(item => item.label === label);
  return dictItem ? dictItem.value : '';
}

/**
 * 将对象中指定字段的字典标签转换为字典值
 * @param {Object} obj - 要转换的对象
 * @param {Object} fieldMapping - 字段映射 { 字段名: 字典类型编码 }
 * @returns {Promise<Object>} - 转换后的对象
 */
export async function convertLabelToValue(obj, fieldMapping) {
  const result = { ...obj };
  
  for (const [field, dictType] of Object.entries(fieldMapping)) {
    if (obj[field]) {
      result[field] = await getDictValue(dictType, obj[field]);
    }
  }
  
  return result;
}

/**
 * 将对象中指定字段的字典值转换为字典标签
 * @param {Object} obj - 要转换的对象
 * @param {Object} fieldMapping - 字段映射 { 字段名: 字典类型编码 }
 * @returns {Promise<Object>} - 转换后的对象
 */
export async function convertValueToLabel(obj, fieldMapping) {
  const result = { ...obj };
  
  for (const [field, dictType] of Object.entries(fieldMapping)) {
    if (obj[field]) {
      result[field] = await getDictLabel(dictType, obj[field]);
    }
  }
  
  return result;
}

/**
 * 从字典中获取下拉选项数据
 * @param {String} dictType - 字典类型编码
 * @returns {Promise<Array>} - 选项数组 [{ value, label }]
 */
export async function getDictOptions(dictType) {
  const dictData = await getDictionary(dictType);
  return dictData.map(item => ({
    value: item.value,
    label: item.label
  }));
} 