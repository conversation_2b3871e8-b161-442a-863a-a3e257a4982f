import request from '@/utils/request';

// 字典类型接口
/**
 * 获取字典类型列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getDictionaryTypeList(params) {
  return request({
    url: '/system/dict/type/list',
    method: 'get',
    params
  });
}

/**
 * 获取字典类型详情
 * @param {Number} id - 字典类型ID
 * @returns {Promise} 请求结果
 */
export function getDictionaryTypeDetail(id) {
  return request({
    url: `/system/dict/type/${id}`,
    method: 'get'
  });
}

/**
 * 新增字典类型
 * @param {Object} data - 字典类型数据
 * @returns {Promise} 请求结果
 */
export function addDictionaryType(data) {
  return request({
    url: '/system/dict/type',
    method: 'post',
    data
  });
}

/**
 * 更新字典类型
 * @param {Object} data - 字典类型数据
 * @returns {Promise} 请求结果
 */
export function updateDictionaryType(data) {
  return request({
    url: '/system/dict/type',
    method: 'put',
    data
  });
}

/**
 * 删除字典类型
 * @param {Number} id - 字典类型ID
 * @returns {Promise} 请求结果
 */
export function deleteDictionaryType(id) {
  return request({
    url: `/system/dict/type/${id}`,
    method: 'delete'
  });
}

/**
 * 批量更新字典状态
 * @param {Array} ids - 字典类型ID数组
 * @param {Boolean} status - 状态
 * @returns {Promise} 请求结果
 */
export function batchUpdateDictionaryStatus(ids, status) {
  return request({
    url: '/system/dict/type/batch-status',
    method: 'put',
    data: { ids, status }
  });
}

// 字典数据接口
/**
 * 获取字典数据列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function getDictionaryDataList(params) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params
  });
}

/**
 * 根据字典类型编码获取字典数据
 * @param {String} typeCode - 字典类型编码
 * @returns {Promise} 请求结果
 */
export function getDictionaryDataByTypeCode(typeCode) {
  return request({
    url: `/system/dict/data/type/${typeCode}`,
    method: 'get'
  });
}

/**
 * 获取字典数据详情
 * @param {Number} id - 字典数据ID
 * @returns {Promise} 请求结果
 */
export function getDictionaryDataDetail(id) {
  return request({
    url: `/system/dict/data/${id}`,
    method: 'get'
  });
}

/**
 * 新增字典数据
 * @param {Object} data - 字典数据
 * @returns {Promise} 请求结果
 */
export function addDictionaryData(data) {
  return request({
    url: '/system/dict/data',
    method: 'post',
    data
  });
}

/**
 * 更新字典数据
 * @param {Object} data - 字典数据
 * @returns {Promise} 请求结果
 */
export function updateDictionaryData(data) {
  return request({
    url: '/system/dict/data',
    method: 'put',
    data
  });
}

/**
 * 删除字典数据
 * @param {Number} id - 字典数据ID
 * @returns {Promise} 请求结果
 */
export function deleteDictionaryData(id) {
  return request({
    url: `/system/dict/data/${id}`,
    method: 'delete'
  });
} 