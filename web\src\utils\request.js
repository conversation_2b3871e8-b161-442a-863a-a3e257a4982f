import axios from 'axios'
import { message } from 'ant-design-vue'
import router from '@/router'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL || '/api', // 从环境变量获取baseURL
  timeout: parseInt(import.meta.env.VITE_APP_API_TIMEOUT) || 30000, // 请求超时时间（30秒）
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 记录请求数据
    if (config.method === 'post' || config.method === 'put') {
      try {
        // 尝试深拷贝一份，防止引用问题
        const clonedData = JSON.parse(JSON.stringify(config.data));
      } catch (err) {
        console.error('请求数据深拷贝失败:', err);
      }
    }
    
    return config
  },
  error => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 如果是blob类型响应（文件下载），直接返回原始数据
    if (response.config.responseType === 'blob') {
      return response.data;
    }
    
    let res = response.data
    const config = response.config
    

    // 如果不是对象，尝试转成对象
    if (typeof res === 'string') {
      try {
        res = JSON.parse(res)
      } catch (e) {
        console.error('响应不是合法JSON，无法解析:', res)
      }
    }
    
    // 这里可以根据后端的响应结构进行调整
    if (res && res.code === 200) {
      return res.data
    }
    
    // 如果请求配置了silent模式，不显示错误提示
    if (!config.silent) {
      message.error(res.message || '操作失败')
    }
    
    // 处理特定的错误码
    if (res.code === 401) {
      // token过期或未登录
      localStorage.removeItem('token')
      router.push('/login')
    }
    
    return Promise.reject(new Error(res.message || '操作失败'))
  },
  error => {
    console.error('响应错误：', error)
    
    // 检查是否为静默请求
    const isSilent = error.config && error.config.silent === true
    
    // 处理网络错误
    if (!error.response) {
      if (!isSilent) {
        message.error('网络异常，请检查网络连接')
      }
      return Promise.reject(error)
    }
    
    // 保存错误信息到error对象中，以便调用者处理特定错误
    if (error.response.data) {
      error.message = error.response.data.message || `${error.response.status} ${error.response.statusText}`
    }
    
    // 处理HTTP状态码
    const status = error.response.status
    
    // 如果不是静默请求，显示错误提示
    if (!isSilent) {
      switch (status) {
        case 401:
          message.error('未登录或登录已过期')
          localStorage.removeItem('token')
          router.push('/login')
          break
        case 403:
          message.error('没有权限')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器错误')
          break
        // 注释掉400的全局处理，让调用者自己处理
        /*
        case 400:
          message.error(error.response.data.message || '请求参数错误')
          break
        */
        default:
          if (status !== 400) { // 不自动显示400错误，让调用者自己处理
            message.error('操作失败，请重试')
          }
      }
    }
    
    return Promise.reject(error)
  }
)

export default request 
