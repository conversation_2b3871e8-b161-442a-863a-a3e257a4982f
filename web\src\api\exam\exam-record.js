import request from '@/utils/request';

/**
 * 考试记录API
 */

// 获取考试记录列表
export function getExamRecords(params) {
  return request({
    url: '/exam/record/records',
    method: 'get',
    params
  });
}

// 获取考试记录详情
export function getExamRecordDetail(id) {
  return request({
    url: `/exam/record/record/${id}`,
    method: 'get'
  });
}

// 创建考试记录
export function createExamRecord(data) {
  return request({
    url: '/exam/record/record',
    method: 'post',
    data
  });
}

// 更新考试记录
export function updateExamRecord(id, data) {
  return request({
    url: `/exam/record/record/${id}`,
    method: 'put',
    data
  });
}

// 更新考试记录确认状态
export function updateConfirmStatus(id, data) {
  return request({
    url: `/exam/record/record/${id}/confirm`,
    method: 'post',
    data
  });
}

// 删除考试记录
export function deleteExamRecord(id) {
  return request({
    url: `/exam/record/record/${id}`,
    method: 'delete'
  });
} 