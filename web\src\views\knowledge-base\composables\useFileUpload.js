import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { 
  uploadFile,
  batchSaveDocuments 
} from '@/api/knowledge-base';

/**
 * 文件上传逻辑的可复用composable
 */
export default function useFileUpload() {
  // 文件上传相关变量
  const fileList = ref([]);
  const uploading = ref(false);
  const currentStep = ref(0);
  const uploadProgress = ref(0);
  const uploadSuccessCount = ref(0);
  const uploadFailCount = ref(0);

  // 文件列表带有序号和格式化的大小
  const fileListWithNames = computed(() => {
    return fileList.value.map((file, index) => {
      const fileType = getFileType(file.name);
      return {
        key: index,
        index: index + 1,
        name: file.name,
        fileType: getFileTypeChinese(fileType),
        size: formatFileSize(file.size || 0)
      };
    });
  });

  // 确认列表的表格列
  const confirmColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60
    },
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 100
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100
    }
  ];

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型
  const getFileType = (fileName) => {
    const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    
    // 文档类型
    if (['pdf'].includes(ext)) {
      return 'pdf';
    } else if (['doc', 'docx'].includes(ext)) {
      return 'word';
    } else if (['xls', 'xlsx'].includes(ext)) {
      return 'excel';
    } else if (['ppt', 'pptx'].includes(ext)) {
      return 'ppt';
    } 
    // 图片类型
    else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
      return 'image';
    } 
    // 文本类型
    else if (['txt', 'log', 'md', 'json', 'xml', 'html', 'css', 'js'].includes(ext)) {
      return 'text';
    }
    // 压缩文件
    else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return 'archive';
    }
    // 音视频文件
    else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
      return 'audio';
    }
    else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
      return 'video';
    }
    
    return 'unknown';
  };

  // 获取文件类型中文名称
  const getFileTypeChinese = (fileType) => {
    const typeMap = {
      'pdf': 'PDF文档',
      'word': 'Word文档',
      'excel': 'Excel表格',
      'ppt': 'PPT演示',
      'image': '图片',
      'text': '文本',
      'archive': '压缩包',
      'audio': '音频',
      'video': '视频',
      'unknown': '未知类型'
    };
    return typeMap[fileType] || '未知类型';
  };

  // 获取文件类型颜色
  const getFileTypeColor = (type) => {
    const colorMap = {
      'pdf': '#f56c6c',
      'word': '#409eff', 
      'excel': '#67c23a',
      'ppt': '#e6a23c',
      'image': '#a18cd1',
      'text': '#909399',
      'archive': '#67c23a',
      'audio': '#e6a23c',
      'video': '#f56c6c',
      'unknown': '#909399'
    };
    
    return colorMap[type] || '#909399';
  };

  // 处理文件变更
  const handleFileChange = (info) => {
    // 获取文件列表
    let newFileList = [...info.fileList];
    
    // 最多允许10个文件
    if (newFileList.length > 10) {
      message.warning('最多只能上传10个文件');
      newFileList = newFileList.slice(0, 10);
    }
    
    // 为每个文件添加实际大小并去重（根据uid和文件名）
    const uniqueFiles = [];
    const uniqueUids = new Set();
    const uniqueNames = new Set();
    
    newFileList.forEach(file => {
      // 如果文件UID已存在，则跳过
      if (uniqueUids.has(file.uid)) {
        return;
      }
      
      // 如果文件名已存在，则跳过
      if (uniqueNames.has(file.name)) {
        message.warning(`文件"${file.name}"已添加，已自动跳过重复文件`);
        return;
      }
      
      // 记录已添加的文件UID和文件名
      uniqueUids.add(file.uid);
      uniqueNames.add(file.name);
      
      // 更新文件大小
      if (file.originFileObj) {
        file.size = file.originFileObj.size;
      }
      
      uniqueFiles.push(file);
    });
    
    // 更新文件列表
    fileList.value = uniqueFiles;
  };

  // 处理文件删除
  const removeFile = (file) => {
    const index = fileList.value.findIndex(item => item.uid === file.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
      message.success('文件已移除');
    }
  };

  // 处理文件上传前的验证
  const beforeUpload = (file) => {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      message.error('文件大小不能超过50MB');
    }
    return isLt50M;
  };

  // 上传文件并保存文档
  const uploadAndSaveDocuments = async (formData, onSuccess) => {
    uploading.value = true;
    
    try {
      // 检查是否已选择文件
      if (fileList.value.length === 0) {
        message.warning('请至少上传一个文件');
        uploading.value = false;
        return;
      }
      
      // 检查表单必填项
      if (!formData.fileCategory || !formData.position) {
        message.warning('请选择文件分类和岗位名称');
        uploading.value = false;
        return;
      }
      
      const documentList = [];
      
      // 循环处理每个文件上传
      for (const file of fileList.value) {
        if (!file.originFileObj) {
          continue;
        }
        
        // 创建FormData对象
        const uploadFormData = new FormData();
        uploadFormData.append('file', file.originFileObj);
        
        // 上传文件
        const uploadResponse = await uploadFile(uploadFormData);
          
        const fileData = uploadResponse;
        
        // 构建文档对象
        const fileNameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
        
        // 使用文件分类作为文件归属
        const docData = {
          name: fileNameWithoutExt, // 使用文件名(无扩展名)作为证书名称
          fileName: fileData.fileName,
          fileType: fileData.fileType,
          fileSize: fileData.fileSize,
          fileUrl: fileData.fileUrl,
          category: formData.fileCategory, // 明确将文件分类设置为文件归属
          position: formData.position,
          certificateType: formData.fileCategory
        };
        
        console.log('待保存的文档对象:', docData);
        documentList.push(docData);
      }
      
      // 批量保存文档
      if (documentList.length > 0) {
        try {
          console.log('批量保存文档列表:', documentList);
          const saveResponse = await batchSaveDocuments({
            documents: documentList,
            commonData: {
              fileCategory: formData.fileCategory,
              position: formData.position,
            }
          });
  
          const { success, failure } = saveResponse;
          
          if (success && success.length > 0) {
            message.success(`成功保存 ${success.length} 个文档`);
          }
          
          if (failure && failure.length > 0) {
            message.warning(`${failure.length} 个文档保存失败，可能是重复文档`);
          }
          
          // 执行成功回调
          if (typeof onSuccess === 'function') {
            onSuccess();
          }
  
        } catch (error) {
          console.error('保存文档失败:', error);
          message.error('保存文档失败: ' + (error.message || '未知错误'));
        }
      } else {
        message.error('没有可保存的文档');
      }
    } catch (error) {
      console.error('上传文件失败', error);
      message.error('上传文件失败: ' + (error.message || '未知错误'));
    } finally {
      uploading.value = false;
    }
  };

  // 重置上传状态
  const resetUploadState = () => {
    currentStep.value = 0;
    uploading.value = false;
    uploadProgress.value = 0;
    uploadSuccessCount.value = 0;
    uploadFailCount.value = 0;
    fileList.value = [];
  };

  // 下一步
  const nextStep = () => {
    if (currentStep.value < 2) {
      currentStep.value += 1;
    }
  };

  // 上一步
  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value -= 1;
    }
  };

  return {
    fileList,
    uploading,
    currentStep,
    fileListWithNames,
    confirmColumns,
    formatFileSize,
    getFileType,
    getFileTypeChinese,
    getFileTypeColor,
    handleFileChange,
    removeFile,
    beforeUpload,
    uploadAndSaveDocuments,
    resetUploadState,
    nextStep,
    prevStep
  };
} 