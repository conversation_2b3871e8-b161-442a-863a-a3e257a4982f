import request from '@/utils/request';

// 获取文档段落列表
export function getDocumentSegments(documentId) {
  return request({
    url: `/knowledge-base/document/${documentId}/segments`,
    method: 'get'
  });
}

// 添加新段落
export function addSegments(documentId, segments) {
  // 数据有效性检查
  if (!documentId) {
    console.error('添加段落失败：缺少文档ID');
    return Promise.reject(new Error('缺少文档ID'));
  }
  
  if (!segments || !Array.isArray(segments) || segments.length === 0) {
    console.error('添加段落失败：段落数据无效', segments);
    return Promise.reject(new Error('段落数据无效'));
  }
  
  // 确保数据格式正确
  const formattedSegments = segments.map(segment => ({
    id: segment.id || undefined, // 对于新段落，不传id
    content: segment.content,
    keywords: Array.isArray(segment.keywords) ? segment.keywords : [],
    position: segment.position || 0
  }));
  
  console.log('API调用 - 添加段落:', { 
    documentId, 
    原始数据: segments,
    格式化数据: formattedSegments
  });
  
  // 发送请求
  return request({
    url: `/knowledge-base/document/${documentId}/segments`,
    method: 'post',
    data: { segments: formattedSegments }
  });
}

// 更新段落
export function updateSegment(documentId, segmentId, data) {
  return request({
    url: `/knowledge-base/document/${documentId}/segments/${segmentId}`,
    method: 'put',
    data
  });
}

// 删除段落
export function deleteSegment(documentId, segmentId) {
  return request({
    url: `/knowledge-base/document/${documentId}/segments/${segmentId}`,
    method: 'delete'
  });
}

// 删除Dify文档
export function deleteDifyDocument(documentId) {
  return request({
    url: `/knowledge-base/dify-document/${documentId}`,
    method: 'delete'
  });
}

// 批量保存段落 - 此方法将改为使用addSegments
export function batchUpdateSegments(documentId, data) {
  // 创建新的段落列表
  return addSegments(documentId, data.segments);
} 