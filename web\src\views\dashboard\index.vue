<template>
  <div class="dashboard-container">
    <div class="dashboard-title">
      <h1>仪表盘</h1>
      <p>查看关键绩效指标和统计数据</p>
    </div>

    <!-- 关键数据统计卡片 -->
    <a-card class="dashboard-card overview-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6" v-for="(item, index) in overviewStats" :key="index">
          <div class="overview-item">
            <div class="overview-icon" :class="`overview-icon-${index+1}`">
              <component :is="item.icon"/>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ item.value }}</div>
              <div class="overview-label">{{ item.label }}</div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 练习统计卡片 -->
    <a-card class="dashboard-card" title="练习排行TOP10" :bordered="false">
      <a-tabs v-model:activeKey="practiceActiveKey" @change="handlePracticeTabChange">
        <a-tab-pane key="daily" tab="日">
          <a-spin :spinning="loadingPracticeData">
            <practice-ranking-list :data="practiceRankingData"/>
          </a-spin>
        </a-tab-pane>
        <a-tab-pane key="weekly" tab="周">
          <a-spin :spinning="loadingPracticeData">
            <practice-ranking-list :data="practiceRankingData"/>
          </a-spin>
        </a-tab-pane>
        <a-tab-pane key="monthly" tab="月">
          <a-spin :spinning="loadingPracticeData">
            <practice-ranking-list :data="practiceRankingData"/>
          </a-spin>
        </a-tab-pane>
        <a-tab-pane key="quarterly" tab="季度">
          <a-spin :spinning="loadingPracticeData">
            <practice-ranking-list :data="practiceRankingData"/>
          </a-spin>
        </a-tab-pane>
        <a-tab-pane key="yearly" tab="年">
          <a-spin :spinning="loadingPracticeData">
            <practice-ranking-list :data="practiceRankingData"/>
          </a-spin>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 科目统计卡片 -->
    <!--    <a-card class="dashboard-card" title="科目统计" :bordered="false">
          <a-row :gutter="16">
            <a-col :span="12">
              <div ref="subjectChartRef" class="chart-container"></div>
            </a-col>
            <a-col :span="12">
              <a-table
                  :columns="subjectColumns"
                  :data-source="subjectStats.categories"
                  :pagination="false"
                  size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'topUsers'">
                    <a-tag v-for="(user, index) in record.topUsers" :key="index" class="user-tag"
                           :color="index === 0 ? '#f5222d' : index === 1 ? '#fa8c16' : '#52c41a'">
                      {{ user.name }} ({{ user.duration }}分钟)
                    </a-tag>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </a-card>-->

    <!-- 考试统计卡片 -->
    <a-card class="dashboard-card" title="考试统计" :bordered="false">
      <a-spin :spinning="loadingExamStats">
        <a-tabs v-model:activeKey="examActiveKey" @change="handleExamTabChange">
          <a-tab-pane key="monthly" tab="月">
            <exam-statistics
              :data="examStatsData.monthly"
              :certificate-ranking-data="certificateRankingData"
              :loading-certificate-data="loadingCertificateData"
            />
          </a-tab-pane>
          <a-tab-pane key="quarterly" tab="季度">
            <exam-statistics
              :data="examStatsData.quarterly"
              :certificate-ranking-data="certificateRankingData"
              :loading-certificate-data="loadingCertificateData"
            />
          </a-tab-pane>
          <a-tab-pane key="yearly" tab="年">
            <exam-statistics
              :data="examStatsData.yearly"
              :certificate-ranking-data="certificateRankingData"
              :loading-certificate-data="loadingCertificateData"
            />
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </a-card>

    <!-- 证书统计卡片 -->
    <a-card class="dashboard-card" title="科目练习情况" :bordered="false">
      <a-spin :spinning="loadingSubjectExamStats">
        <div class="subject-table-container">
          <a-table
            :columns="subjectExamColumnsOptimized"
            :data-source="subjectExamStats"
            :pagination="false"
            size="small"
            :table-layout="'fixed'"
            :scroll="{ y: 360 }"
          >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            <div class="center-content">
              <span class="index-number">{{ index + 1 }}</span>
            </div>
          </template>
          <template v-if="column.key === 'subject'">
            <div class="center-content">
              <div class="subject-name">{{ record.name }}</div>
            </div>
          </template>
          <template v-if="column.key === 'level'">
            <div class="level-tag-container">
              <a-tag color="blue">{{ record.level }}</a-tag>
            </div>
          </template>
          <template v-if="column.key === 'practice'">
            <div class="practice-info">
              <div>{{ record.avgDuration }}分钟</div>
              <div class="text-muted">{{ record.avgCount }}题</div>
            </div>
          </template>
          <template v-if="column.key === 'topStudents'">
            <div class="center-content">
              <div class="top-students-container">
                <a-tag v-for="(student, index) in record.topStudents.slice(0, 3)" :key="index" class="user-tag"
                       :color="index === 0 ? '#f5222d' : index === 1 ? '#fa8c16' : '#52c41a'">
                  {{ student.name }} ({{ student.duration }}分钟)
                </a-tag>
              </div>
            </div>
          </template>
        </template>
      </a-table>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue';
import {
  practiceStats,
  subjectStats,
  certificateStats,
  examStats
} from '@/mock/dashboard';
import PracticeRankingList from './components/PracticeRankingList.vue';
import ExamStatistics from './components/ExamStatistics.vue';
import * as echarts from 'echarts';
import {UserOutlined, BookOutlined, SolutionOutlined, SafetyCertificateOutlined} from '@ant-design/icons-vue';
import {getDashboardOverview, getPracticeRanking, getCertificateRanking, getSubjectExamStats, getExamStatistics, getPositionPassRates} from '@/api/dashboard';
import {message} from 'ant-design-vue';

// 概览统计数据
const overviewStats = ref([
  {
    label: '员工人数',
    value: 0,
    icon: UserOutlined
  },
  {
    label: '知识库数量',
    value: 0,
    icon: BookOutlined
  },
  {
    label: '待审核考试',
    value: 0,
    icon: SolutionOutlined
  },
  {
    label: '待确认证书',
    value: 0,
    icon: SafetyCertificateOutlined
  }
]);

// 获取仪表盘概览数据
const fetchOverviewData = async () => {
  try {
    const res = await getDashboardOverview();
    console.log("res", res)
    if (res) {
      overviewStats.value = [
        {
          label: '员工人数',
          value: res.employeeCount || 0,
          icon: UserOutlined
        },
        {
          label: '知识库数量',
          value: res.knowledgeBaseCount || 0,
          icon: BookOutlined
        },
        {
          label: '待审核考试',
          value: res.pendingExamCount || 0,
          icon: SolutionOutlined
        },
        {
          label: '待确认证书',
          value: res.pendingCertificateCount || 0,
          icon: SafetyCertificateOutlined
        }
      ];
      console.log("overviewStats.value", overviewStats.value);
    }
  } catch (error) {
    message.error('获取仪表盘概览数据失败');
    console.error('获取仪表盘概览数据失败:', error);
  }
};

// 练习统计相关
const practiceActiveKey = ref('daily');
const practiceRankingData = ref([]);
const loadingPracticeData = ref(false);

// 获取练习排行数据
const fetchPracticeRankingData = async (periodType = 'daily') => {
  try {
    loadingPracticeData.value = true;
    const res = await getPracticeRanking({ periodType });
    if (res) {
      practiceRankingData.value = res;
    } else {
      practiceRankingData.value = [];
    }
  } catch (error) {
    message.error('获取练习排行数据失败');
    console.error('获取练习排行数据失败:', error);
    practiceRankingData.value = [];
  } finally {
    loadingPracticeData.value = false;
  }
};

// 切换练习排行时间周期
const handlePracticeTabChange = (key) => {
  fetchPracticeRankingData(key);
};

// 证书排行榜相关
const certificateRankingData = ref([]);
const loadingCertificateData = ref(false);

// 获取证书排行榜数据
const fetchCertificateRankingData = async () => {
  try {
    loadingCertificateData.value = true;
    const res = await getCertificateRanking();
    if (res) {
      certificateRankingData.value = res;
    } else {
      certificateRankingData.value = [];
    }
    console.log("certificateRankingData.value", certificateRankingData.value);
  } catch (error) {
    message.error('获取证书排行榜数据失败');
    console.error('获取证书排行榜数据失败:', error);
    certificateRankingData.value = [];
  } finally {
    loadingCertificateData.value = false;
  }
};

// 科目统计相关
const subjectChartRef = ref(null);
const subjectColumns = [
  {title: '科目名称', dataIndex: 'name', key: 'name'},
  {title: '级别', dataIndex: 'level', key: 'level'},
  {title: '平均练习时长', dataIndex: 'avgDuration', key: 'avgDuration'},
  {title: '平均练习题量', dataIndex: 'avgCount', key: 'avgCount'},
  {title: '学习最多的学员', key: 'topUsers'}
];

// 科目练习和考试统计数据
const subjectExamStats = ref([]);
const loadingSubjectExamStats = ref(false);

// 获取科目练习和考试统计数据
const fetchSubjectExamStats = async () => {
  try {
    loadingSubjectExamStats.value = true;
    const res = await getSubjectExamStats();
    if (res) {
      subjectExamStats.value = res;
    } else {
      subjectExamStats.value = [];
      message.error('获取科目练习和考试统计数据失败');
    }
  } catch (error) {
    console.error('获取科目练习和考试统计数据失败:', error);
    message.error('获取科目练习和考试统计数据失败');
    subjectExamStats.value = [];
  } finally {
    loadingSubjectExamStats.value = false;
  }
};

// 证书排行榜列定义
const certificateColumns = [
  {title: '排名', key: 'rank', width: 70, align: 'center'},
  {title: '姓名', dataIndex: 'name', key: 'name', width: 80},
  {title: '岗位', key: 'position', width: 110},
  {title: '等级', key: 'level', width: 80, align: 'center'},
  {title: '证书数量', key: 'progress', width: 180}
];

// 新增：科目练习和考试情况列定义
const subjectExamColumns = [
  {title: '科目名称', dataIndex: 'name', key: 'name'},
  {title: '考核等级', dataIndex: 'level', key: 'level'},
  {title: '平均练习时长', dataIndex: 'avgDuration', key: 'avgDuration', align: 'center'},
  {title: '平均练习题数', dataIndex: 'avgCount', key: 'avgCount', align: 'center'},
  {title: '考试通过率', dataIndex: 'passRate', key: 'passRate', align: 'center'},
  {title: '学习最多的学员', key: 'topStudents'}
];

// 优化后的科目练习和考试情况列定义
const subjectExamColumnsOptimized = [
  {title: '序号', key: 'index', width: '8%', align: 'center'},
  {title: '科目信息', key: 'subject', width: '20%'},
  {title: '岗位等级', key: 'level', width: '10%', align: 'center'},
  {title: '练习情况', key: 'practice', width: '15%', align: 'center'},
  {title: '学习最多的学员', key: 'topStudents', width: '47%'}
];

// 考试统计相关
const examActiveKey = ref('monthly');
const examStatsData = ref({
  monthly: {
    passCount: 0,
    totalCount: 0,
    passRate: 0,
    positionRates: [],
    trend: []
  },
  quarterly: {
    passCount: 0,
    totalCount: 0,
    passRate: 0,
    positionRates: [],
    trend: []
  },
  yearly: {
    passCount: 0,
    totalCount: 0,
    passRate: 0,
    positionRates: [],
    trend: []
  }
});
const loadingExamStats = ref(false);

// 获取考试统计数据
const fetchExamStatistics = async (periodType = 'monthly') => {
  try {
    loadingExamStats.value = true;

    // 并行获取考试统计数据和岗位通过率数据
    const [examStatsRes, positionRatesRes] = await Promise.all([
      getExamStatistics({ periodType }),
      getPositionPassRates({ periodType })
    ]);
    console.log("examStatsRes"+examStatsRes)
    console.log("positionRatesRes"+positionRatesRes)

    if (examStatsRes && examStatsRes.summary && examStatsRes.trend) {
      examStatsData.value[periodType] = {
        passCount: examStatsRes.summary.passedExamCount,
        totalCount: examStatsRes.summary.totalExamCount,
        passRate: examStatsRes.summary.passRate,
        positionRates: positionRatesRes.positionRates || [], // 使用后端返回的岗位通过率数据
        trend: examStatsRes.trend
      };
    } else {
      message.error('获取考试统计数据失败');
    }
  } catch (error) {
    console.error('获取考试统计数据失败:', error);
    message.error('获取考试统计数据失败');
  } finally {
    loadingExamStats.value = false;
  }
};

// 切换考试统计时间周期
const handleExamTabChange = (key) => {
  examActiveKey.value = key;
  fetchExamStatistics(key);
};

// 证书统计相关
const certTypeChartRef = ref(null);

const getAvatarColor = (index) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#8470FF'];
  return colors[index % colors.length];
};

const getProgressColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#52c41a', '#1890ff', '#722ed1'];
  return colors[index % colors.length];
};

// 获取岗位对应的颜色
const getPositionColor = (position) => {
  const positionColors = {
    '前厅经理': '#1890ff',
    '厨师长': '#13c2c2',
    '服务员': '#52c41a',
    '厨师': '#fa8c16',
    '收银员': '#eb2f96',
    '迎宾员': '#722ed1',
    '后厨助理': '#faad14',
    '烧烤师': '#fa8c16' // 添加烧烤师岗位颜色
  };
  return positionColors[position] || '#1890ff';
};

const getMaxCertCount = (position) => {
  // 找到对应职位的最大证书数量
  const positionData = certificateStats.topHolders.find(item => item.position === position);
  if (positionData && positionData.top5.length > 0) {
    return positionData.top5[0].count;
  }
  return 10; // 默认值
};

onMounted(() => {
  // 获取仪表盘概览数据
  fetchOverviewData();

  // 获取练习排行数据
  fetchPracticeRankingData(practiceActiveKey.value);

  // 获取证书排行榜数据
  fetchCertificateRankingData();

  // 获取科目练习和考试统计数据
  fetchSubjectExamStats();

  // 获取考试统计数据
  fetchExamStatistics(examActiveKey.value);

  // 初始化科目统计图表
  if (subjectChartRef.value) {
    const chart = echarts.init(subjectChartRef.value);
    const subjectNames = subjectStats.categories.map(item => item.name);
    const subjectDurations = subjectStats.categories.map(item => item.avgDuration);
    const subjectCounts = subjectStats.categories.map(item => item.avgCount);

    const option = {
      title: {
        text: '科目练习情况'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        }
      },
      legend: {
        data: ['平均时长(分钟)', '平均题量']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: subjectNames
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '平均时长(分钟)',
          type: 'bar',
          data: subjectDurations,
          itemStyle: {
            color: '#a18cd1'
          }
        },
        {
          name: '平均题量',
          type: 'bar',
          data: subjectCounts,
          itemStyle: {
            color: '#fbc2eb'
          }
        }
      ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => {
      chart.resize();
    });
  }

  // 初始化证书类型分布图表
  if (certTypeChartRef.value) {
    const chart = echarts.init(certTypeChartRef.value);
    const option = {
      title: {
        text: '证书类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      series: [
        {
          name: '证书类型',
          type: 'pie',
          radius: '70%',
          data: certificateStats.typesDistribution.map(item => ({
            value: item.count,
            name: item.type
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            color: function (params) {
              const colorList = ['#a18cd1', '#fbc2eb', '#8fd3f4'];
              return colorList[params.dataIndex % colorList.length];
            }
          }
        }
      ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => {
      chart.resize();
    });
  }
});
</script>

<style scoped>
.dashboard-container {
  /* padding: 24px; */
  overflow: auto;
  height: 100%;
}

.dashboard-title {
  margin-bottom: 24px;
}

.dashboard-title h1 {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 24px;
  color: var(--text-primary);
}

.dashboard-title p {
  color: var(--text-secondary);
  margin: 0;
}

.dashboard-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

/* 概览卡片样式 */
.overview-card {
  margin-bottom: 24px;
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--card-bg-color, #fff);
  border-radius: 8px;
  transition: all 0.3s;
}

.overview-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.overview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-right: 16px;
  color: #fff;
  font-size: 24px;
}

.overview-icon-1 {
  background: linear-gradient(135deg, #36d1dc, #5b86e5);
}

.overview-icon-2 {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}

.overview-icon-3 {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
}

.overview-icon-4 {
  background: linear-gradient(135deg, #84fab0, #8fd3f4);
}

.overview-content {
  flex: 1;
}

.overview-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary, #000);
  line-height: 1.2;
}

.overview-label {
  font-size: 14px;
  color: var(--text-secondary, #666);
  margin-top: 4px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.user-tag {
  margin: 4px;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

.cert-overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.cert-stat-item {
  background: var(--card-bg-color);
  padding: 16px;
  border-radius: 8px;
  flex: 1 1 calc(33.33% - 16px);
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cert-stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.cert-stat-label {
  color: var(--text-secondary);
  font-size: 14px;
}

.cert-ranking-list .ant-list-item {
  padding: 12px 0;
}

.cert-progress {
  width: 200px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
  border-left: 4px solid var(--primary-color, #1890ff);
  padding-left: 10px;
}

.cert-holder-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 新增样式 */
.top-ranked-row {
  background-color: rgba(24, 144, 255, 0.05);
}

.cert-progress-compact {
  width: 160px;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.subject-name {
  font-weight: 500;
  text-align: center;
}

.level-tag-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.practice-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.text-muted {
  color: var(--text-secondary, #999);
  font-size: 12px;
}

.top-students-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* 表格自定义样式 */
:deep(.ant-table-small) {
  font-size: 13px;
}

:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  text-align: center;
}

:deep(.ant-table-small .ant-table-tbody > tr > td) {
  text-align: center;
}

:deep(.ant-table-small .ant-table-thead > tr > th .ant-table-header-column) {
  display: flex;
  justify-content: center;
}

:deep(.ant-table-row:hover) {
  cursor: pointer;
}

/* 自定义 ant-card 头部样式 */
:deep(.ant-card-head) {
  background: var(--gradient-color);
}

:deep(.ant-card-head-title) {
  color: white;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;
}

.index-number {
  font-weight: 500;
  color: var(--text-primary, #000);
  font-size: 14px;
}

/* 添加科目统计表格容器样式 */
.subject-table-container {
  max-height: 400px;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.subject-table-container::-webkit-scrollbar {
  width: 6px;
}

.subject-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.subject-table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.subject-table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
