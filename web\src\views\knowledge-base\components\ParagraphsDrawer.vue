<template>
  <a-drawer
    :visible="visible"
    :title="`${documentName || '文档'} - 段落管理`"
    width="700"
    @close="onClose"
    :body-style="{ paddingBottom: '80px' }"
    destroy-on-close
  >
    <div class="paragraphs-container">
      <div class="operations">
        <a-space>
          <a-button type="primary" @click="handleAddParagraph">
            <plus-outlined /> 添加段落
          </a-button>
          <a-button @click="handleRefresh">
            <reload-outlined /> 刷新数据
          </a-button>
        </a-space>
      </div>

      <div class="empty-state" v-if="paragraphs.length === 0">
        <a-empty description="暂无段落数据">
          <template #description>
            <span>暂无段落数据，点击上方"添加段落"按钮创建新段落</span>
          </template>
          <a-button type="primary" @click="handleAddParagraph">
            <plus-outlined /> 添加段落
          </a-button>
        </a-empty>
      </div>

      <div v-else class="paragraphs-list">
        <div v-for="(paragraph, index) in paragraphs" :key="paragraph.id || index" class="paragraph-item">
          <!-- 非编辑模式 -->
          <template v-if="!paragraph.isEditing">
            <div class="paragraph-header">
              <div class="paragraph-index">段落 #{{ index + 1 }}</div>
              <div class="paragraph-actions">
                <a-button type="text" @click="startEditParagraph(index)">
                  <edit-outlined /> 编辑
                </a-button>
                <a-popconfirm
                  title="确定要删除这个段落吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDeleteParagraph(index)"
                >
                  <a-button type="text" danger>
                    <delete-outlined /> 删除
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
            <div class="paragraph-content">
              {{ paragraph.content }}
            </div>
            <div class="paragraph-keywords" v-if="paragraph.keywords && paragraph.keywords.length > 0">
              <div class="keywords-title">关键词：</div>
              <div class="keywords-list">
                <a-tag v-for="(keyword, kIndex) in paragraph.keywords" 
                  :key="kIndex" 
                  color="blue"
                  class="keyword-tag">
                  {{ keyword }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 编辑模式 -->
          <template v-else>
            <div class="paragraph-header">
              <div class="paragraph-index">编辑段落 #{{ index + 1 }}</div>
              <div class="paragraph-actions">
                <a-button type="primary" size="small" @click="saveParagraphChanges(index)">
                  <save-outlined /> 保存
                </a-button>
                <a-button type="default" size="small" @click="cancelEditParagraph(index)">
                  取消
                </a-button>
              </div>
            </div>
            <div class="paragraph-edit-form">
              <a-form layout="vertical">
                <a-form-item label="段落内容" required>
                  <a-textarea 
                    v-model:value="paragraph.editingContent" 
                    :rows="4" 
                    placeholder="请输入段落内容"
                  />
                </a-form-item>
                <a-form-item label="关键词">
                  <a-select
                    v-model:value="paragraph.editingKeywords"
                    mode="tags"
                    placeholder="请输入关键词，按回车键确认"
                    style="width: 100%"
                    :token-separators="[',']"
                  ></a-select>
                  <!-- <div class="keyword-tips">
                    <a-button type="link" size="small" @click="autoGenerateKeywordsForParagraph(index)">
                      自动生成关键词
                    </a-button>
                  </div> -->
                </a-form-item>
              </a-form>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 段落编辑弹窗 - 仅用于添加新段落 -->
    <a-modal
      :visible="editModalVisible"
      title="添加段落"
      @ok="saveNewParagraph"
      @cancel="editModalVisible = false"
      width="600px"
    >
      <a-form :model="currentParagraph" layout="vertical">
        <a-form-item label="段落内容" name="content" :rules="[{ required: true, message: '请输入段落内容' }]">
          <a-textarea
            v-model:value="currentParagraph.content"
            :rows="6"
            placeholder="请输入段落内容"
          ></a-textarea>
        </a-form-item>
        
        <a-form-item label="关键词" name="keywords">
          <a-select
            v-model:value="currentKeywords"
            mode="tags"
            placeholder="请输入关键词，按回车键确认"
            style="width: 100%"
            :token-separators="[',']"
          ></a-select>
          
          <!-- <div class="keyword-tips">
            <a-button type="link" size="small" @click="autoGenerateKeywords">
              自动生成关键词
            </a-button>
            <span class="tip-text">关键词用于文档检索和分类</span>
          </div> -->
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 抽屉底部按钮 -->
    <div class="drawer-footer">
      <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  documentId: {
    type: String,
    default: ''
  },
  documentName: {
    type: String,
    default: ''
  },
  initialParagraphs: {
    type: Array,
    default: () => []
  },
  fullscreenLoading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'save', 'refresh', 'delete', 'setLoading']);

// 段落数据
const paragraphs = ref([]);
const editModalVisible = ref(false);
const currentParagraph = ref({ content: '' });
const currentKeywords = ref([]);
const currentIndex = ref(-1);
const isNewParagraph = ref(false);

// 监听初始段落数据变化
watch(() => props.initialParagraphs, (newVal) => {
  // 处理初始数据，添加编辑状态属性
  paragraphs.value = (JSON.parse(JSON.stringify(newVal)) || []).map(p => ({
    ...p,
    isEditing: false,
    editingContent: p.content,
    editingKeywords: p.keywords ? [...p.keywords] : []
  }));
}, { immediate: true, deep: true });

// 关闭抽屉
const onClose = () => {
  // 如果有未保存的编辑，提示用户
  const hasUnsavedChanges = paragraphs.value.some(p => p.isEditing);
  if (hasUnsavedChanges) {
    if (confirm('有未保存的修改，确定要关闭吗？')) {
      emit('update:visible', false);
    }
  } else {
    emit('update:visible', false);
  }
};

// 添加段落
const handleAddParagraph = () => {
  // 清空当前段落和关键词
  currentParagraph.value = { content: '' };
  currentKeywords.value = [];
  currentIndex.value = -1;
  isNewParagraph.value = true;
  editModalVisible.value = true;
};

// 开始编辑段落
const startEditParagraph = (index) => {
  const paragraph = paragraphs.value[index];
  // 标记为编辑模式，并设置编辑内容
  paragraph.isEditing = true;
  paragraph.editingContent = paragraph.content;
  paragraph.editingKeywords = paragraph.keywords ? [...paragraph.keywords] : [];
};

// 取消编辑段落
const cancelEditParagraph = (index) => {
  paragraphs.value[index].isEditing = false;
};

// 保存段落修改
const saveParagraphChanges = (index) => {
  const paragraph = paragraphs.value[index];
  
  if (!paragraph.editingContent || !paragraph.editingContent.trim()) {
    message.error('段落内容不能为空');
    return;
  }
  
  // 更新段落内容和关键词
  paragraph.content = paragraph.editingContent;
  paragraph.keywords = paragraph.editingKeywords.length > 0 
    ? [...paragraph.editingKeywords] 
    : generateKeywords(paragraph.editingContent);
  
  // 退出编辑模式
  paragraph.isEditing = false;
  
  // 显示操作成功消息
  
  
  // 只发送当前编辑的段落，不发送所有段落
  const editedSegment = {
    id: paragraph.id,
    content: paragraph.content,
    keywords: paragraph.keywords,
    position: paragraph.position
  };
  
  // 传递单个段落进行保存，增加singleSegment标识符
  emit('save', [editedSegment], true); // 第二个参数表示这是单个段落更新
};

// 为特定段落自动生成关键词
const autoGenerateKeywordsForParagraph = (index) => {
  const paragraph = paragraphs.value[index];
  
  if (!paragraph.editingContent) {
    message.warning('请先输入段落内容');
    return;
  }
  
  const generatedKeywords = generateKeywords(paragraph.editingContent);
  if (generatedKeywords.length > 0) {
    paragraph.editingKeywords = generatedKeywords;
    message.success('已自动生成关键词');
  } else {
    message.warning('无法从当前内容生成关键词');
  }
};

// 编辑段落（此方法仅用于餐考，不再使用）
const handleEditParagraph = (index) => {
  const paragraph = paragraphs.value[index];
  // 设置当前段落内容，但不包含关键词
  currentParagraph.value = { 
    id: paragraph.id,
    content: paragraph.content,
    position: paragraph.position
  };
  
  // 单独设置关键词
  currentKeywords.value = paragraph.keywords ? [...paragraph.keywords] : [];
  
  currentIndex.value = index;
  isNewParagraph.value = false;
  editModalVisible.value = true;
};

// 修改删除段落函数，直接调用删除API
const handleDeleteParagraph = (index) => {
  const paragraph = paragraphs.value[index];
  
  // 检查是否有有效的段落ID
  if (!paragraph.id || paragraph.id.startsWith('temp_')) {
    // 临时段落没有保存到服务器，直接从UI上移除即可
    paragraphs.value.splice(index, 1);
    message.success('段落已删除');
    return;
  }
  
  // 通知父组件显示加载状态
  emit('setLoading', true);
  
  // 调用删除API
  emit('delete', paragraph.id);
};

// 保存新段落
const saveNewParagraph = () => {
  if (!currentParagraph.value.content.trim()) {
    message.error('段落内容不能为空');
    return;
  }

  // 创建一个新对象，包含段落内容和关键词
  const paragraphToSave = {
    ...currentParagraph.value,
    id: `temp_${Date.now()}`,  // 临时ID，保存时后端会分配真实ID
    keywords: currentKeywords.value.length > 0 
      ? [...currentKeywords.value] 
      : generateKeywords(currentParagraph.value.content),
    isEditing: false,
    editingContent: currentParagraph.value.content,
    editingKeywords: currentKeywords.value.length > 0 
      ? [...currentKeywords.value] 
      : generateKeywords(currentParagraph.value.content)
  };

  // 添加新段落到UI
  paragraphs.value.push(paragraphToSave);
  
  // 显示操作成功消息
  message.success('段落已添加');
  editModalVisible.value = false;
  
  // 只发送新添加的段落，而不是所有段落
  const newSegment = {
    id: paragraphToSave.id,
    content: paragraphToSave.content,
    keywords: paragraphToSave.keywords,
    position: paragraphToSave.position
  };
  
  // 传递新段落进行保存，并标记为新段落
  emit('save', [newSegment], false, true); // 第三个参数表示这是新增段落
};

// 自动生成关键词
const autoGenerateKeywords = () => {
  if (!currentParagraph.value.content) {
    message.warning('请先输入段落内容');
    return;
  }
  
  const generatedKeywords = generateKeywords(currentParagraph.value.content);
  if (generatedKeywords.length > 0) {
    // 直接替换关键词数组，而不是修改currentParagraph
    currentKeywords.value = generatedKeywords;
    message.success('已自动生成关键词');
  } else {
    message.warning('无法从当前内容生成关键词');
  }
};

// 简单的关键词生成函数
const generateKeywords = (content) => {
  if (!content) return [];
  // 简单实现：取内容中的一些词作为关键词（实际应用中可使用NLP技术）
  const words = content.split(/[,，.。:：\s\n]/).filter(w => w.length >= 2 && w.length <= 6);
  const uniqueWords = [...new Set(words)];
  return uniqueWords.slice(0, Math.min(5, uniqueWords.length));
};

// 添加刷新功能
const handleRefresh = () => {
  emit('refresh');
  message.success('正在刷新数据...');
};
</script>

<style lang="scss" scoped>
.paragraphs-container {
  .operations {
    margin-bottom: 20px;
    
    .ant-space {
      gap: 8px;
    }
  }

  .paragraphs-list {
    max-height: calc(100vh - 240px);
    overflow-y: auto;
  }

  .paragraph-item {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: #f5f7fa;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .paragraph-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebedf0;

      .paragraph-index {
        font-weight: 500;
      }

      .paragraph-actions {
        display: flex;
        gap: 8px;
      }
    }

    .paragraph-content {
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.6;
    }
    
    .paragraph-keywords {
      margin-top: 12px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      
      .keywords-title {
        font-size: 14px;
        color: #606266;
        margin-right: 8px;
      }
      
      .keywords-list {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        
        .keyword-tag {
          margin-right: 0;
        }
      }
    }
    
    .paragraph-edit-form {
      margin-top: 8px;
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.keyword-tips {
  display: flex;
  align-items: center;
  margin-top: 8px;
  
  .tip-text {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
  }
}
</style> 