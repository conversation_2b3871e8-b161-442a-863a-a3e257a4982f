import request from '@/utils/request'

/**
 * 获取餐烤师配置列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getRestaurantConfigList(params) {
  return request({
    url: '/restaurant-config',
    method: 'get',
    params
  })
}

/**
 * 获取餐烤师配置详情
 * @param {Number} id 配置ID
 * @returns {Promise}
 */
export function getRestaurantConfigDetail(id) {
  return request({
    url: `/restaurant-config/${id}`,
    method: 'get'
  })
}

/**
 * 创建餐烤师配置
 * @param {Object} data 餐烤师配置数据
 * @returns {Promise}
 */
export function createRestaurantConfig(data) {
  return request({
    url: '/restaurant-config',
    method: 'post',
    data
  })
}

/**
 * 更新餐烤师配置
 * @param {Number} id 配置ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export function updateRestaurantConfig(id, data) {
  return request({
    url: `/restaurant-config/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除餐烤师配置
 * @param {Number} id 配置ID
 * @returns {Promise}
 */
export function deleteRestaurantConfig(id) {
  return request({
    url: `/restaurant-config/${id}`,
    method: 'delete'
  })
}

/**
 * 上传餐烤师头像
 * @param {File} file 图片文件
 * @returns {Promise}
 */
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/restaurant-config/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取岗位类型列表（用于下拉框选择）
 * @returns {Promise}
 */
export function getPositionTypeList() {
  return request({
    url: '/position-types',
    method: 'get'
  })
} 