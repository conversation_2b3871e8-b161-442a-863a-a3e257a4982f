# PageHeader 页面头部组件

## 功能介绍

`PageHeader` 是一个通用的页面头部组件，用于展示页面标题、描述以及页面级别的操作按钮。

## 组件属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
| --- | --- | --- | --- | --- |
| title | String | 是 | '' | 页面标题 |
| description | String | 否 | '' | 页面描述文本 |

## 插槽

| 名称 | 说明 |
| --- | --- |
| actions | 右侧操作区域，通常放置添加、导出等按钮 |

## 使用示例

```vue
<template>
  <page-header 
    title="岗位等级" 
    description="管理系统中的岗位等级配置"
  >
    <template #actions>
      <a-button type="primary" @click="handleAdd">
        <template #icon><plus-outlined /></template>
        新增
      </a-button>
    </template>
  </page-header>
</template>

<script setup>
import PageHeader from '@/components/PageHeader';

const handleAdd = () => {
  // 处理添加逻辑
};
</script>
``` 