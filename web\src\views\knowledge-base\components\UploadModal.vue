<template>
  <a-modal
      v-model:visible="visible"
      :title="documentData ? '编辑文档' : '上传文档'"
      @cancel="handleCancel"
      width="800px"
      :maskClosable="false"
      :destroyOnClose="true"
      class="upload-document-modal"
  >
    <a-steps v-if="!documentData" :current="currentStep" size="small" class="upload-steps">
      <a-step title="填写信息" />
      <a-step title="上传文件" />
      <a-step title="确认提交" />
    </a-steps>

    <div class="step-content">
      <!-- 步骤一：填写基本信息 -->
      <div v-if="!documentData && currentStep === 0" class="step-form">
        <a-form
            :model="formData"
            :rules="formRules"
            ref="formRef"
            layout="vertical"
        >
          <a-form-item label="选择岗位" name="positionCascader" :rules="[{ required: true, message: '请选择岗位分类' }]">
            <a-cascader
                v-model:value="formData.positionCascader"
                :options="cascaderOptions"
                placeholder="请选择岗位分类"
                :disabled="!!documentData"
                @change="handlePositionCascaderChange"
                style="width: 100%"
            />
          </a-form-item>

        </a-form>
      </div>

      <!-- 步骤二：上传文件 -->
      <div v-if="!documentData && currentStep === 1" class="step-upload">
        <div class="upload-zone">
          <a-upload-dragger
              :multiple="true"
              :max-count="10"
              :fileList="fileList"
              @change="handleFileChange"
              @reject="handleReject"
              :before-upload="beforeUpload"
              :showUploadList="false"
              :customRequest="handleCustomUpload"
              accept=".docx,.xlsx"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个或批量上传，最多10个文件，单个文件不超过15MB
            </p>
            <div class="upload-requirements">
              <p class="upload-requirements-title">上传要求：</p>
              <ul>
                <li>支持格式：Word(.docx)、Excel(.xlsx)</li>
                <li>容量限制：单个文件大小 ≤15MB</li>
                <li>Excel要求：单个sheet页数据行数 ≤2000条，sheet总数 ≤10个</li>
              </ul>
            </div>
          </a-upload-dragger>
        </div>

        <div class="upload-file-list" v-if="fileList.length > 0">
          <h3>已选择文件 ({{ fileList.length }}/10)</h3>
          <a-list
              size="small"
              :dataSource="fileList"
              :bordered="false"
              class="file-list"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <div class="file-item">
                  <file-pdf-outlined v-if="getFileType(item.name) === 'pdf'" class="file-icon pdf" />
                  <file-word-outlined v-else-if="getFileType(item.name) === 'word'" class="file-icon word" />
                  <file-excel-outlined v-else-if="getFileType(item.name) === 'excel'" class="file-icon excel" />
                  <file-ppt-outlined v-else-if="getFileType(item.name) === 'ppt'" class="file-icon ppt" />
                  <file-image-outlined v-else-if="getFileType(item.name) === 'image'" class="file-icon image" />
                  <file-text-outlined v-else-if="getFileType(item.name) === 'text'" class="file-icon text" />
                  <file-zip-outlined v-else-if="getFileType(item.name) === 'archive'" class="file-icon archive" />
                  <audio-outlined v-else-if="getFileType(item.name) === 'audio'" class="file-icon audio" />
                  <video-camera-outlined v-else-if="getFileType(item.name) === 'video'" class="file-icon video" />
                  <file-outlined v-else class="file-icon" />
                  <span class="file-name">{{ item.name }}</span>
                  <a-button type="text" danger @click="removeFile(item)" class="remove-file">
                    <delete-outlined />
                  </a-button>
                </div>
              </a-list-item>
            </template>
          </a-list>
        </div>


      </div>

      <!-- 步骤三：确认提交 -->
      <div v-if="!documentData && currentStep === 2" class="step-confirm">
        <div class="confirm-content">
          <h3>上传确认</h3>
          <div class="confirm-info">
            <p><strong>岗位分类：</strong>{{ selectedCategoryText }}</p>
            <p><strong>岗位名称：</strong>{{ selectedPositionText }}</p>
            <p><strong>上传文件数量：</strong>{{ fileList.length }}个</p>
          </div>

          <div class="confirm-files">
            <h4>文件列表：</h4>
            <a-table
                size="small"
                :columns="confirmColumns"
                :dataSource="fileListWithNames"
                :pagination="false"
                :bordered="true"
            />
          </div>
        </div>

      </div>

      <!-- 编辑模式 -->
      <div v-if="documentData" class="edit-form">
        <a-form
            :model="formData"
            ref="formRef"
            layout="vertical"
        >
          <a-form-item label="文件名称">
            <span>{{ formData.fileName }}</span>
          </a-form-item>

          <a-form-item label="文件分类">
            <span>{{ getCategoryLabelById(formData.fileCategory) }}</span>
          </a-form-item>

          <a-form-item label="岗位名称">
            <span>{{ getPositionLabelById(formData.position) }}</span>
          </a-form-item>

          <a-form-item label="证书名称（限制10个字）" name="name" :rules="[{ required: true, message: '请输入证书名称' }, { max: 10, message: '证书名称不能超过10个字符' }]">
            <a-input v-model:value="formData.name" placeholder="请输入证书名称" maxlength="10" />
          </a-form-item>

          <div class="edit-actions">
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleEditSubmit">保存</a-button>
          </div>
        </a-form>
      </div>
    </div>
    <template #footer>
      <div class="step-actions">
        <a-button @click="handleCancel" v-if="!documentData&&currentStep===0">取消</a-button>
        <a-button @click="prevStep" v-if="!documentData&&currentStep!==0">上一步</a-button>
        <a-button type="primary" v-if="!documentData&&currentStep===0" @click="nextStep">下一步</a-button>
        <a-button type="primary" v-if="!documentData&&currentStep===1" @click="nextStep" :disabled="fileList.length === 0">下一步</a-button>
        <a-button type="primary" v-if="!documentData && currentStep===2" @click="handleUploadSubmit" :loading="uploading">
          {{ uploading ? '上传中...' : '提交上传' }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  InboxOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileZipOutlined,
  AudioOutlined,
  VideoCameraOutlined,
  FileOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { getPositionTypeOptions, getPositionNameOptions } from '@/api/organization/position';
import {
  uploadFile,
  batchSaveDocuments,
  updateKnowledgeDocument
} from '@/api/knowledge-base';
import * as XLSX from 'xlsx';

// 接收props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  documentData: {
    type: Object,
    default: null
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'success']);

// 同步visible属性
const visible = ref(props.visible);

// 监听props中的visible变化
watch(() => props.visible, (newValue) => {
  visible.value = newValue;
  if (newValue && props.documentData) {
    // 编辑模式
    initEditForm();
  } else if (newValue) {
    // 新增模式：重置表单
    resetForm();
    currentStep.value = 0;
  }
});

// 监听内部visible变化
watch(visible, (newValue) => {
  emit('update:visible', newValue);
});

// 表单引用
const formRef = ref(null);

// 岗位类型选项和岗位名称选项
const positionTypeOptions = ref([]);
const positionNameOptions = ref([]);

// 分步上传相关变量
const currentStep = ref(0);
const uploading = ref(false);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  fileName: '',
  fileType: '',
  fileCategory: '',
  position: '',
  positionLevel: '',
  certificateType: '',
  positionCascader: [], // 级联选择器的值数组 [typeId, nameId]
  documentType: 'exam' // 新增文档类型字段，默认为练考类型
});

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入知识库名称', trigger: 'blur' }],
  positionCascader: [{ required: true, message: '请选择岗位分类', trigger: 'change' }]
};

// 确认列表的表格列
const confirmColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60
  },
  {
    title: '文件名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '类型',
    dataIndex: 'fileType',
    key: 'fileType',
    width: 100
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
    width: 100
  }
];

// 获取文件类型
const getFileType = (fileName) => {
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

  // 文档类型
  if (['pdf'].includes(ext)) {
    return 'pdf';
  } else if (['docx'].includes(ext)) {
    return 'word';
  } else if (['xlsx'].includes(ext)) {
    return 'excel';
  } else if (['ppt', 'pptx'].includes(ext)) {
    return 'ppt';
  }
  // 图片类型
  else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
    return 'image';
  }
  // 文本类型
  else if (['txt', 'log', 'md', 'json', 'xml', 'html', 'css', 'js'].includes(ext)) {
    return 'text';
  }
  // 压缩文件
  else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'archive';
  }
  // 音视频文件
  else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
    return 'audio';
  }
  else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
    return 'video';
  }

  return 'unknown';
};

// 文件列表带有序号和格式化的大小
const fileListWithNames = computed(() => {
  return fileList.value.map((file, index) => {
    const fileType = getFileType(file.name);
    return {
      key: index,
      index: index + 1,
      name: file.name,
      fileType: getFileTypeChinese(fileType),
      size: formatFileSize(file.size || 0)
    };
  });
});

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件类型中文名称
const getFileTypeChinese = (fileType) => {
  const typeMap = {
    'pdf': 'PDF文档',
    'word': 'Word文档',
    'excel': 'Excel表格',
    'ppt': 'PPT演示',
    'image': '图片',
    'text': '文本',
    'archive': '压缩包',
    'audio': '音频',
    'video': '视频',
    'unknown': '未知类型'
  };
  return typeMap[fileType] || '未知类型';
};

// 级联选择器选项
const cascaderOptions = computed(() => {
  // 确保类型选项和名称选项都是数组
  if (!Array.isArray(positionTypeOptions.value) || !Array.isArray(positionNameOptions.value)) {
    console.warn('岗位选项数据不是数组:',
        {types: positionTypeOptions.value, names: positionNameOptions.value});
    return [];
  }

  // 打印级联选择器数据结构
  console.log('构建级联选择器，类型选项数量:', positionTypeOptions.value.length);
  console.log('构建级联选择器，名称选项数量:', positionNameOptions.value.length);

  // 转换为级联选择器需要的格式
  const options = positionTypeOptions.value.map(type => {
    // 确保type.id存在且有效
    const typeId = type.id || type.value;
    if (!typeId) {
      console.warn('岗位类型缺少id:', type);
      return null;
    }

    // 查找该类型下的所有岗位名称
    const children = positionNameOptions.value
        .filter(name => {
          // typeId可能存储在不同属性中
          const nameTypeId = name.typeId || name.type_id;
          return nameTypeId === typeId;
        })
        .map(name => {
          const nameId = name.id || name.value;
          const nameLabel = name.dictLabel || name.name || name.label;
          return {
            value: nameId,
            label: nameLabel,
            isLeaf: true
          };
        });

    // 在children数组最前面添加"通用"选项
    const childrenWithCommon = [
      {
        value: 'COMMON',
        label: '通用',
        isLeaf: true
      },
      ...children
    ];

    // 返回级联选项
    return {
      value: typeId,
      label: type.dictLabel || type.name || type.label,
      children: childrenWithCommon.length > 1 ? childrenWithCommon : [{ value: 'empty', label: '无岗位', disabled: true }]
    };
  }).filter(Boolean); // 过滤掉null值

  console.log('生成的级联选择器选项:', options);
  return options;
});

// 处理级联选择器变更
const handlePositionCascaderChange = (value) => {
  console.log('级联选择器值变更:', value);

  // 清除之前的值
  formData.fileCategory = '';
  formData.position = '';
  selectedCategoryText.value = '';
  selectedPositionText.value = '';

  if (!value || value.length === 0) {
    console.log('级联选择器值被清空');
    return;
  }

  if (value.length >= 1) {
    formData.fileCategory = value[0];

    // 查找并设置分类文本
    if (Array.isArray(positionTypeOptions.value)) {
      const selectedType = positionTypeOptions.value.find(item => {
        const itemId = item.id || item.value;
        return itemId == value[0]; // 使用宽松比较，因为可能一个是字符串一个是数字
      });

      if (selectedType) {
        selectedCategoryText.value = selectedType.dictLabel || selectedType.name || selectedType.label || value[0];
        console.log('找到类型:', selectedType, '设置文本为:', selectedCategoryText.value);
      } else {
        console.warn('未找到对应类型:', value[0]);
        selectedCategoryText.value = String(value[0]);
      }
    }
  }

  if (value.length >= 2) {
    formData.position = value[1];

    // 处理"通用"选项
    if (value[1] === 'COMMON') {
      selectedPositionText.value = '通用';
      console.log('选择了通用选项');
    } else {
      // 查找并设置岗位文本
      if (Array.isArray(positionNameOptions.value)) {
        const selectedPosition = positionNameOptions.value.find(item => {
          const itemId = item.id || item.value;
          return itemId == value[1]; // 使用宽松比较
        });

        if (selectedPosition) {
          selectedPositionText.value = selectedPosition.dictLabel || selectedPosition.name || selectedPosition.label || value[1];
          console.log('找到岗位:', selectedPosition, '设置文本为:', selectedPositionText.value);
        } else {
          console.warn('未找到对应岗位:', value[1]);
          selectedPositionText.value = String(value[1]);
        }
      }
    }
  }

  console.log('级联选择器变更后的值:', {
    fileCategory: formData.fileCategory,
    position: formData.position,
    categoryText: selectedCategoryText.value,
    positionText: selectedPositionText.value
  });
};

// 生命周期钩子
onMounted(() => {
  fetchPositionOptions();
});

// 获取岗位类型和岗位名称选项
const fetchPositionOptions = async () => {
  try {
    console.log('开始获取岗位选项数据...');
    const [typeRes, nameRes] = await Promise.all([
      getPositionTypeOptions(),
      getPositionNameOptions()
    ]);

    console.log('API返回的岗位类型数据:', typeRes);
    console.log('API返回的岗位名称数据:', nameRes);

    // 处理岗位类型数据 - 尝试不同可能的数据结构
    let typeOptions = [];
    if (Array.isArray(typeRes)) {
      typeOptions = typeRes;
    } else if (typeRes && typeRes.rows && Array.isArray(typeRes.rows)) {
      typeOptions = typeRes.rows;
    } else if (typeRes && typeRes.data && Array.isArray(typeRes.data)) {
      typeOptions = typeRes.data;
    } else if (typeRes && typeof typeRes === 'object') {
      // 尝试从对象提取数组
      const possibleArrays = Object.values(typeRes).filter(val => Array.isArray(val));
      if (possibleArrays.length > 0) {
        // 使用最长的数组
        typeOptions = possibleArrays.reduce((a, b) => a.length > b.length ? a : b, []);
      }
    }

    // 处理岗位名称数据 - 尝试不同可能的数据结构
    let nameOptions = [];
    if (Array.isArray(nameRes)) {
      nameOptions = nameRes;
    } else if (nameRes && nameRes.rows && Array.isArray(nameRes.rows)) {
      nameOptions = nameRes.rows;
    } else if (nameRes && nameRes.data && Array.isArray(nameRes.data)) {
      nameOptions = nameRes.data;
    } else if (nameRes && typeof nameRes === 'object') {
      // 尝试从对象提取数组
      const possibleArrays = Object.values(nameRes).filter(val => Array.isArray(val));
      if (possibleArrays.length > 0) {
        // 使用最长的数组
        nameOptions = possibleArrays.reduce((a, b) => a.length > b.length ? a : b, []);
      }
    }

    // 确保数组元素有必要的属性
    positionTypeOptions.value = typeOptions.map(item => {
      // 确保每个项目都有id和label
      return {
        ...item,
        id: item.id || item.value || item.dictValue,
        dictLabel: item.dictLabel || item.name || item.label || '未命名'
      };
    });

    positionNameOptions.value = nameOptions.map(item => {
      // 确保每个项目都有id、typeId和label
      return {
        ...item,
        id: item.id || item.value || item.dictValue,
        typeId: item.typeId || item.type_id || item.positionTypeId || item.position_type_id,
        dictLabel: item.dictLabel || item.name || item.label || '未命名'
      };
    });

    console.log('处理后的岗位类型选项:', positionTypeOptions.value);
    console.log('处理后的岗位名称选项:', positionNameOptions.value);

    // // 如果没有成功获取数据，显示提示
    // if (positionTypeOptions.value.length === 0) {
    //   message.warning('未能获取到岗位类型数据');
    // }
    // if (positionNameOptions.value.length === 0) {
    //   message.warning('未能获取到岗位名称数据');
    // }
  } catch (error) {
    // console.error('获取岗位选项失败', error);
    // message.error('获取岗位选项失败: ' + (error.message || '未知错误'));
    // 确保即使出错也初始化为空数组
    positionTypeOptions.value = [];
    positionNameOptions.value = [];
  }
};

// 初始化编辑表单
const initEditForm = () => {
  if (!props.documentData) return;

  // 填充表单数据
  formData.id = props.documentData.id;
  formData.name = props.documentData.name;
  formData.fileName = props.documentData.fileName;
  formData.fileType = props.documentData.fileType;
  formData.fileCategory = props.documentData.fileCategoryId || props.documentData.fileCategory;
  formData.position = props.documentData.positionId || props.documentData.position;
  formData.positionLevel = props.documentData.positionLevelId || props.documentData.positionLevel;
  formData.certificateType = props.documentData.certificateTypeId || props.documentData.certificateType;
  formData.positionCascader = [formData.fileCategory, formData.position]; // 设置级联选择器的值
  formData.documentType = props.documentData.documentType || 'exam'; // 设置文档类型
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  formData.id = null;
  formData.name = '';
  formData.fileName = '';
  formData.fileType = '';
  formData.fileCategory = '';
  formData.position = '';
  formData.positionLevel = '';
  formData.certificateType = '';
  formData.positionCascader = []; // 重置级联选择器的值
  formData.documentType = 'exam'; // 重置文档类型为默认值
  fileList.value = [];
};

// 下一步
const nextStep = () => {
  if (currentStep.value === 0) {
    // 验证第一步表单
    formRef.value?.validateFields(['positionCascader']).then(() => {
      // 确保级联选择器的值已正确赋值给fileCategory和position
      if (!formData.fileCategory || !formData.position) {
        message.error('请完整选择岗位分类及名称');
        return;
      }

      currentStep.value += 1;
    }).catch(error => {
      console.error('表单验证失败', error);
    });
  } else if (currentStep.value === 1) {
    if (fileList.value.length === 0) {
      message.error('请上传至少一个文件');
      return;
    }
    currentStep.value += 1;
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value -= 1;
  }
};

// 用于确认页面展示的文本
const selectedCategoryText = ref('');
const selectedPositionText = ref('');

// 处理文件拒绝
const handleReject = (file) => {
  message.warning(`文件格式不支持，仅支持Word(.docx)和Excel(.xlsx)文件`);
};

// 处理文件变更
const handleFileChange = (info) => {
  // 获取文件列表
  let newFileList = [...info.fileList];

  // 最多允许10个文件
  if (newFileList.length > 10) {
    message.warning('最多只能上传10个文件');
    newFileList = newFileList.slice(0, 10);
  }

  // 筛选文件类型
  newFileList = newFileList.filter(file => {
    const fileName = file.name || '';
    const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

    // 只允许 docx 和 xlsx 文件
    if (!['docx', 'xlsx'].includes(ext)) {
      message.warning(`文件"${fileName}"格式不支持，仅支持Word(.docx)和Excel(.xlsx)文件`);
      return false;
    }

    return true;
  });

  // 为每个文件添加实际大小并去重（根据uid和文件名）
  const uniqueFiles = [];
  const uniqueUids = new Set();
  const uniqueNames = new Set();

  newFileList.forEach(file => {
    // 如果文件UID已存在，则跳过
    if (uniqueUids.has(file.uid)) {
      return;
    }

    // 如果文件名已存在，则跳过
    if (uniqueNames.has(file.name)) {
      message.warning(`文件"${file.name}"已添加，已自动跳过重复文件`);
      return;
    }

    // 记录已添加的文件UID和文件名
    uniqueUids.add(file.uid);
    uniqueNames.add(file.name);

    // 更新文件大小
    if (file.originFileObj) {
      file.size = file.originFileObj.size;
    }

    uniqueFiles.push(file);
  });

  // 更新文件列表
  fileList.value = uniqueFiles;
};

// 处理文件删除
const removeFile = (file) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    message.success('文件已移除');
  }
};

// 处理文件上传前的验证
const beforeUpload = async (file) => {
  // 检查文件类型
  const fileName = file.name;
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

  // 验证文件格式
  const validFileTypes = ['docx', 'xlsx'];
  if (!validFileTypes.includes(ext)) {
    message.error('只支持上传Word(.docx)和Excel(.xlsx)文件');
    return false;
  }

  // 检查文件大小限制
  const isLt15M = file.size / 1024 / 1024 < 15;
  if (!isLt15M) {
    message.error('文件大小不能超过15MB');
    return false;
  }

  // 检查Excel文件的特殊限制
  if (ext === 'xlsx') {
    try {
      const isValid = await validateExcelFile(file);
      if (!isValid) {
        return false;
      }
    } catch (error) {
      console.error('Excel文件验证失败:', error);
      message.error('Excel文件验证失败: ' + (error.message || '未知错误'));
      return false;
    }
  }

  return true;
};

// Excel文件特殊验证函数
const validateExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        // 检查sheet总数
        const sheetCount = workbook.SheetNames.length;
        if (sheetCount > 10) {
          message.error(`Excel文件sheet总数不能超过10个，当前有${sheetCount}个sheet`);
          resolve(false);
          return;
        }

        // 检查每个sheet的数据行数
        let isValid = true;
        let invalidSheets = [];

        for (const sheetName of workbook.SheetNames) {
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          if (jsonData.length > 2000) {
            invalidSheets.push({ name: sheetName, rows: jsonData.length });
            isValid = false;
          }
        }

        if (!isValid) {
          const errorMsg = invalidSheets.map(sheet =>
              `"${sheet.name}"(${sheet.rows}行)`
          ).join('、');
          message.error(`以下sheet页数据行数超过2000条：${errorMsg}`);
          resolve(false);
          return;
        }

        resolve(true);
      } catch (error) {
        console.error('解析Excel文件失败', error);
        message.error('解析Excel文件失败，请确保文件格式正确');
        resolve(false);
      }
    };

    reader.onerror = (error) => {
      console.error('读取文件失败', error);
      reject(new Error('读取文件失败'));
    };

    reader.readAsArrayBuffer(file);
  });
};

// 自定义上传方法，只收集文件不上传
const handleCustomUpload = ({ file, onSuccess }) => {
  // 这里不进行实际上传，只是模拟上传成功
  // 文件会在handleFileChange中被收集，并在最后提交时统一上传
  setTimeout(() => {
    onSuccess();
  }, 100);
};

// 处理弹窗取消
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 处理上传提交
const handleUploadSubmit = async () => {
  try {
    // 开始上传
    uploading.value = true;

    // 确保正确拆分级联选择器的值
    console.log('提交前检查级联选择器值:', formData.positionCascader);
    console.log('提交前检查拆分后的值:', {
      fileCategory: formData.fileCategory,
      position: formData.position
    });

    // 如果fileCategory或position为空，但positionCascader有值，尝试从positionCascader获取
    if ((!formData.fileCategory || !formData.position) &&
        Array.isArray(formData.positionCascader) &&
        formData.positionCascader.length >= 2) {
      formData.fileCategory = formData.positionCascader[0];
      formData.position = formData.positionCascader[1];
      console.log('从级联选择器值重新获取category和position:', {
        fileCategory: formData.fileCategory,
        position: formData.position
      });
    }

    // 检查表单必填项
    if (!formData.fileCategory || !formData.position) {
      message.warning('请选择文件分类和岗位名称');
      uploading.value = false;
      return;
    }

    const documentList = [];

    // 循环处理每个文件上传
    for (const file of fileList.value) {
      if (!file.originFileObj) {
        continue;
      }

      // 创建FormData对象
      const uploadFormData = new FormData();
      uploadFormData.append('file', file.originFileObj);

      console.log(`开始上传文件: ${file.name}, 大小: ${file.size}字节`);

      try {
        // 上传文件 - 使用API方法uploadFile
        const uploadResponse = await uploadFile(uploadFormData);
        console.log(`文件上传成功:`, uploadResponse);

        const fileData = uploadResponse;

        // 构建文档对象
        const fileNameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;

        // 使用文件分类作为文件归属，只传递ID
        const docData = {
          name: fileNameWithoutExt, // 使用文件名(无扩展名)作为证书名称
          fileName: fileData.fileName || file.name, // 如果后端没返回，使用上传时的文件名
          fileType: fileData.fileType || getFileType(file.name), // 如果后端没返回，自己判断
          fileSize: fileData.fileSize || file.size, // 如果后端没返回，使用上传时的大小
          fileUrl: fileData.fileUrl || '',
          // 保存ID (dictLabel放在前端显示，不传给后端)
          fileCategory: formData.fileCategory, // 来自级联选择器的第一级
          position: formData.position, // 来自级联选择器的第二级
          certificateType: formData.fileCategory,
          documentType: 'exam' // 固定为exam类型
        };

        console.log('待保存的文档对象:', docData);
        documentList.push(docData);
      } catch (uploadError) {
        console.error(`文件 ${file.name} 上传失败:`, uploadError);
        message.error(`文件 ${file.name} 上传失败: ${uploadError.message || '未知错误'}`);
      }
    }

    // 批量保存文档
    if (documentList.length > 0) {
      try {
        console.log('批量保存文档列表:', documentList);
        console.log('保存时使用的公共数据:', {
          fileCategory: formData.fileCategory,
          position: formData.position
        });

        const saveResponse = await batchSaveDocuments({
          documents: documentList,
          commonData: {
            fileCategory: formData.fileCategory,
            position: formData.position,
          }
        });

        const { success, failure } = saveResponse;

        if (success && success.length > 0) {
          message.success(`成功保存 ${success.length} 个文档`);
        }

        if (failure && failure.length > 0) {
          message.warning(`${failure.length} 个文档保存失败，可能是重复文档`);
        }

        // 关闭弹窗并重置表单
        visible.value = false;
        resetForm();
        emit('success');

      } catch (error) {
        console.error('保存文档失败:', error);
        message.error('保存文档失败: ' + (error.message || '未知错误'));
      }
    } else {
      message.error('没有可保存的文档');
    }
  } catch (error) {
    console.error('上传文件失败', error);
    message.error('上传文件失败: ' + (error.message || '未知错误'));
  } finally {
    uploading.value = false;
  }
};

// 处理编辑提交
const handleEditSubmit = async () => {
  try {
    // 确保正确拆分级联选择器的值
    console.log('编辑提交前检查级联选择器值:', formData.positionCascader);
    console.log('编辑提交前检查拆分后的值:', {
      fileCategory: formData.fileCategory,
      position: formData.position
    });

    // 如果fileCategory或position为空，但positionCascader有值，尝试从positionCascader获取
    if ((!formData.fileCategory || !formData.position) &&
        Array.isArray(formData.positionCascader) &&
        formData.positionCascader.length >= 2) {
      formData.fileCategory = formData.positionCascader[0];
      formData.position = formData.positionCascader[1];
      console.log('编辑提交：从级联选择器值重新获取category和position:', {
        fileCategory: formData.fileCategory,
        position: formData.position
      });
    }

    const submitData = {
      name: formData.name,
      fileCategory: formData.fileCategory,
      position: formData.position,
      positionLevel: formData.positionLevel,
      certificateType: formData.certificateType
    };

    console.log('编辑提交的数据:', submitData);

    const response = await updateKnowledgeDocument(formData.id, submitData);

    message.success('更新成功');
    visible.value = false;
    resetForm();
    emit('success');
  } catch (error) {
    console.error('更新知识库文档失败', error);
    message.error('更新失败: ' + (error.message || '未知错误'));
  }
};

// 根据ID获取文件分类标签
const getCategoryLabelById = (id) => {
  const numId = parseInt(id);
  if (!isNaN(numId) && Array.isArray(positionTypeOptions.value)) {
    const category = positionTypeOptions.value.find(item => item.id === numId || item.id === id);
    if (category) {
      return category.dictLabel;
    }
  }
  return id; // 如果找不到匹配的标签，返回原始值
};

// 根据ID获取岗位名称标签
const getPositionLabelById = (id) => {
  // 处理"通用"特殊值
  if (id === 'COMMON') {
    return '通用';
  }
  
  const numId = parseInt(id);
  if (!isNaN(numId) && Array.isArray(positionNameOptions.value)) {
    const position = positionNameOptions.value.find(item => item.id === numId || item.id === id);
    if (position) {
      return position.dictLabel;
    }
  }
  return id; // 如果找不到匹配的标签，返回原始值
};
</script>

<style scoped>
.upload-document-modal {
  max-width: 90%;
}

.upload-steps {
  margin: 32px 0px;
}

.step-content {
  min-height: 300px;
}

.step-form, .step-upload, .step-confirm, .edit-form {
  display: flex;
  flex-direction: column;
}

/* 表单步骤样式优化 */

/* 上传步骤样式 */
.upload-zone {
  border: 2px dashed #e6e6e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.upload-zone:hover {
  border-color: #a18cd1;
  background-color: #f9f0ff;
}

.upload-file-list {
  margin-top: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.upload-file-list h3 {
  font-size: 16px;
  color: #444;
  font-weight: 500;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  width: 100%;
  /* padding: 8px 0; */
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
}

.file-icon.pdf {
  color: #f56c6c;
}

.file-icon.word {
  color: #409eff;
}

.file-icon.excel {
  color: #67c23a;
}

.file-icon.ppt {
  color: #e6a23c;
}

.file-icon.image {
  color: #a18cd1;
}

.file-icon.text {
  color: #909399;
}

.file-icon.archive {
  color: #67c23a;
}

.file-icon.audio {
  color: #e6a23c;
}

.file-icon.video {
  color: #f56c6c;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file {
  margin-left: 12px;
}

/* 确认步骤样式优化 */
.confirm-content {
  padding: 0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.confirm-content h3 {
  font-size: 18px;
  /* margin-bottom: 16px; */
  color: #333;
  font-weight: 500;
  padding: 16px 20px;
  background-image: linear-gradient(135deg, #f9f0ff 0%, #f0f0ff 100%);
  border-bottom: 1px solid #f3e5ff;
}

.confirm-info {
  padding: 20px 0px;
  border-bottom: 1px solid #f0f0f0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.confirm-info p {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.confirm-info p strong {
  color: #555;
  margin-right: 8px;
  min-width: 100px;
  display: inline-block;
}

.confirm-files {
  background-color: #fff;
  padding: 20px 0px;
  border-radius: 0 0 8px 8px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.confirm-files h4 {
  margin-bottom: 16px;
  font-size: 15px;
  color: #444;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.edit-actions {
  margin-top: 40px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 编辑表单样式 */
/* .edit-form {
  max-width: 500px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  border: 1px solid #f0f0f0;
} */

.edit-form span {
  padding: 8px 12px;
  display: block;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  font-size: 14px;
  color: #555;
}

.edit-actions {
  margin-top: 32px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.ant-steps-item-title) {
  font-size: 14px;
  font-weight: 500;
}
:deep(.ant-steps-item-container):hover{
  cursor: auto !important;
}
:deep(.ant-steps-item-container):hover .ant-steps-item-icon {
  border:0px !important;
}
/* :deep(.ant-steps-item-container):hover .ant-steps-item-icon .ant-steps-icon, */
:deep(.ant-steps-item-container):hover .ant-steps-item-title {
  color: rgba(0, 0, 0, 0.45) !important;
}
:deep(.ant-steps-item-active .ant-steps-item-title) {
  color: rgba(0, 0, 0, 0.45) !important;
}

:deep(.ant-steps-item-finish .ant-steps-item-icon) {
  background-color: #a18cd1 !important;
  border-color: #a18cd1 !important;
}
:deep(.ant-steps-item-finish .ant-steps-item-icon>.ant-steps-icon) {
  color: #fff !important;
}

:deep(.ant-steps-item-finish .ant-steps-item-title::after) {
  background-color: #a18cd1 !important;
}

:deep(.ant-steps-item-process .ant-steps-item-icon) {
  background-color: #a18cd1 !important;
  border-color: #a18cd1 !important;
}

:deep(.ant-upload-drag) {
  border: none !important;
  background: transparent !important;
}

:deep(.ant-upload-drag-icon .anticon) {
  color: #a18cd1 !important;
}




.upload-requirements {
  margin-top: 20px;
  padding: 12px 16px;
  background-color: #f9f0ff;
  border-radius: 8px;
  border: 1px solid #f3e5ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  text-align: left;
}

.upload-requirements-title {
  font-weight: 500;
  color: #444;
  margin-bottom: 8px;
  font-size: 14px;
}

.upload-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.upload-requirements li {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 4px;
}
</style>
