<template>
  <div class="enterprise-list-container">
    <!-- 搜索表单 -->
    <div class="search-box">
      <search-form-card
        :model-value="searchForm"
        :items="searchItems"
        @search="handleSearch"
        @reset="resetQuery"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div class="action-box">
      <a-button class="custom-button" @click="showAddModal">
        <template #icon><plus-outlined /></template>
        新增企业
      </a-button>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-box">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        bordered
        rowKey="id"
        :scroll="{ x: 1500 }"
        @change="handleTableChange"
      >
        <!-- 企业类型列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'type'">
            {{ getEnterpriseTypeName(record.type) }}
          </template>
          
          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space size="small" class="action-buttons">
              <a-button type="link" size="small" @click="handleEdit(record)" class="action-button">编辑</a-button>
              <a-button type="link" size="small" @click="handleResetPassword(record)" class="action-button">重置密码</a-button>
              <a-button type="link" size="small" @click="handleToggleStatus(record)" class="action-button">
                {{ record.status ? '禁用' : '启用' }}
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small" class="action-button">
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="config">
                      <a @click="handleConfig(record)">配置企业</a>
                    </a-menu-item>
                    <a-menu-item key="agent">
                      <a @click="handleViewAgent(record)">智能体</a>
                    </a-menu-item>
                    <a-menu-item key="knowledge">
                      <a @click="handleViewKnowledge(record)">知识库</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    
    <!-- 新增/编辑对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalSubmit"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
    >
      <a-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="企业名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入企业名称" />
        </a-form-item>
        
        <a-form-item label="企业类型" name="type">
          <a-select
            v-model:value="formData.type"
            placeholder="请选择企业类型"
            style="width: 100%"
          >
            <a-select-option v-for="item in enterpriseTypes" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="企业编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入企业编码" />
        </a-form-item>
        
        <a-form-item label="地址" name="address">
          <a-input v-model:value="formData.address" placeholder="请输入地址" />
        </a-form-item>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="formData.contact" placeholder="请输入联系人" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 重置密码对话框 -->
    <a-modal
      v-model:visible="resetPasswordVisible"
      title="重置密码"
      @ok="handleResetPasswordConfirm"
      @cancel="resetPasswordVisible = false"
      :confirm-loading="resetPasswordLoading"
    >
      <p>确定要重置【{{ currentEnterprise && currentEnterprise.name || '' }}】的登录密码吗？</p>
      <p>重置后的初始密码为: <strong>123456</strong></p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { getEnterpriseList, createEnterprise, updateEnterprise, deleteEnterprise } from '@/api/enterprise';
import { SearchFormCard } from '@/components/SearchForm';
import { useTablePagination } from '@/utils/common';

const router = useRouter();
const formRef = ref(null);

// 企业类型选项
const enterpriseTypes = [
  { label: '科技公司', value: 'tech' },
  { label: '金融机构', value: 'finance' },
  { label: '教育机构', value: 'education' },
  { label: '医疗机构', value: 'medical' },
  { label: '制造业', value: 'manufacturing' },
  { label: '服务业', value: 'service' },
  { label: '其他', value: 'other' }
];

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  status: undefined
});

// 搜索表单项配置
const searchItems = [
  {
    label: '企业名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入企业名称'
  },
  {
    label: '企业编码',
    field: 'code',
    type: 'input',
    placeholder: '请输入企业编码'
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    width: '120px',
    options: [
      { label: '全部', value: 'all' },
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ]
  }
];

// 表格加载状态
const loading = ref(false);

// 表格分页设置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条数据`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列定义
const columns = [
  {
    title: '企业名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    width: 180
  },
  {
    title: '企业编码',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    ellipsis: true
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
    width: 120
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 320,
    fixed: 'right'
  }
];

// 表格数据
const tableData = ref([]);

// 模态框相关
const modalVisible = ref(false);
const modalMode = ref('add'); // 'add' 或 'edit'
const modalTitle = computed(() => modalMode.value === 'add' ? '新增企业' : '编辑企业');
const modalLoading = ref(false);

// 表单数据
const formData = reactive({
  id: undefined,
  name: '',
  code: '',
  address: '',
  contact: '',
  phone: '',
  email: '',
  status: true
});

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入企业编码', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ]
};

// 重置密码相关
const resetPasswordVisible = ref(false);
const resetPasswordLoading = ref(false);
const currentEnterprise = ref(null);

// 初始化数据
onMounted(() => {
  fetchEnterpriseList();
});

// 获取企业列表数据
const fetchEnterpriseList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    const response = await getEnterpriseList(params);
    tableData.value = response.list || [];
    // 更新分页信息
    updatePagination({
      total: response.total || 0,
      page: response.page,
      pageSize: response.pageSize
    });
  } catch (error) {
    console.error('获取企业列表失败', error);
    message.error('获取企业列表失败');
    // 发生错误时重置分页
    updatePagination({ total: 0, page: 1 });
  } finally {
    loading.value = false;
  }
};

// 使用表格分页组合式函数
const { 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchEnterpriseList,
  initialPagination: { current: 1, pageSize: 10, total: 0 },
  searchForm
});

// 搜索
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  // 重置到第一页
  resetPagination();
  fetchEnterpriseList();
};

// 重置查询
const resetQuery = () => {
  searchForm.name = '';
  searchForm.status = undefined;
  // 重置到第一页
  resetPagination();
  fetchEnterpriseList();
};

// 显示新增对话框
const showAddModal = () => {
  modalMode.value = 'add';
  resetFormData();
  modalVisible.value = true;
};

// 重置表单数据
const resetFormData = () => {
  formData.id = undefined;
  formData.name = '';
  formData.code = '';
  formData.address = '';
  formData.contact = '';
  formData.phone = '';
  formData.email = '';
  formData.status = true;
  
  // 重置表单校验状态
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理编辑
const handleEdit = (record) => {
  modalMode.value = 'edit';
  
  // 填充表单数据
  Object.assign(formData, record);
  
  modalVisible.value = true;
};

// 对话框提交
const handleModalSubmit = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    if (modalMode.value === 'add') {
      // 新增企业
      await createEnterprise(formData);
      message.success('新增企业成功');
    } else {
      // 编辑企业
      await updateEnterprise(formData);
      message.success('编辑企业成功');
    }
    
    modalVisible.value = false;
    fetchEnterpriseList();
  } catch (error) {
    message.error('操作失败: ' + error.message);
  } finally {
    modalLoading.value = false;
  }
};

// 对话框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 处理删除
const handleDelete = async (record) => {
  try {
    loading.value = true;
    await deleteEnterprise(record.id);
    message.success('删除企业成功');
    
    // 如果当前页只有一条数据且不是第一页，则跳转到上一页
    if (tableData.value.length === 1 && pagination.current > 1) {
      pagination.current--;
    }
    
    fetchEnterpriseList();
  } catch (error) {
    message.error('删除企业失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 状态切换
const handleToggleStatus = (record) => {
  Modal.confirm({
    title: '提示',
    content: `确定要${record.status ? '禁用' : '启用'}【${record.name}】吗？`,
    onOk: () => {
      // 找到对应记录
      const index = tableData.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        // 更新状态
        tableData.value[index].status = tableData.value[index].status ? 0 : 1;
        
        message.success(`${tableData.value[index].status ? '启用' : '禁用'}成功`);
      }
    }
  });
};

// 处理重置密码
const handleResetPassword = (record) => {
  currentEnterprise.value = record;
  resetPasswordVisible.value = true;
};

// 确认重置密码
const handleResetPasswordConfirm = () => {
  resetPasswordLoading.value = true;
  
  // 模拟后端请求延迟
  setTimeout(() => {
    resetPasswordLoading.value = false;
    resetPasswordVisible.value = false;
    message.success(`密码重置成功，新密码为: 123456`);
  }, 500);
};

// 配置企业
const handleConfig = (record) => {
  message.info(`配置企业: ${record.name}`);
};

// 查看智能体
const handleViewAgent = (record) => {
  router.push({
    path: '/enterprise/agent',
    query: { enterpriseId: record.id }
  });
};

// 查看知识库
const handleViewKnowledge = (record) => {
  router.push({
    path: '/enterprise/knowledge',
    query: { enterpriseId: record.id }
  });
};
</script>

<style scoped>
.enterprise-list-container {
  width: 100%;
}

.search-box {
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

.action-box {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.table-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

/* 确保表格等宽列 */
:deep(.ant-table) th {
  text-align: center;
}

:deep(.ant-table-cell) {
  white-space: nowrap;
}

/* 固定列样式优化 */
:deep(.ant-table-fixed-right) {
  background-color: #fff;
  padding: 8px 4px !important;
}

:deep(.ant-table-cell-fix-right .action-btns .ant-divider) {
  margin: 0 4px;
}
</style>