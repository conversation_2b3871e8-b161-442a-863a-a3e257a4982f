<template>
  <div class="page-container">
    <page-header>
      <template #search>
        <search-form-card
        :model-value="searchForm"
        :items="formItems"
        @search="handleSearch"
        @reset="resetSearch"
      />
      </template>
    </page-header>
    <!-- 使用抽离的表格组件 -->
    <base-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="paginationConfig"
      :scroll="{ y: 800 }"
      @change="handleTableChange"
      @detail="viewDetail"
      :action-config="actionConfig"
    >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'title'">
        <span class="title-text">{{ record.title }}</span>
      </template>
      <template v-if="column.key === 'positionName'">
        <span>{{ getPositionNameText(record.positionName) }}</span>
      </template>
      <template v-if="column.key === 'positionLevel'">
        <span>{{ getPositionLevelText(record.positionLevel) }}</span>
      </template>
    </template>
    </base-table>

    <!-- 详情对话框 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="'对话详情 - ' + (currentRecord ? `${currentRecord.positionType?.name || ''} ${currentRecord.employee?.name || ''}` : '')"
      width="800px"
      :footer="null"
    >
      <div class="chat-container" v-if="chatDetails.length">
        <div class="chat-messages">
          <div
            v-for="chat in chatDetails"
            :key="chat.id"
            :class="['message-item', chat.role]"
          >
            <div class="message-header">
              <span class="sender">{{ chat.role === 'user' ? '用户' : '餐考师' }}</span>
              <span class="createAt">{{ chat.createdAt }}</span>
            </div>
            <div class="message-content">{{ chat.content }}</div>
          </div>
        </div>
      </div>
      <div v-else class="empty-data">
        <a-empty description="暂无对话数据" />
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue';
// 删除 mock 数据导入
// import { frontHallExaminers, frontHallChats } from '@/mock/catering-examiner/front-hall';
import BaseTable from '@/components/BaseTable';
import { useTablePagination } from '@/utils/common';
import { getPositionNameOptions, getLevelOptions } from '@/api/organization/position';
import { getRestaurantRecordList, getRestaurantRecordDetail } from '@/api/restaurantRecord'; // 添加详情API导入

export default defineComponent({
  name: 'FrontHallExaminer',
  components: {
    BaseTable
  },
  setup() {
    const actionConfig = ref({
      detail: true,
      edit: false,  // 移除编辑按钮
      delete: false // 移除删除按钮
    })

    // 表格配置
    const columns = [
      { 
        title: '员工名称', 
        dataIndex: ['employee', 'name'], 
        key: 'employeeName', 
        width: 120 
      },
      { 
        title: '岗位名称', 
        dataIndex: 'positionName', 
        key: 'positionName', 
        width: 120 
      },
      { 
        title: '岗位等级', 
        dataIndex: 'positionLevel', 
        key: 'positionLevel', 
        width: 120 
      },
      // { title: '标题', dataIndex: 'title', key: 'title', width: 180, ellipsis: true, className: 'title-text-column' },
      // { title: '聊天内容总结', dataIndex: 'chatSummary', key: 'chatSummary', width: 240, ellipsis: true },
      { title: '创建时间', dataIndex: 'createdAt', key: 'createTime', width: 160 },
      // { title: '更新时间', dataIndex: 'updateAt', key: 'updateTime', width: 160 },
      { title: '消息数', dataIndex: 'count', key: 'count', width: 100 },
      { title: '操作', key: 'action', width: 250 }
    ];

    const loading = ref(false);
    const tableData = ref([]);

    // 详情相关
    const detailVisible = ref(false);
    const currentRecord = ref(null);
    const chatDetails = ref([]);

    // 搜索表单 - 改为reactive对象，便于与useTablePagination结合
    const searchForm = reactive({
      employeeName: '',
      createTimeRange: [],
      positionName: undefined,
      positionLevel: undefined
    });

    // 岗位名称和等级选项
    const positionNameOptions = ref([]);
    const positionLevelOptions = ref([]);

    // 获取岗位名称选项
    const fetchPositionNameOptions = async () => {
      try {
        const res = await getPositionNameOptions();
        positionNameOptions.value = res.rows || [];
      } catch (error) {
        console.error('获取岗位名称选项失败:', error);
        positionNameOptions.value = [];
      }
    };

    // 获取岗位等级选项
    const fetchPositionLevelOptions = async () => {
      try {
        const res = await getLevelOptions();
        positionLevelOptions.value = res || [];
      } catch (error) {
        console.error('获取岗位等级选项失败:', error);
        positionLevelOptions.value = [];
      }
    };

    // 根据岗位名称ID获取名称
    const getPositionNameText = (positionNameId) => {
      if (!Array.isArray(positionNameOptions.value) || !positionNameId) {
        return positionNameId || '';
      }
      const option = positionNameOptions.value.find(item => 
        String(item.id) === String(positionNameId) || item.id === positionNameId
      );
      return option ? option.name : positionNameId;
    };

    // 根据岗位等级ID获取名称
    const getPositionLevelText = (positionLevelId) => {
      if (!Array.isArray(positionLevelOptions.value) || !positionLevelId) {
        return positionLevelId || '';
      }
      const option = positionLevelOptions.value.find(item => 
        String(item.id) === String(positionLevelId) || item.id === positionLevelId
      );
      return option ? option.name : positionLevelId;
    };

    const formItems = ref([
      {
        label: '员工名称',
        field: 'employeeName',
        type: 'input',
        placeholder: '请输入员工名称'
      },
      {
        label: '岗位名称',
        field: 'positionName',
        type: 'select',
        placeholder: '请选择岗位名称',
        options: computed(() => {
          const options = Array.isArray(positionNameOptions.value) ? positionNameOptions.value.map(item => ({
            label: item.name,
            value: item.id
          })) : [];
          return options;
        })
      },
      {
        label: '岗位等级',
        field: 'positionLevel',
        type: 'select',
        placeholder: '请选择岗位等级',
        options: computed(() => {
          const options = Array.isArray(positionLevelOptions.value) ? positionLevelOptions.value.map(item => ({
            label: item.name,
            value: item.id
          })) : [];
          return options;
        })
      },
      {
        label: '创建时间',
        field: 'createTimeRange',
        type: 'dateRange',
        placeholder: ['开始日期', '结束日期']
      }
    ]);

    // 获取表格数据
    const fetchTableData = async () => {
      loading.value = true;
      try {
        const params = {
          employeeName: searchForm.employeeName,
          positionName: searchForm.positionName,
          positionLevel: searchForm.positionLevel,
          startTime: searchForm.createTimeRange?.[0],
          endTime: searchForm.createTimeRange?.[1],
          pageNum: originalPagination.current,
          pageSize: originalPagination.pageSize
        };
        
        const res = await getRestaurantRecordList(params);
 
        tableData.value = res.records || res.rows || [];
        
        // 同时更新两个分页对象
        const apiTotal = res.total || 0;
        
        // 更新总数
        totalCount.value = apiTotal;
        
        updatePagination({
          total: apiTotal
        });
      } catch (error) {
        console.error('获取餐烤师记录失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 分页总数
    const totalCount = ref(0);
    
    // 计算属性生成分页配置
    const paginationConfig = computed(() => ({
      current: originalPagination.current,
      pageSize: originalPagination.pageSize,
      total: totalCount.value,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100']
    }));

    // 使用表格分页组合式函数
    const {
      pagination: originalPagination,
      handleTableChange: originalHandleTableChange,
      updatePagination,
      resetPagination
    } = useTablePagination({
      fetchData: fetchTableData,
      initialPagination: { current: 1, pageSize: 10, total: 0 },
      searchForm
    });

    // 包装handleTableChange方法，确保正确处理参数
    const handleTableChange = (changeData) => {
      const { pagination: pag, filters, sorter, extra } = changeData;
      
      // 使用Object.assign更新originalPagination
      Object.assign(originalPagination, {
        current: pag.current,
        pageSize: pag.pageSize
      });
      
      // 调用原始的handleTableChange
      originalHandleTableChange({ pagination: pag }, filters, sorter);
    };

    // 搜索处理
    const handleSearch = (values) => {
      if (values) {
        Object.assign(searchForm, values);
      }
      // 重置到第一页
      resetPagination();
      fetchTableData();
    };

    // 重置搜索
    const resetSearch = () => {
      // 重置搜索条件
      Object.keys(searchForm).forEach(key => {
        if (Array.isArray(searchForm[key])) {
          searchForm[key] = [];
        } else {
          searchForm[key] = '';
        }
      });
      // 重置到第一页
      resetPagination();
      fetchTableData();
    };

    // 查看详情
    const viewDetail = async (record) => {
      currentRecord.value = record;
      loading.value = true;
      try {
        const res = await getRestaurantRecordDetail(record.id);
        // 统一处理返回的数据，确保格式一致
        chatDetails.value = (res || []).map(item => ({
          id: item.id,
          role: item.role || 'user', // 默认为user角色
          content: item.content || '',
          createdAt: item.createdAt || item.created_at || ''
        }));
      } catch (error) {
        console.error('获取对话详情失败:', error);
        chatDetails.value = [];
      } finally {
        loading.value = false;
      }
      detailVisible.value = true;
    };

    onMounted(async () => {
      // 先加载选项数据
      await Promise.all([
        fetchPositionNameOptions(),
        fetchPositionLevelOptions()
      ]);
      
      // 选项数据加载完成后再加载表格数据
      fetchTableData();
    });

    return {
      searchForm,
      columns,
      tableData,
      loading,
      handleTableChange,
      handleSearch,
      resetSearch,
      detailVisible,
      currentRecord,
      chatDetails,
      viewDetail,
      formItems,
      actionConfig,
      getPositionNameText,
      getPositionLevelText,
      paginationConfig
    };
  }
});
</script>

<style lang="scss" scoped>
/* 聊天对话框样式 */
.chat-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;

  .chat-messages {
    .message-item {
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 8px;
      max-width: 80%;
      position: relative;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &.user {
        background-color: rgba(161, 140, 209, 0.1);
        border-left: 3px solid #a18cd1;
        margin-left: auto;
        margin-right: 0;
        border-top-right-radius: 2px;
      }

      &.system {
        background-color: #fff;
        border-left: 3px solid #52c41a;
        margin-right: auto;
        margin-left: 0;
        border-top-left-radius: 2px;
      }

      .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .sender {
          font-weight: 500;
          margin-right: 8px;
          color: #333;
        }

        .createAt {
          font-size: 12px;
          color: #999;
          margin-left: auto;
        }
      }

      .message-content {
        line-height: 1.6;
        word-break: break-all;
        white-space: pre-wrap;
        color: #333;
      }
    }
  }
}

.empty-data {
  padding: 40px 0;
}

:deep(.title-text-column) {
  font-weight: 500;
  color: #333;
}
</style>
