import { spawn } from 'child_process';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import os from 'os';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取本机IP地址
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // 跳过非IPv4地址和内部IP
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

// 启动开发服务器
console.log('正在启动餐烤餐考后台管理系统...');
console.log('请访问本地地址: http://localhost:5173');
console.log(`或通过局域网访问: http://${localIP}:5173`);

// 使用npm run dev命令启动项目
const child = spawn('npm', ['run', 'dev'], {
  cwd: join(__dirname, '..'),
  stdio: 'inherit',
  shell: true
});

child.on('close', (code) => {
  if (code !== 0) {
    console.log(`启动进程退出，退出码 ${code}`);
  }
}); 