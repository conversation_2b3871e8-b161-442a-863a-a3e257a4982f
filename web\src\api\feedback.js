import request from '@/utils/request';

// 获取反馈列表
export function getFeedbackList(params) {
  return request({
    url: '/feedback/list',
    method: 'get',
    params
  });
}

// 获取反馈类型
export function getFeedbackTypes() {
  return request({
    url: '/feedback/types',
    method: 'get'
  });
}

// 获取反馈详情
export function getFeedbackDetail(id) {
  return request({
    url: `/feedback/detail/${id}`,
    method: 'get'
  });
}

// 创建反馈
export function createFeedback(data) {
  return request({
    url: '/feedback/create',
    method: 'post',
    data
  });
}

// 回复反馈
export function replyFeedback(id, data) {
  return request({
    url: `/feedback/reply/${id}`,
    method: 'post',
    data
  });
}