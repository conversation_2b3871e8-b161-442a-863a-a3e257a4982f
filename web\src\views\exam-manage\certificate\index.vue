<!-- 证书记录组件 -->
<template>
  <div class="page-container">
    <!-- 全屏遮罩加载层 -->
    <div class="global-loading-mask" v-if="fullscreenLoading">
      <div class="loading-spinner">
        <a-spin size="large" />
        <div class="loading-text">导出证书中...</div>
      </div>
    </div>
    <page-header>
      <template #search>
        <search-form-card
          :model-value="searchForm"
          :items="formItems"
          :key="formKey"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </template>
      <!-- <template #actions>
        <a-space>
          <a-button danger :disabled="!selectedRowKeys.length" @click="handleBatchExport">
            <export-outlined /> 批量导出
          </a-button>
        </a-space>
      </template> -->
    </page-header>

    <!-- 表格 -->
    <base-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      rowKey="id"
      :pagination="pagination"
      @change="handleTableChange"
      :show-default-action="false"
    >
      <!-- 自定义列内容 -->
      <template #bodyCell="{ column, record }">
        <!-- 操作按钮 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="viewCertificate(record)"
              class="custom-button"
            >
              <eye-outlined />查看证书
            </a-button>
            <!-- <a-button
              type="link"
              size="small"
              @click="handleDownloadCertificate(record)"
              class="custom-button"
            >
              <download-outlined />导出证书
            </a-button> -->
          </a-space>
        </template>
      </template>
    </base-table>

    <!-- 证书预览弹窗 -->
    <a-modal
      v-model:visible="certificateModalVisible"
      title="证书预览"
      :footer="null"
      width="800px"
    >
      <certificate-preview 
        :certificate="currentRecord" 
        @download="handleDownloadCertificate"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, nextTick } from 'vue';
import { EyeOutlined, DownloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { SearchFormCard } from '@/components/SearchForm';
import { getCertificateList } from '@/api/exam/certificate';
import { getPositionNameList, getPositionNameOptions } from '@/api/organization/position';
import { useTablePagination } from '@/utils/common';
import dayjs from 'dayjs';
import logoSvg from '@/assets/logo.svg';
import CertificatePreview from './components/CertificatePreview.vue';


// 重新渲染搜索表单用key
const formKey = ref(0);

// 搜索表单
const searchForm = reactive({
  searchText: '',
  dateRange: [],
  positionFilter: '',
  positionId: ''
});


// 岗位类型选项
const typeList = ref([]);

// 搜索表单配置
const formItems = computed(() => [
  {
    label: '证书名称/员工名称',
    field: 'searchText',
    type: 'input',
    placeholder: '请输入证书名称/员工名称'
  },
  // {
  //   label: '获取证书时间',
  //   field: 'dateRange',
  //   type: 'dateRange',
  //   placeholder: ['开始时间', '结束时间']
  // },
  {
    label: '岗位名称',
    field: 'positionId',
    type: 'select',
    placeholder: '请选择岗位名称',
    // options: [],
    options: typeList.value,
    selectLabel: 'name',
    selectValue: 'id',
    allowClear: true
  }
]);


// 表格加载状态
const loading = ref(false);
const fullscreenLoading = ref(false);

// 表格列定义
const columns = [
  {
    title: '证书编号',
    dataIndex: 'certificateNo',
    width: 180
  },
  {
    title: '证书名称',
    dataIndex: 'certificateName',
  },
  {
    title: '员工姓名',
    dataIndex: 'employeeName',
  },
  {
    title: '岗位名称',
    dataIndex: ['positionNameInfo', 'name'],
  },
  {
    title: '岗位等级',
    dataIndex: ['positionLevelInfo', 'name'],
  },
  {
    title: '获取时间',
    dataIndex: 'obtainTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    title: '有效期至',
    dataIndex: 'validUntil',
    customRender: ({ text }) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 220,
    fixed: 'right'
  }
];

// 数据源
const dataSource = ref([]);

// 当前选中的记录
const currentRecord = ref(null);

// 证书预览弹窗可见性
const certificateModalVisible = ref(false);

// 证书元素引用
const certificateRef = ref(null);

// 选中的行
const selectedRowKeys = ref([]);

// 行选择配置
// const rowSelection = {
//   onChange: (keys, selectedRows) => {
//     selectedRowKeys.value = keys;
//   }
// };
// 获取岗位类型选项
const fetchPositionTypeOptions = async () => {
  try {
    const data = await getPositionNameOptions();
    // if (data ) {
      // 清空现有数组并重新赋值（使用新引用触发响应式更新）
      // const newOptions = [];
      // for (let i = 0; i < data.length; i++) {
      //   const item = data[i];
      //   newOptions.push({
      //     label: item.name,
      //     value: item.id
      //   });
      // }
      typeList.value = data.rows;
      // 更新下拉框选项
      // formItems.value[2].options = newOptions;
    // } else {
    //   console.error('获取岗位类型选项失败，返回数据格式不正确', data);
    // }
  } catch (error) {
    console.error('获取岗位类型选项失败', error);
  }
};


// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      searchText: searchForm.searchText,
      // startDate: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      // endDate: searchForm.dateRange?.[1]?.format('YYYY-MM-DD'),
      // positionFilter: searchForm.positionFilter,
      positionId: searchForm.positionId
    };

    const res = await getCertificateList(params);
    if (res) {
      dataSource.value = res.records;
      
      // 更新分页信息
      updatePagination({
        total: res.total,
        current: parseInt(res.pageNum) || 1,
        pageSize: parseInt(res.pageSize) || 10
      });
    } else {
      message.error(res.message || '获取证书记录数据失败');
    }
  } catch (error) {
    console.error('获取证书记录数据失败', error);
    message.error('获取证书记录数据失败');
  } finally {
    loading.value = false;
  }
}

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: fetchData,
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
  searchForm
});

// 格式化日期
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-';
};

// 获取证书有效期（默认一年）
const getExpiryDate = (obtainTime) => {
  return obtainTime ? dayjs(obtainTime).add(1, 'year').format('YYYY-MM-DD') : '-';
};


// 下载证书
const handleDownloadCertificate = async (record) => {
  try {
    message.loading({ content: '证书生成中...', key: 'download' });
    fullscreenLoading.value = true;

    // 动态导入 dom-to-image
    const domtoimage = (await import('dom-to-image')).default;

    // 如果是从列表直接下载，需要先显示模态框
    if (!certificateModalVisible.value) {
      currentRecord.value = record;
      certificateModalVisible.value = true;
      // 等待模态框内容渲染完成
      await nextTick();
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 获取证书元素
    const certificateElement = certificateRef.value;

    if (!certificateElement) {
      throw new Error('未找到证书元素');
    }

    // 等待所有图片加载完成
    const images = certificateElement.getElementsByTagName('img');
    await Promise.all(Array.from(images).map(img => {
      if (img.complete) return Promise.resolve();
      return new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
      });
    }));

    // 将DOM转换为图片
    const dataUrl = await domtoimage.toPng(certificateElement, {
      quality: 1.0,
      bgcolor: '#ffffff',
      style: {
        transform: 'scale(2)',
        transformOrigin: 'top left'
      }
    });

    // 创建下载链接
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = `${record.certificateName}_${record.employeeName}_证书.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success({ content: '导出成功', key: 'download' });
  } catch (error) {
    console.error('导出证书失败', error);
    message.error({ content: '导出失败：' + (error.message || '未知错误'), key: 'download' });
  } finally {
    fullscreenLoading.value = false;
  }
};

// 批量导出证书
const handleBatchExport = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要导出的证书');
    return;
  }

  // 提示用户确认
  message.info({
    content: `将逐个导出 ${selectedRowKeys.value.length} 份证书，请等待`,
    duration: 3
  });

  fullscreenLoading.value = true;

  try {
    // 找出选中的记录
    const selectedRecords = dataSource.value.filter(record => 
      selectedRowKeys.value.includes(record.id)
    );

    // 逐个导出证书
    for (let i = 0; i < selectedRecords.length; i++) {
      const record = selectedRecords[i];
      
      // 更新加载提示
      message.loading({
        content: `正在导出 ${i+1}/${selectedRecords.length}: ${record.employeeName} 的证书...`,
        key: 'batchExport',
        duration: 0
      });
      
      // 导出单个证书
      await handleDownloadCertificate(record);
      
      // 短暂等待避免浏览器阻止多次下载
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    message.success({
      content: `成功导出 ${selectedRecords.length} 份证书`,
      key: 'batchExport'
    });
    
    // 清空选择
    selectedRowKeys.value = [];
  } catch (error) {
    console.error('批量导出失败', error);
    message.error({
      content: '批量导出失败: ' + (error.message || '未知错误'),
      key: 'batchExport'
    });
  } finally {
    fullscreenLoading.value = false;
  }
};

// 搜索处理
const handleSearch = (values) => {
  // 更新搜索表单
  Object.assign(searchForm, values);
  // 重置分页到第一页
  resetPagination();
  // 获取数据
  fetchData();
};

// 重置搜索
const resetSearch = (values) => {
  if (values) {
    // 如果传入了值，直接使用
    Object.assign(searchForm, values);
  } else {
    // 否则重置为初始值
    searchForm.searchText = '';
    searchForm.dateRange = [];
    searchForm.positionFilter = '';
    searchForm.positionId = '';
  }
  // 重置分页到第一页
  resetPagination();
  // 获取数据
  fetchData();
};

// 查看证书
const viewCertificate = (record) => {
  currentRecord.value = record;
  certificateModalVisible.value = true;
};

// 初始化
onMounted(async () => {
  await fetchPositionTypeOptions();
  formKey.value+=1;
  await fetchData();
});

</script>

<style lang="scss" scoped>
.table-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

</style>
