import { defineStore } from 'pinia'
import { getMenuList, createMenu, updateMenu, deleteMenu } from '@/api/system/menu'

export const useMenuStore = defineStore('menu', {
  state: () => ({
    menus: [],
    permissions: [],
    userMenus: [] // 保存用户可访问的菜单
  }),

  actions: {
    // 获取菜单列表
    async getMenuList() {
      try {
        const response = await getMenuList()
        this.menus = response.data || []
        return this.menus
      } catch (error) {
        console.error('获取菜单列表失败:', error)
        this.menus = []
        return []
      }
    },

    // 创建菜单
    async createMenu(data) {
      const response = await createMenu(data)
      return response.data
    },

    // 更新菜单
    async updateMenu(data) {
      const response = await updateMenu(data)
      return response.data
    },

    // 删除菜单
    async deleteMenu(id) {
      const response = await deleteMenu(id)
      return response.data
    },

    // 设置权限列表
    setPermissions(permissions) {
      this.permissions = permissions || []
    },
    
    // 根据角色权限获取用户菜单
    async getUserMenus(permissions = []) {
      try {
        // 先获取所有菜单
        if (!this.menus || this.menus.length === 0) {
          await this.getMenuList()
        }
        
        // 确保menus是数组
        const menuList = Array.isArray(this.menus) ? this.menus : []
        
        // 筛选用户有权限且未隐藏的菜单
        const filterMenus = (menuList) => {
          if (!Array.isArray(menuList)) return []
          
          return menuList.filter(menu => {
            if (!menu) return false
            
            // 菜单已隐藏，直接过滤掉
            if (menu.hidden || menu.status === false) {
              return false
            }
            
            // 检查权限
            if (menu.perms && Array.isArray(permissions) && !permissions.includes(menu.perms)) {
              return false
            }
            
            // 递归处理子菜单
            if (menu.children) {
              menu.children = filterMenus(menu.children)
            }
            
            // 如果是目录，需要看是否至少有一个子菜单可见
            if (menu.type === 0 && (!Array.isArray(menu.children) || menu.children.length === 0)) {
              return false
            }
            
            return true
          })
        }
        
        this.userMenus = filterMenus(menuList)
        return this.userMenus
      } catch (error) {
        console.error('获取用户菜单失败:', error)
        this.userMenus = []
        return []
      }
    }
  }
}) 