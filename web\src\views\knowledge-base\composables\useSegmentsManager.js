import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { 
  getDocumentSegments, 
  addSegments, 
  updateSegment, 
  deleteSegment 
} from '@/api/knowledge-base/segments';

export function useSegmentsManager() {
  const documentSegments = ref([]);
  const paragraphsDrawerVisible = ref(false);
  const segmentsLoading = ref(false);
  const currentDocumentForSegments = ref(null);
  const fullscreenLoading = ref(false);

  // 打开段落管理抽屉
  const openSegmentsDrawer = async (document) => {
    try {
      if (!document || !document.id) {
        message.error('文档信息不完整');
        return;
      }

      // 保存当前文档信息，用于后续操作
      currentDocumentForSegments.value = document;
      
      // 如果抽屉没有打开，则设置加载状态和打开抽屉
      const isNewOpen = !paragraphsDrawerVisible.value;
      if (isNewOpen) {
        fullscreenLoading.value = true;
      } else {
        // 如果是刷新数据，显示局部加载
        segmentsLoading.value = true;
      }
      
      try {
        // 尝试从API获取数据
        const response = await getDocumentSegments(document.id);
        console.log('获取段落数据成功', response);
        // 适配数据格式 - 后端返回的data字段包含段落数据
        documentSegments.value = response || [];
        
        if (isNewOpen) {
          // 第一次打开才显示抽屉
          paragraphsDrawerVisible.value = true;
        }
      } catch (error) {
        console.error('获取段落数据失败', error);
        // API调用失败时，使用空数据
        documentSegments.value = [];
        message.error('获取段落数据失败');
        
        if (isNewOpen) {
          // 仍然打开抽屉，显示空状态
          paragraphsDrawerVisible.value = true;
        }
      }
    } catch (error) {
      console.error('获取段落数据失败', error);
      message.error('获取段落数据失败');
    } finally {
      segmentsLoading.value = false;
      fullscreenLoading.value = false;
    }
  };

  // 保存段落 - 这里我们分别处理每个段落的添加、更新和删除
  const saveSegments = async (documentId, segments) => {
    try {
      const docId = documentId || currentDocumentForSegments.value?.id;
      
      if (!docId) {
        message.error('文档ID缺失');
        return false;
      }
      
      // 开始保存前显示加载状态
      fullscreenLoading.value = true;
      
      try {
        // 确保segments数据正确传递给API
        console.log('准备提交段落数据:', segments);
        console.log('文档ID:', docId);
        
        if (!segments || !Array.isArray(segments)) {
          throw new Error('段落数据格式无效，期望数组类型');
        }
        
        // 分离新段落和已有段落
        const existingSegments = [];
        const newSegments = [];
        
        segments.forEach(segment => {
          // 清理数据，只保留API需要的字段
          const cleanSegment = {
            content: segment.content,
            keywords: Array.isArray(segment.keywords) ? segment.keywords : [],
            position: segment.position || 0
          };
          
          // 如果段落有ID且不是临时ID，则认为是已存在的段落
          if (segment.id && !segment.id.startsWith('temp_')) {
            cleanSegment.id = segment.id;
            existingSegments.push(cleanSegment);
          } else {
            // 新段落不提交ID，后端会自动生成
            newSegments.push(cleanSegment);
          }
        });
        
        console.log('已有段落:', existingSegments);
        console.log('新添加段落:', newSegments);
        
        // 创建所有操作的Promise数组
        const operations = [];
        
        // 处理每个已有段落的更新
        for (const segment of existingSegments) {
          const segmentId = segment.id;
          // 移除id字段，因为updateSegment API不需要在数据中包含id
          const { id, ...updateData } = segment;
          operations.push(updateSegment(docId, segmentId, updateData));
        }
        
        // 如果有新段落，批量添加
        if (newSegments.length > 0) {
          operations.push(addSegments(docId, newSegments));
        }
        
        // 等待所有操作完成
        await Promise.all(operations);
        
        // message.success('段落保存成功');
        return true;
      } catch (error) {
        console.error('保存段落失败', error);
        message.error('保存段落失败: ' + (error.message || '未知错误'));
        return false;
      } finally {
        // 无论成功失败都关闭加载状态
        fullscreenLoading.value = false;
      }
    } catch (error) {
      console.error('保存段落失败', error);
      message.error('保存段落失败');
      fullscreenLoading.value = false; // 确保在错误情况下也关闭加载状态
      return false;
    }
  };

  // 删除段落
  const removeSegment = async (documentId, segmentId) => {
    try {
      await deleteSegment(documentId, segmentId);
      return true;
    } catch (error) {
      console.error('删除段落失败', error);
      message.error('删除段落失败: ' + (error.message || '未知错误'));
      return false;
    }
  };

  // 更新段落
  const editSegment = async (documentId, segmentId, data) => {
    try {
      await updateSegment(documentId, segmentId, data);
      return true;
    } catch (error) {
      console.error('更新段落失败', error);
      message.error('更新段落失败: ' + (error.message || '未知错误'));
      return false;
    }
  };

  return {
    documentSegments,
    paragraphsDrawerVisible,
    segmentsLoading,
    fullscreenLoading,
    currentDocumentForSegments,
    openSegmentsDrawer,
    saveSegments,
    removeSegment,
    editSegment
  };
} 