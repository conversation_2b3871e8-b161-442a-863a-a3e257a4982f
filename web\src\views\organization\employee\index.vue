<template>
  <div class="employee-container">
    <a-card :bordered="false">
      <!-- 搜索表单 -->
      <div class="search-area">
        <search-form-card
          :model-value="searchForm"
          :items="searchFormItems"
          @search="handleSearch"
          @reset="resetQuery"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="operation-area">
        <a-space>
          <a-button type="primary" class="primary-button" @click="handleAdd">
            新增
          </a-button>
          <a-button class="primary-button" @click="handleImport">
            导入
          </a-button>
          <a-button class="primary-button" @click="handleExport">
            导出
          </a-button>
          <a-button class="primary-button" @click="() => message.success('测试按钮点击')">
            测试
          </a-button>
        </a-space>
      </div>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data-source="employeeList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
        bordered
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'department'">
            {{ record.department || '总部' }}
          </template>
          <template v-if="column.key === 'gender'">
            {{ record.gender === 'male' ? '男' : '女' }}
          </template>
          <template v-if="column.key === 'status'">
            <a-badge :status="record.status === '1' ? 'success' : 'error'" :text="record.status === '1' ? '在职' : '离职'" />
          </template>
          <template v-if="column.key === 'action'">
            <div class="action-buttons">
              <a-button type="link" class="action-button" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" class="action-button" @click="handleDelete(record)">
                删除
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑员工弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="700px"
    >
      <a-form :model="formState" :rules="formRules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" name="name">
              <a-input v-model:value="formState.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formState.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="归属部门" name="departmentId">
              <a-tree-select
                v-model:value="formState.departmentId"
                :tree-data="departmentOptions"
                placeholder="请选择部门"
                tree-default-expand-all
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="岗位名称" name="positionId">
              <a-select v-model:value="formState.positionId" placeholder="请选择岗位">
                <a-select-option v-for="item in positionOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="岗位等级" name="levelId">
              <a-select v-model:value="formState.levelId" placeholder="请选择岗位等级">
                <a-select-option v-for="item in levelOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="gender">
              <a-radio-group v-model:value="formState.gender">
                <a-radio value="male">男</a-radio>
                <a-radio value="female">女</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="身份证号" name="idCard">
              <a-input v-model:value="formState.idCard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="入职时间" name="entryTime">
              <a-date-picker v-model:value="formState.entryTime" style="width: 100%" :locale="zhCN" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="formState.status">
                <a-radio value="1">在职</a-radio>
                <a-radio value="0">离职</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 导入员工弹窗 -->
    <a-modal
      v-model:visible="importModalVisible"
      title="导入员工"
      @ok="handleImportOk"
      @cancel="handleImportCancel"
      width="600px"
      :confirm-loading="importLoading"
    >
      <div class="import-content">
        <div class="import-tips">
          <h4>导入说明：</h4>
          <ul>
            <li>1、红色列为必填项</li>
            <li>2、部门名称请按系统中组织架构填写，多层级用/间隔，例如：阿依来/红旗南路店</li>
            <li>3、岗位级别请按系统中岗位级别填写，多岗位用/间隔，格式：岗位-级别/岗位-级别，例如：服务员-P2/传菜员-P3</li>
            <li>4、身份证号中的X请使用大写</li>
            <li>5、性别：男/女，不填写系统默认男</li>
            <li>6、入职时间：年-月-日</li>
            <li>7、状态：在职/离职，不填写系统默认在职</li>
          </ul>
        </div>
        
        <div class="import-actions">
          <a-space direction="vertical" style="width: 100%;">
            <div>
              <a-button type="link" @click="handleDownloadTemplate" :loading="downloadLoading">
                下载导入模板
              </a-button>
            </div>
            
            <div>
              <a-upload
                :file-list="fileList"
                :before-upload="beforeUpload"
                @remove="handleRemove"
                accept=".xlsx,.xls"
              >
                <a-button>
                  <upload-outlined />
                  选择文件
                </a-button>
              </a-upload>
            </div>
            
            <div v-if="importResult" class="import-result">
              <a-alert
                :message="importResult.message"
                :type="importResult.type"
                show-icon
                closable
              />
            </div>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { getEmployeeList, addEmployee, updateEmployee, deleteEmployee, importEmployees, exportEmployees, downloadEmployeeTemplate } from '@/api/organization/employee';
import { getDepartmentTree } from '@/api/organization/department';
import { getPositionOptions } from '@/api/organization/position';
import { getLevelOptions } from '@/api/organization/position';
import { SearchFormCard } from '@/components/SearchForm';
import { useTablePagination } from '@/utils/common';

// 查询参数
const searchForm = reactive({
  name: '',
  phone: '',
  department: '',
  position: '',
  level: '',
  entryTimeStart: '',
  entryTimeEnd: '',
});

// 监听searchForm变化
watch(searchForm, (newVal) => {
  console.log('[employee] searchForm变化:', newVal);
}, { deep: true });

// 搜索表单配置
const searchFormItems = [
  {
    label: '姓名',
    field: 'name',
    type: 'input',
    placeholder: '请输入姓名'
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    placeholder: '请选择状态',
    width: '180px',
    options: [
      { label: '在职', value: '1' },
      { label: '离职', value: '0' }
    ]
  }
];

// 重置查询
const resetQuery = () => {
  // 重置所有查询字段
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  // 重置到第一页
  resetPagination();
  loadData();
};

// 查询
const handleSearch = (values) => {
  if (values) {
    Object.assign(searchForm, values);
  }
  // 重置到第一页
  resetPagination();
  loadData();
};

// 表格列定义
const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: '10%',
  },
  {
    title: '归属部门',
    dataIndex: 'department',
    key: 'department',
    width: '10%',
  },
  {
    title: '岗位名称',
    dataIndex: 'position',
    key: 'position',
    width: '10%',
  },
  {
    title: '岗位等级',
    dataIndex: 'level',
    key: 'level',
    width: '10%',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: '15%',
  },
  {
    title: '性别',
    dataIndex: 'gender',
    key: 'gender',
    width: '8%',
  },
  {
    title: '入职时间',
    dataIndex: 'entryTime',
    key: 'entryTime',
    width: '15%',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '10%',
  },
  {
    title: '操作',
    key: 'action',
    width: '120px',
    fixed: 'right',
  },
];

// 状态变量
const loading = ref(false);
const employeeList = ref([]);
const selectedRowKeys = ref([]);

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: loadData,
  initialPagination: { 
  current: 1,
  pageSize: 10,
    total: 0, 
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  }
});

// 新增/编辑表单相关
const formRef = ref(null);
const modalVisible = ref(false);
const modalTitle = ref('新增员工');
const modalType = ref('add'); // add 或 edit
const currentRecord = ref(null);

const formState = reactive({
  name: '',
  phone: '',
  departmentId: null,
  positionId: null,
  levelId: null,
  gender: 'male',
  idCard: '',
  entryTime: null,
  status: '1',
});

const formRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号', trigger: 'blur' }
  ],
  departmentId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  positionId: [{ required: true, message: '请选择岗位', trigger: 'change' }],
  levelId: [{ required: true, message: '请选择岗位等级', trigger: 'change' }],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  entryTime: [{ required: true, message: '请选择入职时间', trigger: 'change' }],
};

// 部门选项
const departmentOptions = ref([
  {
    id: 1,
    value: 1,
    title: '餐烤餐考总部',
    children: [
      {
        id: 2,
        value: 2,
        title: '人事部',
      },
      {
        id: 3,
        value: 3,
        title: '技术部',
        children: [
          {
            id: 4,
            value: 4,
            title: '前端组',
          },
          {
            id: 5,
            value: 5,
            title: '后端组',
          }
        ]
      },
      {
        id: 6,
        value: 6,
        title: '市场部',
      }
    ]
  }
]);

// 岗位选项
const positionOptions = ref([
  { value: 1, label: '前端开发' },
  { value: 2, label: '后端开发' },
  { value: 3, label: 'UI设计师' },
  { value: 4, label: '产品经理' },
  { value: 5, label: '市场经理' },
  { value: 6, label: 'HR专员' },
]);

// 岗位等级选项
const levelOptions = ref([
  { value: 1, label: '初级' },
  { value: 2, label: '中级' },
  { value: 3, label: '高级' },
  { value: 4, label: '专家' },
]);

// 导入相关状态
const importModalVisible = ref(false);
const importLoading = ref(false);
const downloadLoading = ref(false);
const fileList = ref([]);
const importResult = ref(null);

// 编辑员工
const handleEdit = (record) => {
  modalType.value = 'edit';
  modalTitle.value = '编辑员工';
  currentRecord.value = record;
  
  formState.name = record.name;
  formState.phone = record.phone;
  formState.departmentId = getDepartmentId(record.department);
  formState.positionId = getPositionId(record.position);
  formState.levelId = getLevelId(record.level);
  formState.gender = record.gender;
  formState.idCard = record.idCard;
  formState.entryTime = record.entryTime;
  formState.status = record.status;
  
  modalVisible.value = true;
};

// 辅助函数：根据部门名称获取部门ID
const getDepartmentId = (name) => {
  // 实际应用中需要遍历部门树找到对应id
  if (name === '人事部') return 2;
  if (name === '技术部') return 3;
  if (name === '前端组') return 4;
  if (name === '后端组') return 5;
  if (name === '市场部') return 6;
  return 1; // 默认返回总部
};

// 辅助函数：根据岗位名称获取岗位ID
const getPositionId = (name) => {
  const position = positionOptions.value.find(item => item.label === name);
  return position ? position.value : null;
};

// 辅助函数：根据等级名称获取等级ID
const getLevelId = (name) => {
  const level = levelOptions.value.find(item => item.label === name);
  return level ? level.value : null;
};

// 删除员工
const handleDelete = (record) => {
  // 在实际应用中，这里会添加确认删除的逻辑
  message.success(`删除员工：${record.name}`);
};

// 打开新增弹窗
const handleAdd = () => {
  modalType.value = 'add';
  modalTitle.value = '新增员工';
  currentRecord.value = null;
  
  formState.name = '';
  formState.phone = '';
  formState.departmentId = null;
  formState.positionId = null;
  formState.levelId = null;
  formState.gender = 'male';
  formState.idCard = '';
  formState.entryTime = null;
  formState.status = '1';
  
  modalVisible.value = true;
};

// 确认弹窗
const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 在实际应用中，这里会调用保存数据的接口
    message.success(`${modalType.value === 'add' ? '新增' : '编辑'}员工成功`);
    modalVisible.value = false;
    // 重新加载数据
    loadData();
  }).catch(() => {
    // 表单验证失败
  });
};

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 导出员工数据
const handleExport = () => {
  const params = { ...searchForm };
  
  exportEmployees(params).then(res => {
    // 处理二进制流
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '员工数据.xlsx';
    link.click();
    URL.revokeObjectURL(link.href);
  }).catch(err => {
    message.error(`导出员工失败: ${err.message}`);
  });
};

// 打开导入弹窗
const handleImport = () => {
  importModalVisible.value = true;
  fileList.value = [];
  importResult.value = null;
};

// 下载导入模板
const handleDownloadTemplate = async () => {
  downloadLoading.value = true;
  try {
    const response = await downloadEmployeeTemplate();
    // 处理二进制流
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '员工导入模板.xlsx';
    link.click();
    URL.revokeObjectURL(link.href);
    message.success('模板下载成功');
  } catch (error) {
    message.error('模板下载失败');
  } finally {
    downloadLoading.value = false;
  }
};

// 文件上传前的处理
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }
  
  fileList.value = [file];
  importResult.value = null;
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = () => {
  fileList.value = [];
  importResult.value = null;
};

// 确认导入
const handleImportOk = async () => {
  if (fileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }
  
  importLoading.value = true;
  try {
    const formData = new FormData();
    formData.append('file', fileList.value[0]);
    
    const response = await importEmployees(formData);
    
    importResult.value = {
      type: 'success',
      message: response.message || `成功导入 ${response.data?.count || 0} 名员工`
    };
    
    // 重新加载数据
    loadData();
    
    // 3秒后关闭弹窗
    setTimeout(() => {
      importModalVisible.value = false;
    }, 3000);
    
  } catch (error) {
    importResult.value = {
      type: 'error',
      message: error.message || '导入失败'
    };
  } finally {
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  fileList.value = [];
  importResult.value = null;
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };
    const response = await getEmployeeList(params);
    // 处理后端返回的数据格式
    if (response.data) {
      employeeList.value = response.data.rows || [];
      // 更新分页信息
      updatePagination({
        total: response.data.total || 0,
        page: response.data.pageNum,
        pageSize: response.data.pageSize
      });
    } else {
      // 兼容旧的数据格式
      employeeList.value = response.list || response.rows || [];
      updatePagination({
        total: response.total || 0,
        page: response.page || response.pageNum,
        pageSize: response.pageSize
      });
    }
  } catch (error) {
    console.error('加载员工数据失败', error);
    message.error('加载员工数据失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.employee-container {
  width: 100%;
}

.search-area {
  margin-bottom: 16px;
}

.operation-area {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.primary-button {
  background-color: #a18cd1;
  border-color: #a18cd1;
  min-width: 88px;
  color: #fff;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 32px;
  line-height: 32px;
}

.primary-button:hover {
  background-color: #8a65c9;
  border-color: #8a65c9;
  color: #fff;
}

.import-content {
  padding: 16px 0;
}


.import-actions {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 24px;
  text-align: center;
}

.import-result {
  margin-top: 16px;
}
</style> 