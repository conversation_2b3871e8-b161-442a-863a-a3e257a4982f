<template>
  <div class="search-form-container">
    <a-form layout="inline" :model="formModel" class="search-form">
      <template v-for="item in formItems" :key="item.field">
        <a-form-item :label="item.label">
          <!-- 输入框 -->
          <a-input
            v-if="item.type === 'input'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || `请输入${item.label}`"
            :style="{ width: item.width || '120px' }"
            allowClear
            @change="() => handleItemChange(item.field)"
          />
          <!-- 选择框 -->
          <a-select
            v-else-if="item.type === 'select'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || `请选择${item.label}`"
            :style="{ width: item.width || '120px' }"
            :show-search="item.showSearch"
            :filter-option="item.showSearch ? (inputValue, option) => {
              // 从原始选项数据中查找对应的文本
              const options = item.options?.value || item.options || [];
              const targetOption = options.find(opt => opt[item.selectValue || 'value'] === option.value);
              const text = targetOption ? targetOption[item.selectLabel || 'label'] : '';
              return text.toString().toLowerCase().includes(inputValue.toLowerCase());
            } : undefined"
            allowClear
            @change="(val) => handleItemChange(item.field, val)"
          >
            <a-select-option v-for="option in (item.options?.value || item.options)" :key="option[item.selectValue||'value']" :value="option[item.selectValue||'value']">
              {{ option[item.selectLabel||'label'] }}
            </a-select-option>
          </a-select>

          <!-- 日期选择 -->
          <a-date-picker
            v-else-if="item.type === 'date'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || `请选择${item.label}`"
            :style="{ width: item.width || '120px' }"
            @change="() => handleItemChange(item.field)"
          />

          <!-- 日期范围 -->
          <a-range-picker
            v-else-if="item.type === 'dateRange'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || [`开始日期`, `结束日期`]"
            :style="{ width: item.width || '240px' }"
            @change="() => handleItemChange(item.field)"
          />

          <!-- 数字输入 -->
          <a-input-number
            v-else-if="item.type === 'number'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || `请输入${item.label}`"
            :style="{ width: item.width || '120px' }"
            @change="() => handleItemChange(item.field)"
          />

          <!-- 数字范围输入 -->
          <div
            v-else-if="item.type === 'range-number'"
            class="range-number-container"
            :style="item.style || { width: '240px' }"
          >
            <a-input-number
              v-model:value="formModel[item.field][0]"
              :placeholder="item.placeholder ? item.placeholder[0] : '最小值'"
              style="width: calc(50% - 12px)"
              @change="() => handleItemChange(item.field)"
            />
            <span class="range-separator">至</span>
            <a-input-number
              v-model:value="formModel[item.field][1]"
              :placeholder="item.placeholder ? item.placeholder[1] : '最大值'"
              style="width: calc(50% - 12px)"
              @change="() => handleItemChange(item.field)"
            />
          </div>

          <!-- 日期时间范围选择器 -->
          <a-range-picker
            v-else-if="item.type === 'range-picker'"
            v-model:value="formModel[item.field]"
            :placeholder="item.placeholder || ['开始时间', '结束时间']"
            :style="item.style || { width: '240px' }"
            :show-time="item.showTime !== false"
            @change="() => handleItemChange(item.field)"
          />
        </a-form-item>
      </template>

      <!-- 按钮区域 -->
      <a-form-item>
        <a-space>
          <a-button type="primary" class="search-button" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            {{ searchText }}
          </a-button>
          <a-button class="search-button" @click="handleReset">
            <template #icon><ReloadOutlined /></template>
            {{ resetText }}
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, watch, toRefs } from 'vue';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'SearchForm',
  components: {
    SearchOutlined,
    ReloadOutlined,
  },
  props: {
    // 表单项配置
    items: {
      type: Array,
      default: () => [],
    },
    // 表单模型绑定值
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    // 搜索按钮文本
    searchText: {
      type: String,
      default: '查询',
    },
    // 重置按钮文本
    resetText: {
      type: String,
      default: '重置',
    },
    // 搜索时是否重置分页到第一页
    resetPage: {
      type: Boolean,
      default: true,
    },
    // 是否使用卡片样式包裹
    useCard: {
      type: Boolean,
      default: false,
    },
    // 搜索表单额外类名
    formClass: {
      type: String,
      default: '',
    },
  },
  emits: ['search', 'reset', 'update:modelValue'],
  setup(props, { emit }) {
    const { modelValue, items } = toRefs(props);
    // 内部表单数据模型
    const formModel = reactive({...modelValue.value});

    // 确保数组字段被正确初始化
    items.value.forEach(item => {
      if ((item.type === 'range-number' || item.type === 'range-picker' || item.type === 'dateRange') && !formModel[item.field]) {
        formModel[item.field] = [];
      }
    });

    // 表单项列表
    const formItems = ref(items.value);

    // 同步父组件传入的值和内部表单值
    watch(() => modelValue.value, (newVal) => {
      Object.assign(formModel, newVal);
    }, { deep: true });

    // 表单项变化处理
    const handleItemChange = (field, value) => {
      // 立即触发更新
      emit('update:modelValue', {...formModel});
    };

    // 同步内部表单值到父组件
    watch(formModel, (newVal) => {
      emit('update:modelValue', {...newVal});
    }, { deep: true });

    // 搜索处理
    const handleSearch = () => {
      emit('search', {...formModel});
    };

    // 重置处理
    const handleReset = () => {
      // 重置表单
      for (const key in formModel) {
        if (Object.prototype.hasOwnProperty.call(formModel, key)) {
          if (Array.isArray(formModel[key])) {
            formModel[key] = [];
          } else if (typeof formModel[key] === 'object' && formModel[key] !== null) {
            formModel[key] = {};
          } else {
            formModel[key] = undefined;
          }
        }
      }

      // 发送重置事件
      emit('reset', {...formModel,pageNum:1,pageSize:10});
    };

    return {
      formModel,
      formItems,
      handleSearch,
      handleReset,
      handleItemChange
    };
  },
});
</script>

<style lang="scss" scoped>
.search-form-container {
  width: 100%;

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;

    .ant-form-item {
      margin-right: 16px;

      &.full-width {
        flex: 1 1 100%;
        display: flex;
        flex-direction: column;

        :deep(.ant-form-item-control) {
          width: 100%;

          .ant-select {
            width: 100% !important;
          }

          .ant-input {
            width: 100% !important;
          }
        }
      }
    }
  }

  .range-number-container {
    display: flex;
    align-items: center;

    .range-separator {
      margin: 0 8px;
      color: #999;
      font-size: 12px;
    }
  }
}
</style>
