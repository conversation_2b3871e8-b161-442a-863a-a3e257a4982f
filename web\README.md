# 餐烤餐考后台管理系统

这是餐烤餐考的后台管理系统前端工程，基于Vue 3开发。

## 项目介绍

- 前端技术栈：Vue 3 + Ant Design Vue + Vite
- 主色调：
  - 主色：#a18cd1 (紫色)
  - 辅助色：#fbc2eb (粉色)
  - 渐变色：linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%) (马卡龙渐变色)

## 主要功能

- 登录页面：用户登录界面
- 企业管理：
  - 企业列表：查看、新增、编辑、启用/禁用企业信息
  - 智能体列表：管理企业的智能体
  - 知识库列表：管理企业的知识库

## 账号信息

测试账号：
- 用户名：admin
- 密码：123456

## 项目启动

1. 安装依赖
```bash
npm install
```

2. 开发环境启动
```bash
npm run dev
```

3. 访问方式
   - 本地访问：http://localhost:5173
   - 局域网访问：http://[本机IP]:5173（启动时会显示具体地址）

4. 生产环境构建
```bash
npm run build
```

## 后续计划

- 集成后端API
- 添加更多功能模块
- 优化页面性能
- 添加单元测试

## 开发者

阿依来团队

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## 项目结构
web
  ├── public/           # 静态资源
  ├── scripts/          # 脚本文件
  ├── src/              # 源代码
  │   ├── api/          # API接口
  │   ├── assets/       # 静态资源
  │   ├── components/   # 全局组件
  │   │   ├── BaseTable/  # 表格组件
  │   │   ├── icons/      # 图标组件
  │   │   ├── PageHeader/ # 页头组件
  │   │   └── SearchForm/ # 搜索表单组件
  │   ├── layout/       # 布局组件
  │   ├── mock/         # 模拟数据
  │   ├── router/       # 路由配置
  │   ├── store/        # 状态管理
  │   ├── utils/        # 工具函数
  │   └── views/        # 页面组件
  ├── .env              # 环境变量
  ├── index.html        # 入口HTML
  ├── jsconfig.json     # JS配置
  ├── package.json      # 项目依赖
  ├── README.md         # 项目说明
  └── vite.config.js    # Vite配置

## 组件说明

### BaseTable 基础表格组件

BaseTable 是一个封装了 Ant Design Vue 的表格组件，提供了常用的表格功能和界面，便于在系统中统一维护和使用。

#### 功能特点

- 统一的表格样式和交互
- 内置默认操作列（详情、编辑、删除）
- 灵活的自定义插槽机制
- 完整的事件支持

详细使用说明参见 [BaseTable README](./src/components/BaseTable/README.md)

### table-helpers 表格辅助工具

提供了表格相关的通用操作函数：

- handleTableChange: 处理表格分页、排序、筛选变化
- resetSearch: 重置搜索表单
- handleSearch: 处理搜索操作

详细使用说明参见源码注释

## 开发指南

### 安装依赖
```bash
npm install
```

### 开发环境启动
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```
