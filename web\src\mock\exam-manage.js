// 练考管理模块的模拟数据

// 练考配置数据
export const examConfigData = [
  {
    id: 1,
    positionBelong: '前厅',
    positionName: '前厅服务员',
    positionLevel: '初级',
    examSubject: '前厅服务基础知识',
    status: '必练',
    practiceDuration: 60,
    questionCount: 20,
    examCount: 3,
    examDuration: 90,
    needConfirmScore: true,
    passScore: 80,
    scoreReleaseRule: '自动发布',
    questionDistribution: '选择题70%，问答题30%',
    questionType: '单选,多选',
    difficultyLevel: '初级',
    createdTime: '2023-07-15 10:30:00',
    updatedTime: '2023-09-12 14:45:00'
  },
  {
    id: 2,
    positionBelong: '后厨',
    positionName: '厨师助理',
    positionLevel: '中级',
    examSubject: '厨房安全与卫生',
    status: '必考',
    practiceDuration: 45,
    questionCount: 15,
    examCount: 2,
    examDuration: 60,
    needConfirmScore: true,
    passScore: 85,
    scoreReleaseRule: '手动发布',
    questionDistribution: '选择题60%，问答题40%',
    questionType: '单选,多选,情景模拟',
    difficultyLevel: '中级',
    createdTime: '2023-06-28 09:15:00',
    updatedTime: '2023-08-22 11:20:00'
  },
  {
    id: 3,
    positionBelong: '后厨',
    positionName: '烧烤师',
    positionLevel: '高级',
    examSubject: '烧烤技术精进',
    status: '必练',
    practiceDuration: 90,
    questionCount: 25,
    examCount: 1,
    examDuration: 120,
    needConfirmScore: false,
    passScore: 90,
    scoreReleaseRule: '自动发布',
    questionDistribution: '选择题50%，问答题50%',
    questionType: '单选,多选,情景模拟',
    difficultyLevel: '高级',
    createdTime: '2023-05-15 14:20:00',
    updatedTime: '2023-07-30 16:30:00'
  },
  {
    id: 4,
    positionBelong: '前厅',
    positionName: '礼宾员',
    positionLevel: '中级',
    examSubject: '客户服务与礼仪',
    status: '必考',
    practiceDuration: 60,
    questionCount: 20,
    examCount: 2,
    examDuration: 90,
    needConfirmScore: true,
    passScore: 85,
    scoreReleaseRule: '手动发布',
    questionDistribution: '选择题60%，问答题40%',
    questionType: '单选,多选',
    difficultyLevel: '中级',
    createdTime: '2023-04-20 11:00:00',
    updatedTime: '2023-06-18 09:45:00'
  },
  {
    id: 5,
    positionBelong: '后厨',
    positionName: '食材管理员',
    positionLevel: '初级',
    examSubject: '食材存储与管理',
    status: '必练',
    practiceDuration: 45,
    questionCount: 15,
    examCount: 3,
    examDuration: 60,
    needConfirmScore: false,
    passScore: 80,
    scoreReleaseRule: '自动发布',
    questionDistribution: '选择题80%，问答题20%',
    questionType: '单选,多选',
    difficultyLevel: '初级',
    createdTime: '2023-03-12 08:30:00',
    updatedTime: '2023-06-20 10:15:00'
  }
];

// 考试审核数据
export const examReviewData = [
  {
    id: 1,
    applicant: '张三',
    certificateName: '前厅服务资格证',
    positionBelong: '前厅',
    positionName: '前厅服务员',
    positionLevel: '初级',
    applyTime: '2023-09-25 09:30:00',
    reviewStatus: '审核中',
    scoreConfirmStatus: '待审核',
    reviewer: '',
    reviewTime: '',
    confirmPerson: '',
    confirmTime: '',
    examTime: ''
  },
  {
    id: 2,
    applicant: '李四',
    certificateName: '厨师助理资格证',
    positionBelong: '后厨',
    positionName: '厨师助理',
    positionLevel: '中级',
    applyTime: '2023-09-24 10:15:00',
    reviewStatus: '已通过',
    scoreConfirmStatus: '待审核',
    reviewer: '王主管',
    reviewTime: '2023-09-24 15:30:00',
    confirmPerson: '',
    confirmTime: '',
    examTime: '2023-10-05 14:00:00'
  },
  {
    id: 3,
    applicant: '王五',
    certificateName: '烧烤师资格证',
    positionBelong: '后厨',
    positionName: '烧烤师',
    positionLevel: '高级',
    applyTime: '2023-09-23 14:20:00',
    reviewStatus: '已完成',
    scoreConfirmStatus: '已通过',
    reviewer: '赵经理',
    reviewTime: '2023-09-23 16:45:00',
    confirmPerson: '赵经理',
    confirmTime: '2023-09-30 11:20:00',
    examTime: '2023-09-30 09:00:00'
  },
  {
    id: 4,
    applicant: '赵六',
    certificateName: '礼宾员资格证',
    positionBelong: '前厅',
    positionName: '礼宾员',
    positionLevel: '中级',
    applyTime: '2023-09-22 11:40:00',
    reviewStatus: '已驳回',
    scoreConfirmStatus: '待审核',
    reviewer: '钱主管',
    reviewTime: '2023-09-22 17:00:00',
    confirmPerson: '',
    confirmTime: '',
    examTime: ''
  },
  {
    id: 5,
    applicant: '孙七',
    certificateName: '食材管理资格证',
    positionBelong: '后厨',
    positionName: '食材管理员',
    positionLevel: '初级',
    applyTime: '2023-09-21 09:10:00',
    reviewStatus: '已通过',
    scoreConfirmStatus: '已驳回',
    reviewer: '周经理',
    reviewTime: '2023-09-21 13:40:00',
    confirmPerson: '周经理',
    confirmTime: '2023-09-28 15:30:00',
    examTime: '2023-09-28 13:00:00'
  },
  {
    id: 6,
    applicant: '周八',
    certificateName: '咖啡师资格证',
    positionBelong: '前厅',
    positionName: '咖啡师',
    positionLevel: '高级',
    applyTime: '2023-09-20 13:25:00',
    reviewStatus: '已完成',
    scoreConfirmStatus: '已通过',
    reviewer: '郑经理',
    reviewTime: '2023-09-20 15:10:00',
    confirmPerson: '郑经理',
    confirmTime: '2023-09-27 10:45:00',
    examTime: '2023-09-27 09:00:00'
  }
];

// 练习记录数据
export const practiceRecordData = [
  {
    id: 1,
    practiceContent: '前厅服务基础知识',
    practiceSubject: '前厅服务礼仪',
    positionBelong: '前厅',
    positionName: '前厅服务员',
    positionLevel: '初级',
    category: '礼仪规范',
    practicePerson: '张三',
    startTime: '2023-09-25 09:30:00',
    duration: 45,
    questionCount: 20,
    studyDuration: 45
  },
  {
    id: 2,
    practiceContent: '厨房安全与卫生',
    practiceSubject: '后厨安全规范',
    positionBelong: '后厨',
    positionName: '厨师助理',
    positionLevel: '中级',
    category: '安全操作',
    practicePerson: '李四',
    startTime: '2023-09-24 10:15:00',
    duration: 60,
    questionCount: 15,
    studyDuration: 55
  },
  {
    id: 3,
    practiceContent: '烧烤技术精进',
    practiceSubject: '烤制技巧',
    positionBelong: '后厨',
    positionName: '烧烤师',
    positionLevel: '高级',
    category: '技术指南',
    practicePerson: '王五',
    startTime: '2023-09-23 14:20:00',
    duration: 90,
    questionCount: 25,
    studyDuration: 85
  },
  {
    id: 4,
    practiceContent: '客户服务与礼仪',
    practiceSubject: '接待礼仪',
    positionBelong: '前厅',
    positionName: '礼宾员',
    positionLevel: '中级',
    category: '礼仪规范',
    practicePerson: '赵六',
    startTime: '2023-09-22 11:40:00',
    duration: 60,
    questionCount: 20,
    studyDuration: 58
  },
  {
    id: 5,
    practiceContent: '食材存储与管理',
    practiceSubject: '食材保鲜技巧',
    positionBelong: '后厨',
    positionName: '食材管理员',
    positionLevel: '初级',
    category: '食材管理',
    practicePerson: '孙七',
    startTime: '2023-09-21 09:10:00',
    duration: 45,
    questionCount: 15,
    studyDuration: 40
  }
];

// 考试记录数据
export const examRecordData = [
  {
    id: 1,
    examSubject: '前厅服务基础知识',
    examTime: '2023-09-15 09:30:00',
    positionBelong: '前厅',
    positionName: '前厅服务员',
    positionLevel: '初级',
    category: '礼仪规范',
    examinee: '张三',
    usedDuration: 85,
    score: 88,
    confirmStatus: '已确认',
    confirmPerson: '王主管'
  },
  {
    id: 2,
    examSubject: '厨房安全与卫生',
    examTime: '2023-09-14 10:15:00',
    positionBelong: '后厨',
    positionName: '厨师助理',
    positionLevel: '中级',
    category: '安全操作',
    examinee: '李四',
    usedDuration: 55,
    score: 92,
    confirmStatus: '已确认',
    confirmPerson: '赵经理'
  },
  {
    id: 3,
    examSubject: '烧烤技术精进',
    examTime: '2023-09-13 14:20:00',
    positionBelong: '后厨',
    positionName: '烧烤师',
    positionLevel: '高级',
    category: '技术指南',
    examinee: '王五',
    usedDuration: 110,
    score: 95,
    confirmStatus: '待确认',
    confirmPerson: ''
  },
  {
    id: 4,
    examSubject: '客户服务与礼仪',
    examTime: '2023-09-12 11:40:00',
    positionBelong: '前厅',
    positionName: '礼宾员',
    positionLevel: '中级',
    category: '礼仪规范',
    examinee: '赵六',
    usedDuration: 75,
    score: 78,
    confirmStatus: '已确认',
    confirmPerson: '钱主管'
  },
  {
    id: 5,
    examSubject: '食材存储与管理',
    examTime: '2023-09-11 09:10:00',
    positionBelong: '后厨',
    positionName: '食材管理员',
    positionLevel: '初级',
    category: '食材管理',
    examinee: '孙七',
    usedDuration: 50,
    score: 85,
    confirmStatus: '待确认',
    confirmPerson: ''
  }
];

// 证书记录数据
export const certificateRecordData = [
  {
    id: 1,
    certificateName: '前厅服务资格证',
    obtainTime: '2023-09-16 15:30:00',
    positionName: '前厅服务员',
    positionBelong: '前厅',
    positionLevel: '初级',
    employeeName: '张三'
  },
  {
    id: 2,
    certificateName: '厨师助理资格证',
    obtainTime: '2023-09-15 14:20:00',
    positionName: '厨师助理',
    positionBelong: '后厨',
    positionLevel: '中级',
    employeeName: '李四'
  },
  {
    id: 3,
    certificateName: '烧烤师资格证',
    obtainTime: '2023-09-14 16:45:00',
    positionName: '烧烤师',
    positionBelong: '后厨',
    positionLevel: '高级',
    employeeName: '王五'
  },
  {
    id: 4,
    certificateName: '礼宾员资格证',
    obtainTime: '2023-09-13 11:10:00',
    positionName: '礼宾员',
    positionBelong: '前厅',
    positionLevel: '中级',
    employeeName: '赵六'
  },
  {
    id: 5,
    certificateName: '食材管理资格证',
    obtainTime: '2023-09-12 09:40:00',
    positionName: '食材管理员',
    positionBelong: '后厨',
    positionLevel: '初级',
    employeeName: '孙七'
  },
  {
    id: 6,
    certificateName: '咖啡师资格证',
    obtainTime: '2023-09-11 10:20:00',
    positionName: '咖啡师',
    positionBelong: '前厅',
    positionLevel: '高级',
    employeeName: '周八'
  },
  {
    id: 7,
    certificateName: '调酒师资格证',
    obtainTime: '2023-09-10 14:30:00',
    positionName: '调酒师',
    positionBelong: '前厅',
    positionLevel: '高级',
    employeeName: '吴九'
  },
  {
    id: 8,
    certificateName: '餐厅经理资格证',
    obtainTime: '2023-09-09 16:15:00',
    positionName: '餐厅经理',
    positionBelong: '前厅',
    positionLevel: '高级',
    employeeName: '郑十'
  }
]; 