<template>
  <div class="agent-container">
    <div class="agent-list-container">
      <!-- 搜索表单 -->
      <div class="search-box">
        <search-form-card
          :model-value="searchForm"
          :items="searchItems"
          @search="handleSearch"
          @reset="resetSearch"
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-box">
        <a-button class="custom-button" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          新增智能体
        </a-button>
      </div>
      
      <!-- 数据表格 -->
      <div class="table-box">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          bordered
          rowKey="id"
          :scroll="{ x: 1500 }"
          @change="handleTableChange"
        >
          <!-- 状态列自定义渲染 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="record.status ? 'success' : 'error'">
                {{ record.status ? '启用' : '禁用' }}
              </a-tag>
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.key === 'action'">
              <a-space size="small" class="action-buttons">
                <a-button type="link" size="small" @click="handleEdit(record)" class="action-button">编辑</a-button>
                <a-button type="link" size="small" @click="handleConfig(record)" class="action-button">配置智能体</a-button>
                <a-button type="link" size="small" @click="handleToggleStatus(record)" class="action-button">
                  {{ record.status ? '禁用' : '启用' }}
                </a-button>
                <a-popconfirm
                  title="确定要删除此智能体吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" size="small" class="action-button">删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
      
      <!-- 新增/编辑对话框 -->
      <a-modal
        v-model:visible="modalVisible"
        :title="modalTitle"
        @ok="handleModalSubmit"
        @cancel="handleModalCancel"
        :confirm-loading="modalLoading"
      >
        <a-form
          :model="formData"
          :rules="formRules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="智能体名称" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入智能体名称" />
          </a-form-item>
          
          <a-form-item label="智能体编码" name="code">
            <a-input v-model:value="formData.code" placeholder="请输入智能体编码" />
          </a-form-item>
          
          <a-form-item label="类型" name="type">
            <a-select
              v-model:value="formData.type"
              placeholder="请选择智能体类型"
            >
              <a-select-option value="客服">客服</a-select-option>
              <a-select-option value="销售">销售</a-select-option>
              <a-select-option value="数据分析">数据分析</a-select-option>
              <a-select-option value="内容创作">内容创作</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="所属企业" name="enterpriseId">
            <a-select
              v-model:value="formData.enterpriseId"
              placeholder="请选择所属企业"
            >
              <a-select-option 
                v-for="item in enterpriseList" 
                :key="item.id" 
                :value="item.id"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="智能体描述" name="description">
            <a-textarea
              v-model:value="formData.description"
              placeholder="请输入智能体描述"
              :rows="4"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { 
  getAgentList, 
  createAgent, 
  updateAgent, 
  deleteAgent 
} from '@/api/enterprise/agent';
import { getEnterpriseList } from '@/api/enterprise';
import { SearchFormCard } from '@/components/SearchForm';

const router = useRouter();
const route = useRoute();
const formRef = ref(null);

// 查询表单数据
const searchForm = reactive({
  name: '',
  code: '',
  type: undefined,
  enterpriseId: undefined,
  status: undefined
});

// 搜索表单项配置
const searchItems = [
  {
    label: '智能体名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入智能体名称'
  },
  {
    label: '智能体编码',
    field: 'code',
    type: 'input',
    placeholder: '请输入智能体编码'
  },
  {
    label: '类型',
    field: 'type',
    type: 'select',
    width: '120px',
    options: [
      { label: '全部', value: '' },
      { label: '客服', value: '客服' },
      { label: '销售', value: '销售' },
      { label: '数据分析', value: '数据分析' },
      { label: '内容创作', value: '内容创作' },
      { label: '其他', value: '其他' }
    ]
  },
  {
    label: '所属企业',
    field: 'enterpriseId',
    type: 'select',
    width: '220px',
    options: computed(() => {
      const baseOption = [{ label: '全部', value: '' }];
      return baseOption.concat(enterpriseList.value.map(item => ({
        label: item.name,
        value: item.id
      })));
    })
  },
  {
    label: '状态',
    field: 'status',
    type: 'select',
    width: '120px',
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: 'true' },
      { label: '禁用', value: 'false' }
    ]
  }
];

// 表格加载状态
const loading = ref(false);

// 表格分页设置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条数据`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列定义
const columns = [
  {
    title: '智能体名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    width: 180
  },
  {
    title: '智能体编码',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '所属企业',
    dataIndex: 'enterpriseName',
    key: 'enterpriseName',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 320,
    fixed: 'right'
  }
];

// 企业列表
const enterpriseList = ref([]);
const enterpriseLoading = ref(false);

// 表格数据
const tableData = ref([]);

// 模态框相关
const modalVisible = ref(false);
const modalMode = ref('add'); // 'add' 或 'edit'
const modalTitle = computed(() => modalMode.value === 'add' ? '新增智能体' : '编辑智能体');
const modalLoading = ref(false);

// 表单数据
const formData = reactive({
  id: undefined,
  name: '',
  code: '',
  type: undefined,
  description: '',
  enterpriseId: undefined,
  status: true
});

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入智能体编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择智能体类型', trigger: 'change' }],
  enterpriseId: [{ required: true, message: '请选择所属企业', trigger: 'change' }]
};

// 初始化数据
onMounted(() => {
  fetchAgentList();
  fetchEnterpriseList();
});

// 获取企业列表
const fetchEnterpriseList = async () => {
  try {
    enterpriseLoading.value = true;
    const res = await getEnterpriseList({ pageSize: 999 });
    enterpriseList.value = res.list;
  } catch (error) {
    message.error('获取企业列表失败: ' + error.message);
  } finally {
    enterpriseLoading.value = false;
  }
};

// 获取智能体列表数据
const fetchAgentList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    };
    
    const res = await getAgentList(params);
    tableData.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    message.error('获取智能体列表失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchAgentList();
};

// 查询
const handleSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  }
  pagination.current = 1;
  fetchAgentList();
};

// 重置搜索
const resetSearch = (values) => {
  // 如果values不为空，使用values更新searchForm
  if (values) {
    Object.assign(searchForm, values);
  } else {
    searchForm.name = '';
    searchForm.code = '';
    searchForm.type = undefined;
    searchForm.enterpriseId = undefined;
    searchForm.status = undefined;
  }
  pagination.current = 1;
  fetchAgentList();
};

// 显示新增对话框
const showAddModal = () => {
  modalMode.value = 'add';
  resetFormData();
  modalVisible.value = true;
};

// 重置表单数据
const resetFormData = () => {
  formData.id = undefined;
  formData.name = '';
  formData.code = '';
  formData.type = undefined;
  formData.description = '';
  formData.enterpriseId = undefined;
  formData.status = true;
  
  // 重置表单校验状态
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理编辑
const handleEdit = (record) => {
  modalMode.value = 'edit';
  
  // 填充表单数据
  Object.assign(formData, record);
  
  modalVisible.value = true;
};

// 对话框提交
const handleModalSubmit = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    if (modalMode.value === 'add') {
      // 新增智能体
      await createAgent(formData);
      message.success('新增智能体成功');
    } else {
      // 编辑智能体
      await updateAgent(formData);
      message.success('编辑智能体成功');
    }
    
    modalVisible.value = false;
    fetchAgentList();
  } catch (error) {
    message.error('操作失败: ' + error.message);
  } finally {
    modalLoading.value = false;
  }
};

// 对话框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 状态切换
const handleToggleStatus = (record) => {
  Modal.confirm({
    title: '提示',
    content: `确定要${record.status ? '禁用' : '启用'}【${record.name}】吗？`,
    onOk: async () => {
      try {
        loading.value = true;
        await updateAgent({
          ...record,
          status: !record.status
        });
        message.success(`${record.status ? '禁用' : '启用'}成功`);
        fetchAgentList();
      } catch (error) {
        message.error(`操作失败: ${error.message}`);
      } finally {
        loading.value = false;
      }
    }
  });
};

// 处理删除
const handleDelete = async (record) => {
  try {
    loading.value = true;
    await deleteAgent(record.id);
    message.success('删除智能体成功');
    
    // 如果当前页只有一条数据且不是第一页，则跳转到上一页
    if (tableData.value.length === 1 && pagination.current > 1) {
      pagination.current--;
    }
    
    fetchAgentList();
  } catch (error) {
    message.error('删除智能体失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 配置智能体
const handleConfig = (record) => {
  message.info(`配置智能体: ${record.name}`);
};
</script>

<style scoped>
.agent-container {
  padding: 15px 0;
  width: 100%;
}

.breadcrumb-container {
  margin-bottom: 16px;
  padding: 8px 0;
}

.search-box {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

.action-box {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.table-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  overflow-x: auto;
}

/* 固定列样式 */
:deep(.ant-table-cell-fix-right) {
  background-color: #fff;
}
</style> 