<template>
    <router-view />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
  font-size: 14px;
  color: #333333;
}

#app {
  height: 100%;
  width: 100%;
}

a {
  text-decoration: none;
  color: #a18cd1;
}

a:hover {
  color: #fbc2eb;
}

/* 自定义 Ant Design 主题 */
:root {
  --primary-color: #a18cd1;
  --primary-hover-color: #b6a6d8;
  --primary-active-color: #f1ecfa;
  --secondary-color: #fbc2eb;
}

.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--primary-hover-color);
  border-color: var(--primary-hover-color);
}
.ant-btn-primary:not(:disabled):hover {
  background-color: var(--primary-hover-color);
  border-color: var(--primary-hover-color);
}
.ant-btn-default:not(:disabled):hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-pagination-item-active {
  border-color: var(--primary-color);
}

.ant-pagination-item-active a {
  color: var(--primary-color);
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-radio-checked .ant-radio-inner {
  border-color: var(--primary-color);
}

.ant-radio-inner::after {
  background-color: var(--primary-color);
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-radio-wrapper:hover .ant-radio-inner,
.ant-radio:hover .ant-radio-inner {
  border-color: var(--primary-color);
}
</style>
