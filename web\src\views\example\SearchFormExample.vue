<template>
  <div class="search-form-example-container">
    <page-header 
      title="搜索表单组件示例"
    >
    </page-header>
    
    <div class="section">
      <h3>基础搜索表单</h3>
      <search-form
        v-model="basicSearchForm"
        :items="basicSearchItems"
        @search="handleBasicSearch"
        @reset="handleBasicReset"
      />
      <div class="result-box">
        <h4>搜索结果:</h4>
        <pre>{{ JSON.stringify(basicSearchResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="section">
      <h3>带卡片样式的搜索表单</h3>
      <search-form-card
        v-model="cardSearchForm"
        :items="cardSearchItems"
        @search="handleCardSearch"
        @reset="handleCardReset"
      />
      <div class="result-box">
        <h4>搜索结果:</h4>
        <pre>{{ JSON.stringify(cardSearchResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="section">
      <h3>更多表单控件类型</h3>
      <search-form-card
        v-model="advancedSearchForm"
        :items="advancedSearchItems"
        @search="handleAdvancedSearch"
        @reset="handleAdvancedReset"
      />
      <div class="result-box">
        <h4>搜索结果:</h4>
        <pre>{{ JSON.stringify(advancedSearchResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue';
import { SearchForm, SearchFormCard } from '@/components/SearchForm';


export default defineComponent({
  name: 'SearchFormExample',
  components: {
    SearchForm,
    SearchFormCard,
    PageHeader
  },
  setup() {
    // 基础搜索表单
    const basicSearchForm = reactive({
      name: '',
      code: '',
      status: undefined,
    });
    
    const basicSearchItems = [
      {
        label: '名称',
        field: 'name',
        type: 'input',
        placeholder: '请输入名称',
      },
      {
        label: '编码',
        field: 'code',
        type: 'input',
        placeholder: '请输入编码',
      },
      {
        label: '状态',
        field: 'status',
        type: 'select',
        width: '120px',
        options: [
          { label: '全部', value: '' },
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' },
        ],
      },
    ];
    
    const basicSearchResult = ref({});
    
    const handleBasicSearch = (values) => {
      basicSearchResult.value = values;
    };
    
    const handleBasicReset = (values) => {
      basicSearchResult.value = values;
    };
    
    // 带卡片样式的搜索表单
    const cardSearchForm = reactive({
      enterpriseName: '',
      enterpriseType: undefined,
      createDate: undefined,
    });
    
    const cardSearchItems = [
      {
        label: '企业名称',
        field: 'enterpriseName',
        type: 'input',
        placeholder: '请输入企业名称',
      },
      {
        label: '企业类型',
        field: 'enterpriseType',
        type: 'select',
        width: '180px',
        options: [
          { label: '全部', value: '' },
          { label: '科技公司', value: 'tech' },
          { label: '金融机构', value: 'finance' },
          { label: '教育机构', value: 'education' },
          { label: '其他', value: 'other' },
        ],
      },
      {
        label: '创建日期',
        field: 'createDate',
        type: 'date',
        placeholder: '请选择创建日期',
      },
    ];
    
    const cardSearchResult = ref({});
    
    const handleCardSearch = (values) => {
      cardSearchResult.value = values;
    };
    
    const handleCardReset = (values) => {
      cardSearchResult.value = values;
    };
    
    // 更多表单控件类型
    const advancedSearchForm = reactive({
      keyword: '',
      category: undefined,
      priceRange: undefined,
      dateRange: [],
      quantity: undefined,
    });
    
    const advancedSearchItems = [
      {
        label: '关键词',
        field: 'keyword',
        type: 'input',
        placeholder: '请输入关键词',
      },
      {
        label: '分类',
        field: 'category',
        type: 'select',
        width: '160px',
        showSearch: true,
        options: [
          { label: '全部', value: '' },
          { label: '类别一', value: 'category1' },
          { label: '类别二', value: 'category2' },
          { label: '类别三', value: 'category3' },
        ],
      },
      {
        label: '日期范围',
        field: 'dateRange',
        type: 'dateRange',
        placeholder: ['开始日期', '结束日期'],
      },
      {
        label: '数量',
        field: 'quantity',
        type: 'number',
        placeholder: '请输入数量',
      },
    ];
    
    const advancedSearchResult = ref({});
    
    const handleAdvancedSearch = (values) => {
      advancedSearchResult.value = values;
    };
    
    const handleAdvancedReset = (values) => {
      advancedSearchResult.value = values;
    };
    
    return {
      basicSearchForm,
      basicSearchItems,
      basicSearchResult,
      handleBasicSearch,
      handleBasicReset,
      
      cardSearchForm,
      cardSearchItems,
      cardSearchResult,
      handleCardSearch,
      handleCardReset,
      
      advancedSearchForm,
      advancedSearchItems,
      advancedSearchResult,
      handleAdvancedSearch,
      handleAdvancedReset,
    };
  },
});
</script>

<style lang="scss" scoped>
.search-form-example-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
    }
  }
  
  .section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    
    h3 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
    
    .result-box {
      margin-top: 16px;
      padding: 16px;
      background-color: #f8f8f8;
      border-radius: 4px;
      
      h4 {
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
      }
      
      pre {
        padding: 8px;
        background-color: #f2f2f2;
        border-radius: 4px;
        font-family: monospace;
        font-size: 13px;
      }
    }
  }
}
</style> 