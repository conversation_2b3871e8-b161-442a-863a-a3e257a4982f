import request from '@/utils/request'

// 获取知识库列表
export function getKnowledgeList(params) {
  return request({
    url: '/system/knowledge/list',
    method: 'get',
    params
  })
}

// 获取知识库详情
export function getKnowledgeDetail(id) {
  return request({
    url: `/system/knowledge/detail/${id}`,
    method: 'get'
  })
}

// 创建知识库
export function createKnowledge(data) {
  return request({
    url: '/system/knowledge',
    method: 'post',
    data
  })
}

// 更新知识库
export function updateKnowledge(data) {
  return request({
    url: '/system/knowledge',
    method: 'put',
    data
  })
}

// 删除知识库
export function deleteKnowledge(id) {
  return request({
    url: `/system/knowledge/${id}`,
    method: 'delete'
  })
}

// 获取知识库文档段落
export function getDocumentSegments(id) {
  return request({
    url: `/system/knowledge/${id}/segments`,
    method: 'get'
  })
}

// 根据文档段落智能出题
export function generateQuestions(id) {
  return request({
    url: `/system/knowledge/${id}/generate-questions`,
    method: 'post'
  })
}

// 获取知识库题目列表
export function getQuestionList(id) {
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'get'
  })
}

// 添加固定题目
export function addQuestion(id, data) {
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'post',
    data
  })
}

// 更新题目
export function updateQuestion(id, data) {
  return request({
    url: `/knowledge-base/${id}/questions`,
    method: 'put',
    data
  })
}

/**
 * 删除题目
 * @param {string} knowledgeId - 知识库ID
 * @param {string} questionId - 题目ID
 * @returns {Promise<any>} - 响应数据
 */
export function deleteQuestion(knowledgeId, questionId) {
  return request({
    url: `/knowledge-base/${knowledgeId}/questions/${questionId}`,
    method: 'delete'
  });
}

/**
 * 批量保存题目
 * @param {string} knowledgeId - 知识库ID
 * @param {array} questions - 题目数组
 * @returns {Promise<any>} - 响应数据
 */
export function batchSaveQuestions(knowledgeId, questions) {
  return request({
    url: `/kb/${knowledgeId}/batch-save-questions`,
    method: 'post',
    data: { questions }
  });
}

// 批量保存文档
export function batchSaveDocuments(data) {
  return request({
    url: '/system/knowledge/batch',
    method: 'post',
    data
  })
} 