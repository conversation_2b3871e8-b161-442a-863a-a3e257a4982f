<template>
  <a-card class="document-card" :loading="loading" :bordered="false" hoverable>
    <div class="document-icon">
      <!-- 根据文件类型显示不同图标 -->
      <file-pdf-outlined v-if="document.fileType === 'pdf'" class="icon pdf-icon" />
      <file-word-outlined v-if="document.fileType === 'word'" class="icon word-icon" />
      <file-excel-outlined v-if="document.fileType === 'excel'" class="icon excel-icon" />
      <file-ppt-outlined v-if="document.fileType === 'ppt'" class="icon ppt-icon" />
      <file-image-outlined v-if="document.fileType === 'image'" class="icon image-icon" />
      <file-text-outlined v-if="document.fileType === 'text'" class="icon text-icon" />
      <file-outlined v-else class="icon default-icon" />
    </div>
    
    <div class="document-info">
      <div class="document-title" :title="document.name">{{ document.name }}</div>
      <div class="document-meta">
        <div class="meta-item">
          <calendar-outlined />
          <span>{{ formatDate(document.createTime) }}</span>
        </div>
        <div class="meta-item">
          <user-outlined />
          <span>{{ document.createBy }}</span>
        </div>
      </div>
      
      <div v-if="document.remarks" class="document-remarks" :title="document.remarks">
        {{ document.remarks }}
      </div>
      
      <div class="document-category">
        <tag-outlined />
        <span>{{ getCategoryName(document.category) }}</span>
      </div>
      
      <div class="document-actions">
        <a-space>
          <a-button type="link" size="small" @click="handleView">
            <eye-outlined /> 查看
          </a-button>
          <a-button type="link" size="small" @click="handleDownload">
            <download-outlined /> 下载
          </a-button>
          <a-button type="link" size="small" @click="handleEdit">
            <edit-outlined /> 编辑
          </a-button>
          <a-button type="link" size="small" danger @click="handleDelete">
            <delete-outlined /> 删除
          </a-button>
        </a-space>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { computed } from 'vue';
import {
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileOutlined,
  CalendarOutlined,
  UserOutlined,
  TagOutlined,
  EyeOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

// 接收文档对象
const props = defineProps({
  document: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 事件
const emit = defineEmits(['view', 'download', 'edit', 'delete']);

// 处理查看文档
const handleView = () => {
  emit('view', props.document);
};

// 处理下载文档
const handleDownload = () => {
  emit('download', props.document);
};

// 处理编辑文档
const handleEdit = () => {
  emit('edit', props.document);
};

// 处理删除文档
const handleDelete = () => {
  emit('delete', props.document);
};

// 根据分类ID获取分类名称
const getCategoryName = (categoryId) => {
  const categoryMap = {
    'company_policy': '公司制度',
    'tech_doc': '技术文档',
    'training': '培训资料',
    'meeting': '会议记录',
    'other': '其他'
  };
  return categoryMap[categoryId] || categoryId;
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
.document-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
              0 3px 6px 0 rgba(0, 0, 0, 0.12), 
              0 5px 12px 4px rgba(0, 0, 0, 0.09);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
                0 3px 6px 0 rgba(0, 0, 0, 0.12), 
                0 5px 12px 4px rgba(0, 0, 0, 0.09);
  }
  
  .document-icon {
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    
    .icon {
      font-size: 48px;
      
      &.pdf-icon {
        color: #f5222d;
      }
      
      &.word-icon {
        color: #1890ff;
      }
      
      &.excel-icon {
        color: #52c41a;
      }
      
      &.ppt-icon {
        color: #fa8c16;
      }
      
      &.image-icon {
        color: #722ed1;
      }
      
      &.text-icon {
        color: #13c2c2;
      }
      
      &.default-icon {
        color: #8c8c8c;
      }
    }
  }
  
  .document-info {
    .document-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 1.4;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .document-meta {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      
      .meta-item {
        display: flex;
        align-items: center;
        
        span {
          margin-left: 4px;
        }
      }
    }
    
    .document-remarks {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 1.5;
      max-height: 36px;
    }
    
    .document-category {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      
      span {
        margin-left: 4px;
      }
    }
    
    .document-actions {
      display: flex;
      justify-content: center;
      margin-top: 8px;
      
      :deep(.ant-btn-link) {
        padding: 0 8px;
      }
    }
  }
}
</style> 