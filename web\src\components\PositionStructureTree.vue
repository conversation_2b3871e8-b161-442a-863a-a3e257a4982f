<!-- 岗位结构树组件 -->
<template>
  <div class="position-structure-tree-container">
    <h3 class="tree-title">{{ title }}</h3>
    <div v-if="loading" class="loading-container">
      <a-spin />
    </div>
    <a-tree
      v-else
      :tree-data="treeData"
      :default-expanded-keys="defaultExpandedKeys"
      @select="handleTreeSelect"
      :fieldNames="{ title: 'title', key: 'key', children: 'children' }"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import { message } from 'ant-design-vue';
import { getPositionStructureTree } from '@/api/organization/position';

const props = defineProps({
  // 树组件标题
  title: {
    type: String,
    default: '岗位结构'
  },
  // 默认展开的节点
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  },
  // 是否显示所有配置节点
  showAllNode: {
    type: Boolean,
    default: true
  },
  // 所有配置节点的标题
  allNodeTitle: {
    type: String,
    default: '全部'
  }
});

const emit = defineEmits(['select']);

// 树数据
const treeData = ref([]);
const loading = ref(false);

// 处理树节点选择
const handleTreeSelect = (selectedKeys, info) => {
  if (selectedKeys.length === 0) return;
  
  const key = selectedKeys[0];
  const node = info.node;
  
  // 转换节点数据，保持与旧组件相同的数据结构
  let nodeData = {
    ...node,
  };

  // 根据node.type和node.key的格式判断节点类型并添加必要的字段
  if (node.type === 'all') {
    nodeData = {
      ...nodeData,
      category: '',
      position: '',
      level: ''
    };
  } else if (node.type === 'category') {
    // 从node数据中获取typeId，对应岗位类型表的ID
    const typeId = node.typeId || '';
    
    nodeData = {
      ...nodeData,
      category: typeId,
      position: '',
      level: ''
    };
  } else if (node.type === 'position') {
    // 从node数据中获取必要的字段
    const typeId = node.typeId || '';
    
    nodeData = {
      ...nodeData,
      category: typeId,
      position: node.title,
      level: ''
    };
  } else if (node.type === 'level') {
    // 从node数据中获取必要的字段
    const typeId = node.typeId || '';
    const levelId = node.levelId || '';
    
    nodeData = {
      ...nodeData,
      category: typeId,
      position: node.positionName,
      level: levelId
    };
  }
  
  // 发送选中的节点数据到父组件
  emit('select', nodeData);
};

// 初始化树数据
const loadTreeData = async () => {
  loading.value = true;
  try {
    const response = await getPositionStructureTree();
    
    // 如果不显示"所有配置"节点，则过滤掉
    if (!props.showAllNode) {
      treeData.value = response.filter(node => node.key !== 'all');
    } else {
      // 如果需要自定义"所有配置"节点的标题
      if (response[0] && response[0].key === 'all') {
        response[0].title = props.allNodeTitle;
      }
      treeData.value = response;
    }
  } catch (error) {
    message.error('获取岗位结构树失败: ' + error.message);
    treeData.value = [];
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadTreeData();
});
</script>

<style scoped>
.position-structure-tree-container {
  width: 100%;
  height: 100%;
}

.tree-title {
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

/* 树节点高亮颜色 */
:deep(.ant-tree-node-selected) {
  background-color: rgba(161, 140, 209, 0.2) !important;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(161, 140, 209, 0.1) !important;
}
</style> 