import request from '@/utils/request'

// 获取企业列表
export function getEnterpriseList(params) {
  return request({
    url: '/system/enterprise/list',
    method: 'get',
    params
  })
}

// 获取企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/system/enterprise/detail/${id}`,
    method: 'get'
  })
}

// 创建企业
export function createEnterprise(data) {
  return request({
    url: '/system/enterprise',
    method: 'post',
    data
  })
}

// 更新企业
export function updateEnterprise(data) {
  return request({
    url: '/system/enterprise',
    method: 'put',
    data
  })
}

// 删除企业
export function deleteEnterprise(id) {
  return request({
    url: `/system/enterprise/${id}`,
    method: 'delete'
  })
} 