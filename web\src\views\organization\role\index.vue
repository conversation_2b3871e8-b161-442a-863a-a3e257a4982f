<template>
  <div class="page-container">
    <page-header 
    >
      <template #search>
        <search-form-card
        :model-value="form" 
        :items="formItems"
        @search="handleSearch"
        @reset="resetSearch"
      />
      </template>
      <template #actions>
        <!-- 操作按钮 -->
        <a-space>
          <a-button type="primary" class="primary-button" @click="handleAdd">
            新增角色
          </a-button>
          <a-button class="primary-button" @click="batchEnable(true)">
            批量启用
          </a-button>
          <a-button class="primary-button" @click="batchEnable(false)">
            批量禁用
          </a-button>
        </a-space>

      </template>
    </page-header>

      <!-- 表格 -->
      <base-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        @change="handleTableChange"
        row-key="id"
        :action-config="actionConfig"
        :delete-title="deleteTitle"
        @edit="handleEdit"
        @delete="handleDelete"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-badge :status="record.status ? 'success' : 'error'" :text="record.status ? '启用' : '禁用'" />
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" size="small" class="custom-button" @click="handlePermission(record)">
              <key-outlined />权限
            </a-button>
          </template>
        </template>
      </base-table>
    <!-- 新增/编辑角色弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form :model="formState" :rules="formRules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="角色名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="角色编码" name="code">
          <a-input v-model:value="formState.code" placeholder="请输入角色编码" />
        </a-form-item>
        <a-form-item label="角色类型" name="type">
          <a-select v-model:value="formState.type" placeholder="请选择角色类型">
            <a-select-option value="1">系统角色</a-select-option>
            <a-select-option value="2">自定义角色</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="权限范围" name="scope">
          <a-select v-model:value="formState.scope" placeholder="请选择权限范围">
            <a-select-option value="1">全局</a-select-option>
            <a-select-option value="2">部门</a-select-option>
            <a-select-option value="3">岗位</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formState.description" placeholder="请输入描述" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限配置弹窗 -->
    <a-modal
      v-model:visible="permissionModalVisible"
      title="权限配置"
      @ok="handlePermissionOk"
      @cancel="handlePermissionCancel"
      width="800px"
      :footer="null"
    >
      <a-tabs v-model:activeKey="activePermissionTab">
        <a-tab-pane key="menu" tab="菜单权限">
          <a-tree
            v-model:checkedKeys="checkedMenuKeys"
            :tree-data="menuTreeData"
            checkable
            :default-expand-all="true"
          />
        </a-tab-pane>
        <a-tab-pane key="data" tab="数据权限">
          <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <a-form-item label="数据范围">
              <a-radio-group v-model:value="dataScope">
                <a-radio value="1">全部数据权限</a-radio>
                <a-radio value="2">部门数据权限</a-radio>
                <a-radio value="3">部门及以下数据权限</a-radio>
                <a-radio value="4">仅本人数据权限</a-radio>
                <a-radio value="5">自定义数据权限</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="关联部门" v-if="dataScope === '5'">
              <a-tree-select
                v-model:value="selectedDepartments"
                :tree-data="departmentOptions"
                style="width: 100%"
                multiple
                tree-default-expand-all
              />
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      
      <div class="permission-footer">
        <a-button class="primary-button" @click="handlePermissionCancel">取消</a-button>
        <a-button type="primary" class="primary-button" @click="handlePermissionOk" style="margin-left: 8px;">保存</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import BaseTable from '@/components/BaseTable';
import { useTablePagination } from '@/utils/common';

// 搜索表单
const form = ref({
  name: '',
  code: '',
  type: '',
  scope: '',  
  status: '',
  description: '',
});

// 设置表格操作配置
const actionConfig = {
  edit: true,
  delete: true
};

// 删除确认标题
const deleteTitle = '确定删除此角色吗?';

const formItems = [
  { label: '角色名称', name: 'name', type: 'input' },
  { label: '角色编码', name: 'code', type: 'input' }, 
  { label: '角色类型', name: 'type', type: 'select', options: [ 
    { label: '系统角色', value: '1' },
    { label: '自定义角色', value: '2' },
  ]},
  { label: '权限范围', name: 'scope', type: 'select', options: [
    { label: '全局', value: '1' },
    { label: '部门', value: '2' },
    { label: '岗位', value: '3' },
  ]},
  { label: '状态', name: 'status', type: 'select', options: [
    { label: '启用', value: true },
    { label: '禁用', value: false },
  ]},
  { label: '描述', name: 'description', type: 'textarea' },
];


// 搜索处理
const handleSearch = (values) => {
  console.log('搜索', values);
  if (values) {
    Object.assign(form.value, values);
  }
  // 重置到第一页并加载数据
  resetPagination();
  loadData();
};

// 重置搜索
const resetSearch = (values) => {
  if (values) {
    Object.assign(form.value, values);
  } else {
    form.value = {
      name: '',
      code: '',
      type: '',
      scope: '',  
      status: '',
      description: '',
    };
  }
  // 重置分页并加载数据
  resetPagination();
  loadData();
};

// 表格列定义
const columns = [
  {
    title: '角色名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '角色编码',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '角色类型',
    dataIndex: 'type',
    key: 'type',
    customRender: ({ text }) => text === '1' ? '系统角色' : '自定义角色',
  },
  {
    title: '权限范围',
    dataIndex: 'scope',
    key: 'scope',
    customRender: ({ text }) => {
      if (text === '1') return '全局';
      if (text === '2') return '部门';
      if (text === '3') return '岗位';
      return '';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
  },
];

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '超级管理员',
    code: 'ADMIN',
    type: '1',
    scope: '1',
    status: true,
    description: '系统最高权限角色',
    createBy: 'system',
    createTime: '2023-01-01 00:00:00',
    updateTime: '2023-01-01 00:00:00',
  },
  {
    id: 2,
    name: '普通管理员',
    code: 'MANAGER',
    type: '1',
    scope: '2',
    status: true,
    description: '普通管理权限角色',
    createBy: 'admin',
    createTime: '2023-01-02 10:00:00',
    updateTime: '2023-01-02 10:00:00',
  },
  {
    id: 3,
    name: '部门主管',
    code: 'DEPT_MANAGER',
    type: '2',
    scope: '2',
    status: true,
    description: '部门管理权限角色',
    createBy: 'admin',
    createTime: '2023-01-03 10:30:00',
    updateTime: '2023-01-03 10:30:00',
  },
  {
    id: 4,
    name: '普通员工',
    code: 'EMPLOYEE',
    type: '2',
    scope: '3',
    status: true,
    description: '普通员工权限角色',
    createBy: 'admin',
    createTime: '2023-01-04 09:20:00',
    updateTime: '2023-01-04 09:20:00',
  },
  {
    id: 5,
    name: '访客',
    code: 'VISITOR',
    type: '2',
    scope: '3',
    status: false,
    description: '访客权限角色',
    createBy: 'admin',
    createTime: '2023-01-05 14:15:00',
    updateTime: '2023-01-05 14:15:00',
  },
];


// 表格数据
const dataSource = ref([]);
const loading = ref(false);

// 加载数据
const loadData = () => {
  loading.value = true;
  // 模拟请求
  setTimeout(() => {
    dataSource.value = mockData;
    // 更新分页信息
    updatePagination({
      total: mockData.length,
      current: pagination.current,
      pageSize: pagination.pageSize
    });
    loading.value = false;
  }, 500);
};

// 使用表格分页组合式函数
const { 
  pagination, 
  handleTableChange, 
  updatePagination, 
  resetPagination 
} = useTablePagination({
  fetchData: loadData,
  initialPagination: { 
    current: 1, 
    pageSize: 10, 
    total: 0,
    showTotal: (total) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
  searchForm: form
});

// 表格选择
const selectedRowKeys = ref([]);
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 批量启用/禁用
const batchEnable = (status) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }
  // 在实际应用中，这里会调用批量启用/禁用的接口
  const action = status ? '启用' : '禁用';
  message.success(`批量${action}成功`);
  
  // 更新本地数据
  dataSource.value = dataSource.value.map(item => {
    if (selectedRowKeys.value.includes(item.id)) {
      return { ...item, status };
    }
    return item;
  });
  selectedRowKeys.value = [];
  // 重新加载数据以刷新视图
  loadData();
};

// 新增/编辑表单相关
const formRef = ref(null);
const modalVisible = ref(false);
const modalTitle = ref('新增角色');
const modalType = ref('add'); // add 或 edit
const currentRecord = ref(null);

const formState = reactive({
  name: '',
  code: '',
  type: '2', // 默认自定义角色
  scope: '3', // 默认岗位
  status: true,
  description: '',
});

const formRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择角色类型', trigger: 'change' }],
  scope: [{ required: true, message: '请选择权限范围', trigger: 'change' }],
};

// 编辑角色
const handleEdit = (record) => {
  modalType.value = 'edit';
  modalTitle.value = '编辑角色';
  currentRecord.value = record;
  
  // 设置表单数据
  Object.keys(formState).forEach(key => {
    formState[key] = record[key];
  });
  
  modalVisible.value = true;
};

// 删除角色
const handleDelete = (record) => {
  // 在实际应用中，这里会添加确认删除的逻辑
  message.success(`删除角色：${record.name}`);
};

// 打开新增弹窗
const handleAdd = () => {
  modalType.value = 'add';
  modalTitle.value = '新增角色';
  currentRecord.value = null;
  
  // 重置表单数据
  formState.name = '';
  formState.code = '';
  formState.type = '2';
  formState.scope = '3';
  formState.status = true;
  formState.description = '';
  
  modalVisible.value = true;
};

// 确认弹窗
const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 在实际应用中，这里会调用保存数据的接口
    message.success(`${modalType.value === 'add' ? '新增' : '编辑'}角色成功`);
    modalVisible.value = false;
    // 重新加载数据
    loadData();
  }).catch(() => {
    // 表单验证失败
  });
};

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 权限配置相关
const permissionModalVisible = ref(false);
const activePermissionTab = ref('menu');
const currentPermissionRecord = ref(null);
const checkedMenuKeys = ref([]);
const dataScope = ref('1');
const selectedDepartments = ref([]);

// 部门选项
const departmentOptions = ref([
  {
    id: 1,
    value: 1,
    title: '餐烤餐考总部',
    children: [
      {
        id: 2,
        value: 2,
        title: '人事部',
      },
      {
        id: 3,
        value: 3,
        title: '技术部',
        children: [
          {
            id: 4,
            value: 4,
            title: '前端组',
          },
          {
            id: 5,
            value: 5,
            title: '后端组',
          }
        ]
      },
      {
        id: 6,
        value: 6,
        title: '市场部',
      }
    ]
  }
]);

// 菜单树形数据
const menuTreeData = ref([
  {
    title: '系统管理',
    key: 'system',
    children: [
      {
        title: '用户管理',
        key: 'system:user',
        children: [
          { title: '查看', key: 'system:user:list' },
          { title: '新增', key: 'system:user:add' },
          { title: '编辑', key: 'system:user:edit' },
          { title: '删除', key: 'system:user:delete' },
          { title: '导出', key: 'system:user:export' },
        ],
      },
      {
        title: '角色管理',
        key: 'system:role',
        children: [
          { title: '查看', key: 'system:role:list' },
          { title: '新增', key: 'system:role:add' },
          { title: '编辑', key: 'system:role:edit' },
          { title: '删除', key: 'system:role:delete' },
        ],
      },
      {
        title: '菜单管理',
        key: 'system:menu',
        children: [
          { title: '查看', key: 'system:menu:list' },
          { title: '新增', key: 'system:menu:add' },
          { title: '编辑', key: 'system:menu:edit' },
          { title: '删除', key: 'system:menu:delete' },
        ],
      },
    ],
  },
  {
    title: '组织管理',
    key: 'organization',
    children: [
      {
        title: '组织架构',
        key: 'organization:structure',
        children: [
          { title: '查看', key: 'organization:structure:list' },
          { title: '新增', key: 'organization:structure:add' },
          { title: '编辑', key: 'organization:structure:edit' },
          { title: '删除', key: 'organization:structure:delete' },
        ],
      },
      {
        title: '员工管理',
        key: 'organization:employee',
        children: [
          { title: '查看', key: 'organization:employee:list' },
          { title: '新增', key: 'organization:employee:add' },
          { title: '编辑', key: 'organization:employee:edit' },
          { title: '删除', key: 'organization:employee:delete' },
          { title: '导入', key: 'organization:employee:import' },
          { title: '导出', key: 'organization:employee:export' },
        ],
      },
    ],
  },
]);

// 打开权限配置弹窗
const handlePermission = (record) => {
  currentPermissionRecord.value = record;
  permissionModalVisible.value = true;
  activePermissionTab.value = 'menu';
  
  // 模拟获取权限数据
  if (record.id === 1) {
    // 超级管理员，拥有所有权限
    const allKeys = getAllKeys(menuTreeData.value);
    checkedMenuKeys.value = allKeys;
    dataScope.value = '1';
    selectedDepartments.value = [];
  } else if (record.id === 2) {
    // 普通管理员，部分权限
    checkedMenuKeys.value = ['system:user:list', 'system:role:list', 'organization:structure:list', 'organization:employee:list'];
    dataScope.value = '2';
    selectedDepartments.value = [2, 3, 6];
  } else {
    // 其他角色，更少权限
    checkedMenuKeys.value = ['organization:employee:list'];
    dataScope.value = '4';
    selectedDepartments.value = [];
  }
};

// 获取所有菜单的key
const getAllKeys = (treeData) => {
  const keys = [];
  const traverse = (nodes) => {
    nodes.forEach(node => {
      keys.push(node.key);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(treeData);
  return keys;
};

// 保存权限配置
const handlePermissionOk = () => {
  // 在实际应用中，这里会调用保存权限数据的接口
  message.success('权限配置保存成功');
  permissionModalVisible.value = false;
};

// 取消权限配置
const handlePermissionCancel = () => {
  permissionModalVisible.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 移除重复的.action-buttons和.action-button样式定义，使用全局样式 */

:deep(.ant-table-fixed-right) {
  background-color: #fff;
}
</style> 