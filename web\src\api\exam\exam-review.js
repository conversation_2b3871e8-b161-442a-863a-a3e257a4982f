import request from '@/utils/request';

/**
 * 考试审核API
 */

// 获取考试审核申请列表
export function getApplicationList(params) {
  return request({
    url: '/exam/review/applications',
    method: 'get',
    params
  });
}

// 获取考试审核申请详情
export function getApplicationDetail(id) {
  return request({
    url: `/exam/review/application/${id}`,
    method: 'get'
  });
}

// 创建考试审核申请
export function createApplication(data) {
  return request({
    url: '/exam/review/application',
    method: 'post',
    data
  });
}

// 更新考试审核申请
export function updateApplication(id, data) {
  return request({
    url: `/exam/review/application/${id}`,
    method: 'put',
    data
  });
}

// 删除考试审核申请
export function deleteApplication(id) {
  return request({
    url: `/exam/review/application/${id}`,
    method: 'delete'
  });
}

// 审核考试申请
export function reviewApplication(id, data) {
  return request({
    url: `/exam/review/application/${id}/review`,
    method: 'post',
    data
  });
}

// 添加考试成绩
export function addExamScore(applicationId, data) {
  return request({
    url: `/exam/review/score/${applicationId}`,
    method: 'post',
    data
  });
}

// 更新成绩确认状态
export function updateScoreConfirmStatus(data) {
  return request({
    url: `/exam/review/score/confirm`,
    method: 'post',
    data
  });
}

// 获取考试成绩列表
export function getExamScoreList(params) {
  return request({
    url: '/exam/review/scores',
    method: 'get',
    params
  });
}

// 获取考试统计数据
export function getExamStatistics(params) {
  return request({
    url: '/exam/review/statistics',
    method: 'get',
    params
  });
}

// 添加考试题目数据
export function addExamQuestions(applicationId, data) {
  return request({
    url: `/exam/review/questions/${applicationId}`,
    method: 'post',
    data
  });
}

// 更新考试题目数据
export function updateExamQuestions(applicationId, data) {
  return request({
    url: `/exam/review/questions/${applicationId}`,
    method: 'put',
    data
  });
}

// 获取考试题目统计信息
export function getExamQuestionsStatistics(applicationId) {
  return request({
    url: `/exam/review/questions/statistics/${applicationId}`,
    method: 'get'
  });
}

// 批量审核考试申请
export function batchReviewApplication(data) {
  return request({
    url: `/exam/review/batch/review`,
    method: 'post',
    data
  });
}

// 批量更新成绩确认状态
export function batchUpdateScoreConfirmStatus(data) {
  return request({
    url: `/exam/review/batch/score/confirm`,
    method: 'post',
    data
  });
}

// 获取考试科目选项
export function getExamSubjectOptions() {
  return request({
    url: '/knowledge-base/knowledge-options',
    method: 'get'
  });
} 