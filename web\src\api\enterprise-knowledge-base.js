import request from '@/utils/request';

// 获取企业知识库列表
export const getEnterpriseKnowledgeList = (params) => {
  return request({
    url: '/knowledge-base/list',
    method: 'get',
    params: {
      ...params,
      documentType: 'enterprise' // 固定传递企业知识库类型
    }
  });
};

// 上传文件
export const uploadFile = (data) => {
  return request({
    url: '/knowledge-base/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 批量保存文档
export const batchSaveDocuments = (data) => {
  return request({
    url: '/knowledge-base/batch-save',
    method: 'post',
    data
  });
};

// 更新知识库文档
export const updateKnowledgeDocument = (id, data) => {
  return request({
    url: `/knowledge-base/${id}`,
    method: 'put',
    data
  });
};

// 批量删除知识库文档（使用循环调用单个删除接口）
export const batchDeleteDocuments = async (ids) => {
  const promises = ids.map(id => 
    request({
      url: `/knowledge-base/${id}`,
      method: 'delete'
    })
  );
  await Promise.all(promises);
  return { code: 200, message: '批量删除成功' };
};

// 切换文档状态
export const toggleDocumentStatus = (id, status) => {
  return request({
    url: `/knowledge-base/${id}/status`,
    method: 'patch',
    data: { status }
  });
};

// 获取文档处理状态
export const getDocumentProcessStatus = (id) => {
  return request({
    url: `/knowledge-base/${id}/process-status`,
    method: 'get',
    silent: true
  });
};

// 段落相关的API
export const getDocumentParagraphs = (documentId) => {
  return request({
    url: `/knowledge-base/document/${documentId}/segments`,
    method: 'get'
  });
};

export const updateParagraph = (documentId, segmentId, data) => {
  return request({
    url: `/knowledge-base/document/${documentId}/segments/${segmentId}`,
    method: 'put',
    data
  });
}; 